<template>
    <div class="editSpeakerInfo-container">
        <div class="head-box">
            <div class="left-bre">
                <span>个人中心</span>
                <span class="middle-hr">/</span>
                <span>个人实验室</span>
            </div>
            <div v-if="isSpeaker" class="right-enter" @click="enterLaboratory">
                <span>前往实验室</span>
                <i class="el-icon-arrow-right"></i>
            </div>
        </div>
        <p class="edit-title" v-if="isSpeaker">编辑个人实验室信息</p>
        <!-- 当前登录用户是讲者 -->
        <template v-if="isSpeaker">
            <el-form :rules="rules" ref="spearkerForm" label-position="right" label-width="200px" :model="spearkerForm">
                <!-- <el-row>
                    <el-form-item label="实验室皮肤" prop="skin">
                        <div class="skin-box">
                                <div v-for="item in skinArr" class="skin-item">
                                    <img width="143" height="80" :src="item.url" alt="" srcset="">
                                    <el-radio v-model="spearkerForm.skin"  class="mt-10" :label="item.flag">{{ item.label }}</el-radio>
                                </div>
                        </div>
                    </el-form-item>
                </el-row> -->
                <el-row>
                    <el-col :span="16">
                        <el-form-item label="实验室名称" prop="name">
                            <el-input :maxlength="20" show-word-limit v-model="spearkerForm.name"></el-input>
                        </el-form-item>
                        <el-form-item label="上传头像" prop="pictureUid">
                            <div class="flex">
                                <div @click="hyMaterialFlag = true">
                                     <el-upload class="upload" action="" disabled :auto-upload="false" :show-file-list="false"
                                        :on-change="(file, fileList) => { return uploadChange(file, fileList, 1) }">
                                        <div v-if="!spearkerForm.pictureUid"
                                            class="cursor dialog-icon flex ai-center flex-col jc-center">
                                            <i class="el-icon-plus"></i>
                                            <div class="font-16">请选择封面</div>
                                        </div>
                                        <img v-else v-lazy="spearkerForm.pictureUrl" class="dialog-icon">
                                    </el-upload>
                                </div>
                                <img @click="hyMaterialFlag = true" width="92" height="92" style="border-radius: 50%;overflow: hidden;" v-if="spearkerForm.pictureUid" v-lazy="spearkerForm.pictureUrl" class="dialog-icon ml-20">
                            </div>
                            <p style="color: #999999;">建议图片尺寸比例280*370</p>
                        </el-form-item>
                        <el-form-item label="联系手机" prop="phone">
                            <el-input :maxlength="11" :max="11" v-model="spearkerForm.phone"></el-input>
                        </el-form-item>
                        <el-form-item label="微信号" prop="wechatNumber">
                            <el-input v-model="spearkerForm.wechatNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="23">
                        <el-form-item label="实验室简介" prop="intro">
                            <el-input type="textarea" :maxlength="15" rows="3" placeholder="请输入简介" v-model="spearkerForm.intro"></el-input>
                        </el-form-item>
                        <el-form-item label="实验室详情" prop="content">
                            <tinyMce :toolbar="['paste pastetext cut copy']" :height="200" class="tiny-mce-lawdetail"
                                :showToolbar="true" :readonly="false" v-model="spearkerForm.content" :showMenuBar="false">
                            </tinyMce>
                        </el-form-item>
                        <!-- <el-row>
                            <el-col class="mr-20" :span="14">
                                <el-form-item style="margin-bottom: 0;" label="感兴趣的知识领域" prop="category">
                                    <el-select style="width: 100%;" filterable v-model="spearkerForm.category" placeholder="请选择您感兴趣的知识领域">
                                        <el-option v-for="item in cateGoryList" :key="item.uid" :label="item.name"
                                            :value="item.uid">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col class="mr-20" :span="4">
                                <el-input v-model="spearkerForm.customTag" placeholder="自定义标签"></el-input>
                            </el-col>
                            <el-col :span="2">
                                <el-button style="width: 40px; height: 40px;" class="flex ai-center jc-center" icon="el-icon-plus"></el-button>
                            </el-col>
                        </el-row> -->
                        <!-- <el-row>
                            <el-col :span="24">
                                <div class="wrap-desc">
                                    <div class="tag-lists">
                                        <el-tag
                                        class="mr-10"
                                        v-for="tag in 2"
                                        :key="tag"
                                        closable
                                        type="info">
                                            <span>标签一</span>
                                        </el-tag>
                                    </div>
                                    <p class="tag-desc">最多展示8个标签</p>
                                </div>
                            </el-col>
                        </el-row> -->
                        <el-form-item label="" prop="category">
                            <el-button style="width: 120px;height: 36px;" class="flex ai-center jc-center" type="primary" @click="realSubmit">确 定</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </template>
        <el-empty
            style="margin-top: 250px;"
            v-else
            :image="require('@/assets/images/certification/empty.png')"
            description="暂无讲者信息"
        ></el-empty>
        <hyMaterial :choiceDialog="hyMaterialFlag" @updateFlag="updateFlag" :type="2" @currentObj="currentImg">
        </hyMaterial>
    </div>
</template>
<script>
export default {
    data() {
        var checkPhone = (rule, value, callback) => {
            let reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
            if (value && reg.test(value)) { 
                return callback();
            }
            if(!value) return callback();
            return callback(new Error('请输入正确的手机号'));
        };
        return {
            hyMaterialFlag: false,
            userInfo: null,
            skinArr: [
                {
                    url: 'https://cdn.wuzhiing.cn/hyfile/default-skin.png',
                    flag:'default-skin',
                    label:'默认'
                },
                {
                    url: 'https://cdn.wuzhiing.cn/hyfile/governmententerprise-skin.png',
                    flag: 'governmententerprise-skin',
                    label: '政企'
                },
                {
                    url: 'https://cdn.wuzhiing.cn/hyfile/college-skin.png',
                    flag: 'college-skin',
                    label: '学院'
                },
                {
                    url: 'https://cdn.wuzhiing.cn/hyfile/nationalcustoms-skin.png',
                    flag: 'nationalcustoms-skin',
                    label: '国风'
                },
                {
                    url: 'https://cdn.wuzhiing.cn/hyfile/scienceand%20technology-skin.png',
                    flag: 'scienceandtechnology-skin',
                    label: '科技'
                }
            ],
            rules: {
                phone: [
                    { validator:checkPhone, trigger: ['change', 'blur'] }
                ],
                wechatNumber: [
                    { required: false, message: '请输入微信号', trigger: ['change', 'blur'] }
                ],
                name: [
                    { required: true, message: '请输入我的名称', trigger: ['change', 'blur'] },
                ],
                qrCodeName: [
                    { required: false, message: '请输入二维码标题', trigger: ['change', 'blur'] },
                ],
                intro: [
                    { required: true, message: '请输入我的简介', trigger: ['change', 'blur'] },
                ],
                content: [
                    { required: true, message: '请输入我的详情', trigger: ['change', 'blur'] },
                ],
                category: [
                    { required: false, message: '请选择讲者分类', trigger: ['change', 'blur'] },
                ],
                pictureUid: [
                    { required: true, message: '请上传头像', trigger: 'blur' },
                ],
            },
            cateGoryList: [],
            spearkerForm: {
                skin:'default-skin',
                tags:[],
                customTag:'',
                name: '',
                wechatNumber: '',
                phone:'',
                qrCodeName: '',
                pictureUrl: '',
                pictureUid: '',
                qrCodeUrl: '',
                qrCodeUid: '',
                intro: '',
                content: '',
                category: '',
            }
        };
    },
    model: {
        prop: 'visibleValue',
        event: 'visibleChange'
    },
    created() {
        // 讲者分类列表
        this.getCategoryList();
    },
    async mounted() {
        // 获取个人讲者信息
        await this.$nextTick();
        await this.getInfo();
        this.initEdirorData();
    },
    computed: {
        isSpeaker({ userInfo }) { 
            return !!userInfo;
        }
    },
    methods: {
        // 选择素材
        currentImg(img) {
            if (img) {
                const { fileUrl, fileUid } = img;
                const url = fileUrl
                this.spearkerForm.pictureUrl = url;
                this.spearkerForm.pictureUid = fileUid
            }
        },
        // 关闭素材中心弹窗
        updateFlag(value) {
            this.hyMaterialFlag = value
        },
        // 前往实验室
        enterLaboratory() { 
            let { speakerUid = '' } = this.$store.state.userInfo || {};
            if (speakerUid) { 
                this.$router.push({
                    path: '/laboratoryNew/personal',
                    query: { 
                        speakerUid: speakerUid
                    }
                });
            }
        },
        async getInfo() {
            let { speakerUid='' } = this.$store.state.userInfo || {};
            await this.$request.laboratoryNew.getInfoSpeaker({
                speakerUid
            }).then(res => {
                this.userInfo = res.data.data;
            })
        },
        // 初始化编辑信息
        initEdirorData() {
            this.spearkerForm = {
                // skin: this.userInfo.skin,
                name: this.userInfo.name,
                phone: this.userInfo.phone,
                wechatNumber: this.userInfo.wechatNumber,
                // qrCodeName: this.userInfo.qrCodeName,
                pictureUrl: this.userInfo.picturePath,
                pictureUid: this.userInfo.pictureUrl,
                // qrCodeUrl: this.userInfo.qrCodePath,
                // qrCodeUid: this.userInfo.qrCodeUrl,
                intro: this.userInfo.intro,
                content: this.userInfo.content,
                // category: this.userInfo.category
            }
        },
        realSubmit() {
            let { speakerUid = '' } = this.$store.state.userInfo || {};
            this.$refs.spearkerForm.validate(async(valid) => {
                if (valid) {
                    let form = { ...this.spearkerForm };
                    // 表单
                    form.uid = speakerUid;
                    form.pictureUrl = form.pictureUid;
                    form.qrCodeUrl = form.qrCodeUid;
                    delete form.pictureUid;
                    delete form.qrCodeUid;
                    let result = await this.$request.lectureHall.editSpeakerInfo(form);
                    if (result.data.code === this.$code.SUCCESS) {
                        this.$message.success('编辑成功');
                    }
                } else {
                    return false;
                }
            });
        },
        async getCategoryList() {
            let params = {
                name: '',
                status: 1,
                type: 1,
                currentPage: 1,
                pageSize: 9999999
            }
            let result = await this.$request.lectureHall.getCategoryList(params);
            if (result.data.code === this.$code.SUCCESS) {
                this.cateGoryList = result.data.data.records;
            }
        },
        // 上传图片
        async uploadChange(file, fileList, type) {
            let limit = file.size / 1024 / 1024;
            var reg = /(.*)\.(jpg|jpeg|png)$/ig;
            if (!reg.test(file.name)) {
                this.$message({
                    type: "error",
                    message: "仅支持JPG/JPEG/PNG"
                })
                return;
            }
            //大于2M
            if (limit > 2) {
                this.$message({
                    type: "warning",
                    message: "您上传的文件超过了2M了"
                });
                return;
            }

            let formData = new FormData();
            formData.append("filedatas", file.raw);
            formData.append("userUid", "wehsbanmsbdwehqwie");
            formData.append("source", "picture");
            formData.append("sortName", "web");
            formData.append("projectName", "blog");
            let result = await this.$axios({
                url: this.$api.fileCheckFile,
                method: 'post',
                data: formData
            });
            //上传成功，图片合规
            if (result?.data.code == 200) {
                let data = result.data.data[0];
                let fullUrl = localStorage.getItem('picBaseUrl') + data.picUrl;
                // 封面
                if (type == 1) {
                    this.spearkerForm.pictureUrl = fullUrl;
                    this.spearkerForm.pictureUid = data.uid
                } else {
                    // 二维码
                    this.spearkerForm.qrCodeUrl = fullUrl;
                    this.spearkerForm.qrCodeUid = data.uid
                }
            } else if (result?.data.code == 601) {
                // 上传图片不合规

            }
        }
    }
}
</script>
<style lang="scss" scoped>
.editSpeakerInfo-container{
    padding: 20px;
    width: 980px;
    min-height: 891px;
    background: rgba(255,255,255,0.8);
    box-shadow: 0px 4px 10px 0px rgba(55,99,170,0.1);
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 2px solid #FFFFFF;
    .skin-box{
        display: flex;
        .skin-item{
            margin-right: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
    /deep/ .wrap-desc{
        margin:10px 0 30px 200px;
        .tag-lists{
            margin-bottom: 10px;
        }
        .tag-desc{
            padding: 0;
            font-size: 12px;
            font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
            font-weight: 400;
            color: #999999;
        }
    }
    .edit-title{
        margin: 8px 0 24px 0;
        font-size: 20px;
        font-family: Alibaba PuHuiTi 2.0-65 Medium, Alibaba PuHuiTi 20;
        font-weight: 600;
        color: #333333;
    }
    .head-box{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left-bre{
            font-size: 14px;
            font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
            font-weight: 400;
            color: #999999;
            .middle-hr{
                margin: 0 8px;
            }
        }
        .right-enter{
            cursor: pointer;
            font-size: 14px;
            font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
            font-weight: 400;
            color: #006EFF;
        }
    }
}
.upload {
    width: 92px;
    height: 121px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #d9d9d9;

    /deep/ .el-upload {
        height: 100%;
        display: flex;
    }

    img {
        width: 100%;
        height: 100%;
    }
}</style>