<!--V2-->
<template>
  <div class="hy-index" v-loading.fullscreen.lock="fullscreenLoading">
    <!-- 轮播图 -->
    <homeBannerV2></homeBannerV2>
    <!-- 文章分类 -->
    <!-- <searchTagV2></searchTagV2> -->
    <!-- 精选课程 -->
    <pushCourseV2 :showXiaoe="showXiaoe"></pushCourseV2>
    <div class="wrapper">
      <div class="wrapper-left">
        <!-- 精选文章 -->
        <pushArticlesV2></pushArticlesV2>
      </div>
      <div class="wrapper-right">
        <!-- 创作话题 -->
        <homeAdvertV2></homeAdvertV2>
      </div>
    </div>
    <!-- 在线直播 -->
    <onlineLiveV2 v-if="showXiaoe"></onlineLiveV2>
    <div class="row-answerAndRank">
      <!-- 排行榜 -->
      <homeRankV2></homeRankV2>
      <!-- 问答广场 -->
      <qaSquareV2></qaSquareV2>
    </div>
    <!-- 社区服务 -->
    <communityServerV2></communityServerV2>
    <!-- 联系我们 -->
    <!-- <homeContact></homeContact> -->
    <!-- <homeDialog :show="showDialog" :link="dialogInfo"></homeDialog> -->
    <div v-if="showDialog" class="enter-dialog cursor" @click="hanlderClick">
      <img style="width: 100%" :src="dialogImg" alt="" />
      <i class="close-icon el-icon-close" @click.stop="closeDialog"></i>
    </div>
    <div v-if="showDialog" class="dialog-mask"></div>
  </div>
</template>

<script>
import { TDKS } from "../plugins/tdk";
export default {
  name: "hy-index",
  head() {
    return {
      title: TDKS.home.title,
      meta: [
        {
          hid: "indexdescription",
          name: "description",
          content: TDKS.home.description,
        },
        {
          hid: "indexkeywords",
          name: "keywords",
          content: TDKS.home.keywords,
        },
      ],
    };
  },
  data() {
    return {
      fullscreenLoading: false,
      showDialog: false,
      dialogInfo: null,
      dialogImg: "",
      dialogRoute: '',
      page: {
        currentPage: 1,
        pageSize: 5,
      },
      showXiaoe: false
    };
  },
  created() {
    // console.log('path', this.$route.path, this.$route.path == '/');
    if (this.$route.path == '/') {
      this.$router.push('/lectureHall')
    }
    this.getXiaoeLiveData()
  },
  computed: {
    homeImgData() {
      return this.$store.state.homeImgData
    }
  },
  watch: {
    //监听 $store中的homeImgData，由default中的接口存入 $store后触发并显示 广告弹窗
    homeImgData: {
      handler: function (v) {
        //console.log('this.$store.state.homeImgData==============', v);
        if (v.HOME_POP_ADVERT.length) {
          this.dialogInfo = v.HOME_POP_ADVERT[0]
          this.dialogImg = v.HOME_POP_ADVERT[0].pictureUrl
          this.dialogRoute = v.HOME_POP_ADVERT[0].linkUrl
          if (!this.$store.state.homeDialogShow) {
            this.$store.commit("changeHomeDialogShow", true);
            this.showDialog = true
          }
        }
      }
    }
  },
  async mounted() {
  },
  methods: {

    // 点击弹窗
    hanlderClick() {
      this.showDialog = false;
      if (!this.dialogRoute) return
      if (this.dialogRoute.indexOf('http') != -1) {
        window.open(this.dialogRoute)
        return
      }
      this.$router.push(this.dialogRoute);
    },
    // 关闭弹窗
    closeDialog() {
      this.showDialog = false;
    },

    getXiaoeLiveData(){
      this.$request.menu.getXiaoeLiveData().then(res => {
        this.showXiaoe = res.data.data.xiaoeLiveSwitch == '1'
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.enter-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 840px;
  z-index: 99999;

  .close-icon {
    position: absolute;
    bottom: -70px;
    left: 50%;
    transform: translateX(-37%);
    font-size: 25px;
    color: #fff;
    z-index: 1000;
    border: 2px solid #fff;
    border-radius: 50%;
    padding: 4px;
  }
}

.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 99998;
  background-color: rgba(0, 0, 0, 0.5);
}

.hy-index {
  background-color: #F4F6F7;
}

.row-answerAndRank {
  display: flex;
  justify-content: space-between;
  margin: 64px auto 0 auto;
  width: 1200px;
}

.wrapper {
  display: flex;
  width: 1200px;
  margin: 0 auto;
}

.wrapper-left {
  flex: 1;
  margin-right: 20px;
}

.wrapper-right {
  width: 330px;
}
</style>
