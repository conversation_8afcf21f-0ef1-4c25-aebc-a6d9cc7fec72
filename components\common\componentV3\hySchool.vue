<template>
  <div class="hy-school" ref="hySchool" :style="boxStyle">
    <div class="head">
      <span>认证培训</span>
      <span @click="gotoCertificateHome()" style="cursor: pointer;">更多</span>
    </div>
    <div class="class-content">
      <div class="item" @click="gotoCertificateDetail(item.uid)" v-for="(item, index) in classList" :key="index">
        <div class="hover">
          <img :src="item.mainPictureUrl" alt="" srcset="" />
        </div>
        <p style="font-size: 16px">{{ item.fullName }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      classList: [
        // {
        //   url: "https://cdn.wuzhiing.cn/hyfile/%E5%AE%B9%E5%99%A8%20905%403x.png",
        //   label:
        //     "[2023K8sCKA]原生K8s管理品认证课-零基础考题更新免费学全新PSI考试系统",
        // },
        // {
        //   url: "https://cdn.wuzhiing.cn/hyfile/%E5%AE%B9%E5%99%A8%20906%403x.png",
        //   label: "[产品经理]AxureRP9产品原型设计实战课程(操作篇)",
        // },
        // {
        //   url: "https://cdn.wuzhiing.cn/hyfile/%E5%AE%B9%E5%99%A8%20907%403x.png",
        //   label: "售前解决方案工程师透彻讲解视频课程(中级)",
        // },
        // {
        //   url: "https://cdn.wuzhiing.cn/hyfile/%E5%AE%B9%E5%99%A8%20907%403x.png",
        //   label: "售前解决方案工程师透彻讲解视频课程(中级)",
        // },
      ],
      boxStyle: "",
    };
  },
  mounted() {
    window.addEventListener("resize", this.liveBroadcastBgResize);
    this.$nextTick(() => {
      this.liveBroadcastBgResize();
    });
    this.initTrainCertificatePageData()
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.liveBroadcastBgResize);
  },
  methods: {
    gotoCertificateHome() {
        this.$router.push({
        path: "/certification/home/",
      });
    },
    gotoCertificateDetail(uid) {
      this.$router.push({
        path: "/certification/certificateDetail",
        query: {
          uid: uid,
        },
      });
    },
    liveBroadcastBgResize() {
      let offsetWidth = this.$refs.hySchool.offsetWidth;
      this.boxStyle = "--review-item-width:" + (offsetWidth - 62) / 4 + "px;";
    },
    async initTrainCertificatePageData(areaUid) {
      this.loading = true;
      let params = {
        currentPage: 1,
        pageSize: 4
      };
      let result = await this.$axios({
        url: this.$api.getTrainCertificatePage,
        method: "post",
        data: params,
      });
      if (result.data.code === this.$code.SUCCESS) {
        this.total = result.data.data.total;
        this.classList = result.data.data.records;
      }
      this.loading = false;
    },
  },
};
</script>
<style lang='scss' scoped>
.hy-school {
  padding: 0 12px;
  width: 100%;
  margin-top: 50px;
  .class-content {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .item {
      flex-shrink: 0;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      width: var(--review-item-width);
      cursor: pointer;
      p {
        margin-top: 14px;
      }
      .hover {
        width: 100%;
        padding-top: 56%;
        // background: rebeccapurple;
        position: relative;
        img {
          position: absolute;
          top: 0;
          width: 100%;
          height: 100%;
          object-fit: fill;
        }
        .study-num {
          position: absolute;
          right: 20px;
          top: 15px;
          width: 81px;
          height: 24px;
          background: rgba(26, 27, 43, 0.3);
          border-radius: 20px 20px 20px 20px;
          font-size: 12px;
          font-weight: 400;
          z-index: 30;
          color: #ffffff;
          text-align: center;
          line-height: 24px;
        }
      }
    }
    .item:hover {
      img {
        transform: scale(1.1);
        z-index: 20;
      }
      p {
        color: #4290f7;
      }
    }
  }
  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span:first-of-type {
      font-size: 20px;
      font-family: Alibaba PuHuiTi 2-65 Medium, Alibaba PuHuiTi 20;
      font-weight: 600;
      color: #333333;
    }
    span:last-of-type {
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #999999;
    }
  }
}
</style>