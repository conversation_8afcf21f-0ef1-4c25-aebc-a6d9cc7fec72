<template>
  <div class="my-train">
    <!-- 头部内容 -->
    <div class="top-content w-1440">
      <div class="content py-40">
        <div class="top-tag mb-25">
          <hyBreadcrumb :breadcrumbList="breadcrumbList" :activeList="[breadcrumbList.length - 2]"></hyBreadcrumb>
        </div>
        <div class="top-detail flex">
          <div class="detail-image">
            <img v-if="trainClassDetail.fileUrl" :src="trainClassDetail.fileUrl"
              style="width:100%;height:100%;" alt="" srcset="">
            <img v-else src="@/static/images/emptyImg.jpg" alt="" srcset="">
          </div>
          <div class="flex flex-1 flex-col ml-30">
            <div class="font-28 color-black font-bold">{{ trainClassDetail.name }}</div>
            <div class="flex mt-15 color-666 font-14">
              <div class="detail-bg flex ai-center">
                <img src="~/assets/images/certification/myTraining/date-icon.png" style="width:16px;height:16px;" alt="">
                <span class="ml-5">培训时间<span class="color-main ml-10 f-w-700">{{
                  `${trainClassDetail.trainingStartTime && String(trainClassDetail.trainingStartTime).substring(0, 10)}
                                    -
                                    ${trainClassDetail.trainingEndTime && String(trainClassDetail.trainingEndTime).substring(0, 10)}`
                }}</span></span>
              </div>
              <div class="detail-bg flex ai-center">
                <img src="~/assets/images/certification/myTraining/address-icon.png" style="width:16px;height:16px;"
                  alt=""> <span class="ml-5">培训地点<span class="color-main ml-10 f-w-700">{{
                    trainClassDetail.trainingPlass
                  }}</span></span>
              </div>
              <div class="detail-bg flex ai-center">
                <img src="~/assets/images/certification/myTraining/person-info-icon.png" style="width:16px;height:16px;"
                  alt=""> <span class="ml-5">报名人数<span class="color-main ml-10 mr-5 f-w-700">{{
                    trainClassDetail.signupNum
                  }}</span>人</span>
              </div>
              <div class="detail-bg flex ai-center">
                <img src="~/assets/images/certification/myTraining/cursor-hour-icon.png" style="width:16px;height:16px;"
                  alt=""> <span class="ml-5">学习时长<span class="color-main ml-10 mr-5 f-w-700">{{
                    trainClassDetail.classHour
                  }}</span>课时</span>
              </div>
            </div>
            <div class="mt-25 flex">
              <div class="flex ai-center" v-if="trainClassDetail.sellWay == 1">
                <span class="font-14 color-666">培训费用</span>
                <span class="ml-5 font-18 font-bold" style="color:#FF564D;">
                {{ trainClassDetail.sellWay==1 ? `￥${trainClassDetail.trainingPrice}`:'免费' }}</span>
              </div>
              <div v-if="isSignUpStatus == 4" class="defaluBtn flex ml-15">
                <div style="margin:auto">培训中</div>
              </div>
              <hyButton class="ml-10"
                :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff' }"
                v-else size="small" width="120px" :disabled="(!trainClassDetail.trainingPrice && (trainClassDetail.sellWay == 1))" height="36px" :plain="true"
                @click="signStudy">{{ trainClassDetail.sellWay==1?'报名':'免费加入学习' }}</hyButton>
              <hyButton class="ml-10" size="small" width="120px" height="36px"
                v-if="isSignUpStatus == 4 && trainClassDetail.examList && trainClassDetail.examList.length > 0"
                :plain="true" @click="mockExam">模拟考试</hyButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bottom-content flex">
      <div class="bottom-left">
        <div class="choice-div">
          <div v-for="(item, index) in tabList" :key="index" class="my-create-cursor-item"
            :class="{ 'active-item': activeIndex === index }" @click="handleClick(item.component,index,item.type)">
            <img v-lazy="item.icon" alt="" style="width: 20px; height: 20px" />
            <span class="ml-5">{{ item.label }}</span>
          </div>

          <div :class="tabList.length == 3 ?'bg-div':'bg-div2'" :style="{ left: bgLeft }"></div>
        </div>
        <component :is="activeName" v-if="trainClassDetail && trainClassDetail.uid" :isSignUpStatus="isSignUpStatus"
           :type='activeType' :courseDirectoryList="trainClassDetail.trainModuleContentList"></component>
        <!-- <div>
          <el-tabs v-model="activeName" @tab-click="selectChange">
            <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.name">
              <component v-if="trainClassDetail && trainClassDetail.uid" :isSignUpStatus="isSignUpStatus"
                :type="item.type" :is="item.component" :courseDirectoryList="trainClassDetail.trainModuleContentList">
              </component>
            </el-tab-pane>
          </el-tabs>
        </div> -->
      </div>
      <div class="bottom-right flex flex-1 flex-col ml-20 p-20">
        <div class="color-black">
          推荐课程
        </div>
        <template v-if="RecommendList.length > 0">
          <div class="right-item py-20 flex" v-for="item in RecommendList" @click="gotoVideoDetail(item)" :key="item.uid">
            <div class="right-image">
              <img :src="item.coverUrl" style="width:100%;height:100%;border-radius: 4px;" alt="">
            </div>
            <div class="flex flex-1 flex-col ml-20" style="width:0;">
              <div class="color-black font-14 ellipsis" :title="item.title">{{ item.title }}</div>
              <div class="color-999 font-12 my-10">{{ String(item.createTime) }}</div>
              <div style="color:#FF564D;" class="font-14 f-w-700">{{ item.sellingPrice ? "￥" + item.sellingPrice : '免费' }}
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="empty">
            <el-empty description="暂无内容"></el-empty>
          </div>
        </template>
      </div>
    </div>
    <!-- 报名弹窗 -->
    <template>
      <signUp v-if="DialogVisible" @closed="signUpClosed" :DialogVisible="DialogVisible" :classUid="$route.query.uid">
      </signUp>
    </template>
  </div>
</template>

<script>
import signUp from './component/signUp.vue'
export default {
  provide() {
    return {
      myTrainingThis: this
    }
  },
  components: {
    signUp
  },
  data() {
    // 验证手机号
    const validateMobile = (rule, value, callback) => {
      let sqlReg = new RegExp(/select|update|delete|exec|count|'|"|=|;|>|<|%/);
      let mobileReg = new RegExp(/^0?1[3|4|5|8|7|9|6][0-9]\d{8}$/);
      if (value === "") {
        callback(new Error("请您输入正确的联系电话"));
      } else {
        if (sqlReg.test(value)) {
          callback(new Error("请不要输入特殊字符"));
        } else {
          if (mobileReg.test(value)) {
            callback();
          } else {
            callback(new Error("请您输入正确的联系电话"));
          }
        }
      }
    };
    return {
      signMap: {},
      signDetailInfo: {},
      activeName: '1',
      tabList: [
        { name: "1", type: 1, label: "培训介绍", component: 'trainingIntroduction' ,
          icon: require("@/assets/images/certification/introduce.png"),
        },
        { name: "2", type: 2, label: "课程目录", component: 'courseDirectory',
          icon: require("@/assets/images/certification/plan.png"),
         },
        { name: "3", type: 3, label: "试听课程", component: 'auditionCourse',
          icon: require("@/assets/images/certification/practice.png"),
         }
      ],
      trainClassDetail: {}, // 培训班消息
      RecommendList: [], // 推荐课程
      DialogVisible: false, // 报名弹窗
      SignUpForm: {
        typeUid: '', // 证书uid
        classUid: '', //班级uid
        contactPhone: '', // 联系电话
        contactName: '', // 联系人
        applicantName: '', // 报名人
        applicantPhone: '', // 报名电话
        signUpType: '0', // 报名方式
        checkTableUid: '', // 申请表uid
        checkTableUrl: '', //申请表url
        checkTableName: '', //申请表name
        excelUid: '', // 多人信息文件uid
        excelUrl: '', // 多人信息文件url
        excelName: '', // 多人信息文件name
        moreCheckTableUid: '', //申请表uid(多人时)
        moreCheckTableUrl: '', //申请表url(多人时)
        moreCheckTableName: '', //申请表name(多人时)
      }, // 报名表单
      SignUpRules: { // 表单规则
        classUid: [
          { required: true, message: '请选择班级', trigger: ['blur', 'change'] },
        ],
        contactPhone: [
          { required: true, message: '请输入联系电话', trigger: ['blur', 'change'] },
          { validator: validateMobile, trigger: ['blur', 'change'] }
        ],
        contactName: [
          { required: true, message: '请输入联系人', trigger: ['blur', 'change'] },
        ],
        applicantName: [
          { required: true, message: '请填写报名人', trigger: ['blur', 'change'] },
        ],
        applicantPhone: [
          { required: true, message: '请输入活动名称', trigger: ['blur', 'change'] },
        ],
        checkTableUid: [
          { required: true, message: '请上传申请表', trigger: ['blur', 'change'] },
        ],
        excelUid: [
          { required: true, message: '请报名人名单表格', trigger: ['blur', 'change'] },
        ],
        moreCheckTableUid: [
          { required: true, message: '请上传申请表', trigger: ['blur', 'change'] },
        ],
      },
      trainClassLevel1: [], // 证书列表
      trainClassLevel2: [], // 班级列表
      loading: false, // 文件上传loading
      loadingText: '文件上传中...', // loading文字说明
      isSignUpStatus: 0, // 是否在报名中
      step: false,
      originMsg: '{}',
      activeIndex:0,
      activeType:1,
      activeName:'trainingIntroduction',
      bgLeft:".5%"
    }
  },
  async mounted() {
    // debugger
    if (this.$route.query.flag) this.originMsg = this.$route.query.originMsg

    // 获取报名信息
    await this.getSignupMessage();
    const data = await this.getClassLevel()
    this.trainClassLevel1 = data
    if (this.$route.query.uid) {
      await this.getTrainClassDetail(this.$route.query.uid)
    }
    this.getRecommendCursor()
    // this.getClassLevel()
    const userInfo = this.$store.state.userInfo || null
    this.SignUpForm.applicantName = userInfo && this.formatName(userInfo.realName)
    this.SignUpForm.applicantPhone = userInfo && `${userInfo.mobile.substring(0, 3)}****${userInfo.mobile.substring(7, 11)}`
    //培训中
    if (this.isSignUpStatus == 4) { 
      this.tabList.push(
        { name: "4", type: 4, label: "直播听课", component: 'liveListening',
          icon: require("@/assets/images/certification/information.png"),
         }
      );
    }
  },
  watch: {
    'SignUpForm.signUpType': {
      handler: function (val) {
        if (val) {
          if (this.getCookie(this.$route.query.uid)) {
            return
          } else {
            if (val == 0) {
              this.SignUpForm.moreCheckTableName = ''
              this.SignUpForm.moreCheckTableUid = ''
              this.SignUpForm.moreCheckTableUrl = ''
              this.SignUpForm.excelUid = ''
              this.SignUpForm.excelUrl = ''
              this.SignUpForm.excelName = ''
            } else if (val == 1) {
              this.SignUpForm.checkTableName = ''
              this.SignUpForm.checkTableUid = ''
              this.SignUpForm.checkTableUrl = ''
            }
          }
        }
      },
    }
  },
  computed: {
    breadcrumbList() {
      return [...this.$store.state.courseBreadcrumb, { label: this.trainClassDetail?.name }]
    }
  },
  methods: {
    gotoVideoDetail(item) {
      // 免费
      if (!item.sellingPrice) {
        this.$router.push({
          path: '/certification/live',
          query: {
            chapterUid: item.uid,
            ht: 1,
            se: 1
          }
        })
      }
    },
    signUpClosed() {
      this.DialogVisible = false;
    },
    // 模拟考试
    mockExam() {
      this.$router.push({
        path: "/certification/trainExam",
        query: { uid: this.$route.query.uid },
      });
    },
    async goToPay() {
      this.$store.commit('setCourseBreadcrumb', [{ label: '认证培训' }, { label: '在线培训' }]);
      // 防抖
      this.$debounce(async () => {
        // 跳转到支付界面
        this.$router.push({
          path: "/certification/pay",
          query: { suid: this.signDetailInfo.uid },
        });
      })();
    },
    // 获取培训班详情
    async getTrainClassDetail(uid) {
      await this.$axios({
        url: this.$api.trainClassInfo,
        method: 'get',
        params: { uid }
      }).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
          let reg = /练习班/ig;
          const data = res.data.data
          this.trainClassDetail = JSON.parse(JSON.stringify(data));
          if (reg.test(this.trainClassDetail.name)) {
            this.tabList[1].label = '在线练习';
          }
          this.SignUpForm.typeUid = data.certificateManageMsg && data.certificateManageMsg.uid
          const Arr = [...this.trainClassLevel1]
          const current = Arr.filter((item) => item.uid == data.certificateUid)[0]
          this.trainClassLevel2 = current && current.classList
          this.SignUpForm.classUid = uid
          this.isSignUpStatus = data.registerStatus
          this.$store.commit('setTrainClassDetail', data)

          if (!this.step) {
            if (this.$route.query.flag==='true' && data.registerStatus != 4) {
              this.openSignUp()
              this.step = true
            }
          }
        }
      })
    },
    // 获取推荐课程
    getRecommendCursor() {
      const data = { limit: 6 }
      this.$axios({
        url: this.$api.getRecommendClass,
        method: 'post',
        data
      }).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
          this.RecommendList = res.data.data
        }
      })
    },
    // 获取证书培训班分级列表
    getClassLevel() {
      return new Promise((resolve, reject) => {
        this.$axios({
          url: this.$api.getClassCertificateList,
          method: 'get'
        }).then((res) => {
          if (res.data.code == this.$code.SUCCESS) {
            resolve(res.data.data)
          }
        })
      })
    },
    typeChange(val) {
      this.SignUpForm.classUid = ''
      const Arr = [...this.trainClassLevel1]
      const current = Arr.filter((item) => item.uid == val)[0]
      this.trainClassLevel2 = current && current.classList
    },
    async getSignupMessage() {
      let params = {
        classUid: this.$route.query.uid
      }
      let result = await this.$axios({
        url: this.$api.getSignupMessage,
        method: 'get',
        params
      });
      if (result?.data.code === this.$code.SUCCESS) {
        this.signDetailInfo = result.data.data ? result.data.data : {};
      }
      //console.log("getSignupMessage", result.data.data);
    },
    signStudy() { 
      // 培训中
      if (this.isSignUpStatus == 4) return;
      // 免费
      if (this.trainClassDetail.sellWay == 0) {
        this.$alert('是否加入此培训班', '温馨提示', {
          confirmButtonText: '确定',
          callback: async (action) => {
            if (action === 'confirm') { 
              let result = await this.$request.combatCourse.freeJoin({
                uid: this.$route.query.uid
              });
              if (result.data.code == this.$code.SUCCESS) {
                // 培训中
                this.isSignUpStatus = 4;
                this.$message.success('加入成功');
              }
            }
          }
        });
        return;
      } else {
        if (this.isSignUpStatus != 0) {
            this.openSignUp();
          return
        }
        // 判断当前时间是否大于 报名时间开始 大于的话允许 进入判断
        if (new Date().valueOf() > new Date(this.trainClassDetail.registrationStartTime).valueOf()) {
          // 判断当前时间是否 大于 报名结束时间 大于 不允许报名
          if (new Date().valueOf() > new Date(this.trainClassDetail.registrationEndTime).valueOf()) {
            this.$message.warning('报名时间当前已结束')
          } else {
            this.openSignUp();
          }
        } else {
          this.$message.warning('报名时间当前未开启')
        }
      }
    },
    // 报名按钮
    async openSignUp() {
      // 培训中
      if (this.isSignUpStatus == 4) return;
      if (this.isSignUpStatus == 5){
        this.$message.warning('培训已结束')
        return;
      } 

      if (this.getCookie(this.$route.query.uid)) {
        this.SignUpForm = JSON.parse(this.getCookie(this.$route.query.uid))
      }
      this.DialogVisible = true
    },
    // 关闭弹窗
    closeDialog() {
      this.DialogVisible = false
      this.SignUpForm.contactName = ''
      this.SignUpForm.contactPhone = ''
      this.SignUpForm.checkTableUid = ''
      this.SignUpForm.checkTableName = ''
      this.SignUpForm.checkTableUrl = ''
      this.SignUpForm.excelUid = ''
      this.SignUpForm.excelUrl = ''
      this.SignUpForm.excelName = ''
      this.SignUpForm.moreCheckTableUid = ''
      this.SignUpForm.moreCheckTableUrl = ''
      this.SignUpForm.moreCheckTableName = ''
    },
    handleClick(name, index,type) {
      this.activeIndex = index;
      this.activeName = name;
      this.activeType = type
      let baseLet
      if (this.tabList.length == 4) {
        baseLet = index * 25;
      } else {
        baseLet = index * 33.5
      }
      this.bgLeft = (baseLet = index ? baseLet - 0.5 : baseLet + 0.5) + "%";
      this.selectChange()
    },
    selectChange() {
      // 节流
      this.$throttle(() => {
        if (this.$route.query.uid) {
          this.getTrainClassDetail(this.$route.query.uid)
        }
      })();
    },
    uploadClick(type) {
      this.$refs[`my-upload-${type}`].click()
    },
    // 单人模板上传
    presonFileChange(e) {
      if (!e) return
      const file = e.target.files[0]
      const suffix = String(file.name).substring(String(file.name).lastIndexOf('.') + 1)
      if (['doc', 'docx'].includes(suffix)) {
        this.fileUploadFun(file, 'person')
      } else {
        this.$message.warning('上传文件格式错误，支持doc,docx格式！')
      }
    },
    // 多人模板上传
    multFileChange(e) {
      if (!e) return
      const file = e.target.files[0]
      const suffix = String(file.name).substring(String(file.name).lastIndexOf('.') + 1)
      if (['rar', 'zip'].includes(suffix)) {
        this.fileUploadFun(file, 'mult')
      } else {
        this.$message.warning('上传文件格式错误，支持rar,zip格式！')
      }
    },
    // 多人表格上传
    excelFileChange(e) {
      if (!e) return
      const file = e.target.files[0]
      const suffix = String(file.name).substring(String(file.name).lastIndexOf('.') + 1)
      if (['xls', 'xlsx'].includes(suffix)) {
        this.fileUploadFun(file, 'xls')
      } else {
        this.$message.warning('上传文件格式错误，支持xls,xlsx格式！')
      }
    },
    // 上传函数
    fileUploadFun(file, type) {
      this.loading = true
      let formData = new FormData();
      formData.append("file", file);
      formData.append("userUid", "wehsbanmsbdwehqwie");
      formData.append("source", "picture");
      formData.append("sortName", "web");
      formData.append("projectName", "blog");
      this.$axios({
        url: this.$api.chatPicture,
        method: 'post',
        data: formData
      }).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
          const data = res.data.data[0]
          if (type == 'person') {
            this.SignUpForm.checkTableName = file.name
            this.SignUpForm.checkTableUid = data.uid
            this.SignUpForm.checkTableUrl = data.picUrl
          } else if (type == 'mult') {
            this.SignUpForm.moreCheckTableName = file.name
            this.SignUpForm.moreCheckTableUid = data.uid
            this.SignUpForm.moreCheckTableUrl = data.picUrl
          } else if (type == 'xls') {
            this.SignUpForm.excelUid = data.uid
            this.SignUpForm.excelUrl = data.picUrl
            this.SignUpForm.excelName = file.name
          }
          this.loading = false
        }
      }).finally(() => {
        this.loading = false
      })
    },
    cancelTableUid(type) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (type == 'person') {
          this.SignUpForm.checkTableUid = ''
          this.SignUpForm.checkTableName = ''
        } else if (type == 'mult') {
          this.SignUpForm.moreCheckTableName = ''
          this.SignUpForm.moreCheckTableUid = ''
        } else if (type == 'xls') {
          this.SignUpForm.excelUid = ''
          this.SignUpForm.excelName = ''
        }

      }).catch(() => {
        return
      });
    },
    // 下载模板
    tempDownload(type, url) {
      if (url) {
        const a = document.createElement('a')
        //多人报名
        if (type == 2) {
          a.href = localStorage.getItem('picBaseUrl') + url;
        } else {
          a.href = url;
        }
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      } else {
        this.$message.warning('暂无文件！')
      }
    },
    //格式化名字
    formatName(name) {
      if (!name) return
      let newStr;
      if (name.length === 2) {
        newStr = name.substr(0, 1) + "*";
      } else if (name.length > 2) {
        let char = "";
        for (let i = 0, len = name.length - 2; i < len; i++) {
          char += "*";
        }
        newStr = name.substr(0, 1) + char + name.substr(-1, 1);
      } else {
        newStr = name;
      }
      return newStr;
    },
    setCookie(cname, cvalue, exdays) {
      var d = new Date();
      d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
      var expires = "expires=" + d.toGMTString();
      document.cookie = cname + "=" + cvalue + "; " + expires;
    },
    getCookie(cname) {
      var name = cname + "=";
      var ca = document.cookie.split(';');
      for (var i = 0; i < ca.length; i++) {
        var c = ca[i].trim();
        if (c.indexOf(name) == 0) { return c.substring(name.length, c.length); }
      }
      return "";
    },
    // 保存操作
    save() {
      const uid = this.$route.query.uid || ''
      const data = JSON.parse(JSON.stringify(this.SignUpForm))
      if (uid) {
        this.setCookie(uid, JSON.stringify(data), 3)
        this.$message.success('保存成功，数据保留时间72小时')
        this.DialogVisible = false
      } else {
        this.DialogVisible = false
      }
    },
    // 重新提交
    resubmit() {
      this.isSignUpStatus = 0
    },
    // 提交
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.SignUpForm))
          //console.log('datra', data)
          delete data.typeUid
          if (data.signUpType == 0) {
            delete data.moreCheckTableName
            delete data.moreCheckTableUid
            delete data.moreCheckTableUrl
            data.excelUid = ''
            data.excelName = ''
            data.excelUrl = ''
            this.subFun(data)
          } else if (data.signUpType == 1) {
            data.checkTableName = data.moreCheckTableName
            data.checkTableUid = data.moreCheckTableUid
            data.checkTableUrl = data.moreCheckTableUrl
            delete data.moreCheckTableName
            delete data.moreCheckTableUid
            delete data.moreCheckTableUrl
            this.subFun(data)
          }
        }
      })
    },
    subFun(data) {
      this.loading = true
      this.loadingText = '报名中...'
      this.$axios({
        url: this.$api.trainingSignUp,
        method: 'post',
        data
      }).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
          this.isSignUpStatus = 1
          this.loading = false
        }
      }).catch(err => {
        this.$message.warning(err.message);
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/.el-tabs__active-bar {
  height: 1px !important;
}

/deep/ .el-tabs__nav-wrap::after {
  height: 1px !important;
  background: #E6EAED;
}

/deep/ .el-tabs__header {
  margin: 0px !important;
}

/deep/.el-tabs__item.is-active {
  color: $colorMain;
  font-size: $fontC;
}

/deep/ .el-tabs__item {
  font-size: $fontC;
  color: $color999;
}

/deep/ .el-tabs__item:hover {
  color: $colorMain;
}

.my-train {
  .top-content {
    // height: 280px;
    margin: 20px auto 0;
    background: url("@/assets/certification/title_bk.png") no-repeat;
      // background-size: 100% 100%;
      background-size: 102.5% 114%;
      background-position: -18px -10px;

    .content {
      height: inherit;
      margin: 0px auto;
      padding: 16px 20px;
      .top-tag {
        .el-breadcrumb {
          /deep/ .el-breadcrumb__item {
            .el-breadcrumb__inner {
              font-size: 14px;
              font-weight: normal;
              color: #999999 !important;
            }
          }
        }
      }

      .top-detail {
        .detail-image {
          width: 200px;
          height: 150px;
          border-radius: 4px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .detail-bg {
          margin-left: 20px;
          padding: 5px 16px;
          border-radius: 4px;
          background: #F5F8FC;
        }

        .detail-bg:first-child {
          margin-left: 0px;
        }
      }
    }
  }

  .bottom-content {
    width: 1440px;
    min-width: 1440px;
    margin: 30px auto;

    .bottom-left {
      padding: 20px 32px;
      width: 1080px;
      min-height: 840px;
      box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.051);
      border-radius: 4px;
      // background: $colorWhite;
      background: url("@/assets/images/certification/body_bk.png") no-repeat;
      background-size: 100% 100%;
      background-size: 103% 105%;
      background-position: -12px -16px;
      flex-shrink: 0;
    }

    .bottom-right {
      width: 0;
      height: 840px;
      box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.051);
      border-radius: 4px;
      background: $colorWhite;

      .right-item {
        border-bottom: 1px solid #E6EAED;

        .right-image {
          flex-shrink: 0;
          width: 120px;
          height: 90px;
        }
      }

      .right-item:last-child {
        border-bottom: none;
      }
    }
  }

  .training-el-dialog {
    /deep/ .el-dialog {
      height: 660px;
      display: flex;
      flex-direction: column;
      top: 50%;
      transform: translateY(-50%);

      .signup-ing {
        width: 320px;
        height: 105px;
      }

      .el-dialog__header {
        padding: 0px;
      }

      .el-dialog__body {
        display: flex;
        flex: 1;
        padding: 0px 30px;
        color: $color999;

        .demo-SignUpForm {
          width: 100%;
          display: flex;
          flex-direction: column;

          .grid-form {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 20px;

            /deep/ .el-form-item {

              /deep/ .el-select {
                width: 100%;
              }
            }
          }

          .line-height-item {
            .el-form-item__content {
              line-height: 16px !important;
            }
          }

          .file-status {
            .cancel-status {
              display: none;
            }
          }

          .file-status:hover {
            .success-status {
              display: none;
            }

            .cancel-status {
              display: inline-block;
            }
          }

          .file-div {
            height: 28px;
            border-radius: 4px;
            border: 1px solid $colorMain;
          }
        }
      }

      .el-dialog__footer {
        padding: 0px 32px 0px 0px;
        height: 84px;
        display: flex;
      }

    }
  }
}
.choice-div {
    width: 100%;
    padding: 4px;
    background: #F5F8FC;
    box-shadow: inset 0px 2px 5px 0px rgba(55, 99, 170, 0.05), inset 0px -2px 5px 0px rgba(55, 99, 170, 0.05);
    border-radius: 4px;
    display: flex;
    height: 36px;
    position: relative;
    font-size: 14px;
  }

  .my-create-cursor-item {
    display: flex;
    width: 33.33%;
    align-items: center;
    justify-content: center;
    z-index: 1;
    color: #666;
    cursor: pointer;
  }

  .active-item {
    color: $colorMain;
  }

  .bg-div {
    position: absolute;
    width: 33.33%;
    background: #FFFFFF;
    box-shadow: 0px 2px 5px 0px rgba(55, 99, 170, 0.1);
    height: 85%;
    top: 50%;
    border-radius: 4px;
    z-index: 0;
    transform: translateY(-50%);
    transition: left 0.3s ease;
  }
  .bg-div2{
    position: absolute;
    background: #FFFFFF;
    box-shadow: 0px 2px 5px 0px rgba(55, 99, 170, 0.1);
    height: 85%;
    top: 50%;
    border-radius: 4px;
    z-index: 0;
    transform: translateY(-50%);
    transition: left 0.3s ease;
    width: 25%;
  }
</style>

<style>
.el-form-item__label {
  color: #666 !important;
}

.el-select {
  width: 100%;
}

.defaluBtn {
  background: #F5F8FC;
  width: 120px;
  height: 36px;
  font-size: 14px;
  font-family: 'AlibabaPuHuiTi-2-55';
  border-radius: 3px;
  padding: auto;
}

</style>
