<template>
    <el-card class="createdLive">
        <div class="body">
            <div class="flex ai-center jc-between">
                <p class="font20 f-w-bold">{{ pageTitle }}</p>
                <div class="flex-center back-button font14 f-w-400 cursor" @click="backPage">返回</div>
            </div>
            <div class="mt-20">
                <el-form :model="form" :rules="rule" label-width="120px" ref="liveForm">
                    <el-form-item label="上传直播封面" prop="logoUrl">
                        <div class="choice-img-area font-12 color-999">
                            <div class="img-div flex-col jc-center ai-center" @click="openChoice(1)">
                                <template v-if="!outlogoUrl">
                                    <i class="iconfont icon-add1 color-main"></i>
                                    <span style="color: rgba(0,0,0,0.4);">上传照片</span>
                                </template>
                                <template v-else>
                                    <img v-lazy="outlogoUrl" alt=""
                                        style="width: 100%;height: 100%;object-fit: cover;">
                                </template>
                            </div>
                        </div>
                        <span class="title-c-3 font14">上传直播封面，建议图片尺寸比例320*180</span>
                    </el-form-item>
                    <el-form-item label="上传二维码图" prop="qrCodeUrl">
                        <div class="choice-img-area font-12 color-999">
                            <div class="img-div flex-col jc-center ai-center" @click="openChoice(2)">
                                <template v-if="!outqrCodeUrl">
                                    <i class="iconfont icon-add1 color-main"></i>
                                    <span style="color: rgba(0,0,0,0.4);">上传照片</span>
                                </template>
                                <template v-else>
                                    <img v-lazy="outqrCodeUrl" alt=""
                                        style="width: 100%;height: 100%;object-fit: cover;">
                                </template>
                            </div>
                        </div>
                        <span class="title-c-3 font14">上传二维码图，建议图片尺寸比例260*160</span>
                    </el-form-item>
                    <el-form-item label="上传直播海报" prop="livePosterUrl">
                        <div class="choice-img-area font-12 color-999">
                            <div class="img-div flex-col jc-center ai-center" @click="openChoice(3)">
                                <template v-if="!outlivePosterUrl">
                                    <i class="iconfont icon-add1 color-main"></i>
                                    <span style="color: rgba(0,0,0,0.4);">上传照片</span>
                                </template>
                                <template v-else>
                                    <img v-lazy="outlivePosterUrl" alt=""
                                        style="width: 100%;height: 100%;object-fit: cover;">
                                </template>
                            </div>
                        </div>
                        <span class="title-c-3 font14">上传直播海报，建议图片宽度820*1080</span>
                    </el-form-item>
                    <el-form-item label="直播标题" prop="name">
                        <el-input v-model="form.name" style="width: 360px;"></el-input>
                    </el-form-item>
                    <el-form-item label="直播链接">
                        <el-input v-model="form.liveAddress" style="width: 360px;"></el-input>
                    </el-form-item>
                    <el-form-item label="开播时间" prop="date">
                        <el-date-picker v-model="date" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="dateChange"
                            :picker-options="pickerOptions" style="width: 360px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="选择主题" prop="category">
                        <el-select v-model="form.category" placeholder="请选择主题" style="width: 226px;">
                            <el-option v-for="item in themeList" :key="item.uid" :label="item.name" :value="item.uid">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="直播简介" prop="intro">
                        <el-input v-model="form.intro" type="textarea" maxlength="100" show-word-limit
                            resize="none"></el-input>
                    </el-form-item>
                    <el-form-item label="直播详情" prop="content">
                        <wangEditor height="500px" :key="timestamp" v-model="form.content"></wangEditor>
                    </el-form-item>
                </el-form>
                <el-button class="mt-25 submit flex-center" style="margin-left: 120px;" @click="validateForm">确定</el-button>
            </div>
        </div>
        <choiceImgorVideo :choiceDialog="choiceFlag" @updateFlag="updateFlag" :type="2" @currentObj="currentImg">
        </choiceImgorVideo>
    </el-card>
</template>

<script>
import choiceImgorVideo from './myCurriculum/components/choiceImgorVideo.vue'
import omit from 'lodash/omit'
import pick from 'lodash/pick'
import cloneDeep from 'lodash/cloneDeep'
export default {
    name: '',
    components: {
        choiceImgorVideo
    },
    data() {
        const checkDate = (relus, value, callback) => {
            if (this.form.endTime && this.form.startTime) {
                callback()
            } else {
                callback(new Error('请选择开播时间'))
            }
        }

        const validateContent = (rules, value, callback) => {
            if (delHtml(value)) {
                callback()
            } else {
                const imgStrs = value.match(/<img.*?>/g)
                if (imgStrs && imgStrs.length) { // 内容只有图片时
                    callback()
                } else {
                    callback(new Error('请填写直播详情'))
                }
            }

            // 过滤html代码、空格、回车 空白字符
            function delHtml(str) {
                str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
                str = str.replace(/[\r\n]/g, '')
                str = str.replace(/\s/g, '')
                str = str.replace(/&nbsp;/ig, '')
                return str
            }
        }
        return {
            choiceType: 1,//1封面 2二维码 3海报
            choiceFlag: false,
            outlogoUrl: '',
            outqrCodeUrl: '',
            outlivePosterUrl: '',
            form: {
                logoUrl: '', // 直播封面uid
                qrCodeUrl: '', // 直播二维码图ID
                livePosterUrl: '', // 直播海报图ID
                name: '', // 直播标题
                intro: '', // 直播简介
                content: '', // 直播详情
                liveAddress: '', // 直播链接
                category: '', // 直播主题
                startTime: '', // 直播开始时间
                endTime: '', // 直播结束时间
            },
            rule: {
                logoUrl: [{ required: true, message: '请上传直播封面', trigger: ['change', 'blur'] }],
                qrCodeUrl: [{ required: true, message: '请上传二维码图', trigger: ['change', 'blur'] }],
                livePosterUrl: [{ required: true, message: '请上传直播海报', trigger: ['change', 'blur'] }],
                name: [{ required: true, message: '请填写直播标题', trigger: ['change', 'blur'] }],
                date: [{ required: true, validator: checkDate, trigger: ['change', 'blur'] }],
                category: [{ required: true, message: '请选择直播主题', trigger: ['change', 'blur'] }],
                intro: [{ required: true, message: '请填写直播简介', trigger: ['change', 'blur'] }],
                content: [{ required: true, validator: validateContent, trigger: ['change', 'blur'] },],
            },
            themeList: [],//主题选项
            date: [],
            timestamp: '',
            pickerOptions: {
                disabledDate(time) {
                    // 获取当前时间
                    const now = new Date();
                    // 将当前时间增加1小时
                    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
                    // 返回是否禁用该日期
                    // return time.getTime() < oneHourLater.getTime();
                },
            },
            pageTitle: '创建直播', // 页面标题
        }
    },
    computed: {

    },
    watch: {
        'date': {
            handler: function (val) {
                this.form.startTime = val ? val[0] : null;
                this.form.endTime = val ? val[1] : null;
            },
            deep: true
        }
    },
    mounted() {
        this.getThemeOptions()
        if (this.$route.query.liveUid) {
            this.pageTitle = this.$route.query.type == 'edit' ? '编辑直播' : '创建直播'
            this.getLiveInfo(this.$route.query.liveUid)
        }
    },
    methods: {
        //获取主题选项
        async getThemeOptions() {
            const params = {
                type: 3,
                currentPage: 1,
                pageSize: 9999999
            }
            let result = await this.$request.lectureHall.getCategoryList(params);
            if (result.data.code === this.$code.SUCCESS) {
                this.themeList = result.data.data.records;
            }
        },
        //表单校验
        validateForm() {
            this.$refs['liveForm'].validate((valid) => {
                if (valid) {
                    this.$debounce(() => {
                        this.submitForm()
                    }, 1000)();
                } else {

                }
            });
        },
        //提交表单
        submitForm() {
            const params = cloneDeep(this.form)
            const flag = this.$route.query.type == 'edit'
            const subFun = flag ? 'updateMyCreateLiveInfo' : 'myCreateLive'
            if (flag) params.uid = this.$route.query.liveUid
            this.$request.myCourse[subFun](params).then(res => {
                if (res.data.code === 200) {
                    this.$message({
                        message: `${flag ? '编辑' : '创建'}直播成功~`,
                        type: 'success',
                        duration: 1000
                    });
                    setTimeout(() => {
                        this.backPage()
                    }, 500);
                }
            })
        },
        // 获取直播课信息
        getLiveInfo(liveUid) {
            this.$request.myCourse.getCreateLiveInfo({ liveUid }).then((res) => {
                if (res.data.code == 200) {
                    const type = this.$route.query.type
                    const params = pick(res.data.data, ['category', 'logoUrl', 'qrCodeUrl', 'livePosterUrl', 'name', 'intro', 'content', 'liveAddress'])
                    const extendParams = pick(res.data.data, ['startTime', 'endTime', 'logoPath', 'livePosterPath', 'qrCodePath'])
                    this.form = cloneDeep(params)
                    if (type == 'edit') {
                        this.date = [extendParams.startTime, extendParams.endTime]
                    }
                    this.outlogoUrl = extendParams.logoPath
                    this.outqrCodeUrl = extendParams.qrCodePath
                    this.outlivePosterUrl = extendParams.livePosterPath
                    this.timestamp = new Date().getTime()
                }
            })
        },
        //选择日期
        dateChange(e) {

        },
        updateFlag(value) {
            this.choiceFlag = value
        },
        getBaseUrl() {
            return (localStorage.getItem('picBaseUrl'))
        },
        // 选中的当前图片
        currentImg(img) {
            const validateImg = (str) => {
                this.$refs['liveForm'].clearValidate(str)
            }
            if (img) {
                const { fileUrl, fileUid } = img
                if (this.choiceType == 1) {
                    this.form.logoUrl = fileUid
                    this.outlogoUrl = fileUrl
                    validateImg('logoUrl')
                }
                if (this.choiceType == 2) {
                    this.form.qrCodeUrl = fileUid
                    this.outqrCodeUrl = fileUrl
                    validateImg('qrCodeUrl')
                }
                if (this.choiceType == 3) {
                    this.form.livePosterUrl = fileUid
                    this.outlivePosterUrl = fileUrl
                    validateImg('livePosterUrl')
                }
            }
        },
        // 打开素材中心弹窗
        openChoice(num) {
            this.choiceType = num
            this.choiceFlag = true
        },
        // 返回上一页
        backPage() {
            this.$router.go(-1);
        },
    },
}
</script>

<style lang="scss" scoped>
.submit {
    width: 120px;
    height: 36px;
    background: linear-gradient(90deg, #338BFF 1%, #006EFF 100%);
    border-radius: 4px;
    color: #fff;
}

.back-button {
    width: 64px;
    height: 32px;
    border-radius: 4px;
    background: #EBF0F7;
    color: #006EFF;
}

.createdLive {
    width: 980px;
    min-height: 980px;

    .choice-img-area {
        .img-div {
            width: 150px;
            height: 150px;
            border: 1px dashed #D4DBE0;
            box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
            border-radius: 4px;
            line-height: 18px;
            cursor: pointer;
        }

        .img-div:hover {
            border-color: $colorMain;
        }
    }
}
</style>