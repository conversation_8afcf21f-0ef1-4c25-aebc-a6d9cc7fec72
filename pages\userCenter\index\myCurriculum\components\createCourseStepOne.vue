<!-- 创建单品课程第一步组件 -->
<template>
    <div class="container-one">
        <div class="container">
            <el-form :model="courseForm" :rules="rules" ref="courseForm" label-width="110px" class="demo-courseForm">
                <el-form-item label="视频课程名称" prop="name" style="width: 80%;" class="courseForm-input">
                    <el-input style="width: 56%;" v-model="courseForm.name" placheolder="请输入视频课程名称" maxlength="30"
                        show-word-limit></el-input> <span class="color-999 font-12">支持中英文、特殊字符，限制30个字符以内</span>
                </el-form-item>
                <el-form-item label="知识话题" prop="sortUid" style="width: 50%;">
                    <el-cascader style="width: 100%;" v-model="courseForm.sortUid" :options="knowledgeList"
                        @change="handleChange" :props="{
                            value: 'uid',
                            label: 'sortName',
                            children: 'blogSortList'
                        }"></el-cascader>
                </el-form-item>

                <el-form-item label="封面图" prop="pictureUid" style="width: 50%;">
                    <div class="choice-img-area font-12 color-999">
                        <div class="img-div flex-col jc-center ai-center" @click="openChoice">
                            <template v-if="!courseForm.pictureUrl">
                                <i class="iconfont icon-add1 color-main"></i>
                                <span style="color: rgba(0,0,0,0.4);">选择图片</span>
                            </template>
                            <template v-else>
                                <img v-lazy="courseForm.pictureUrl" alt=""
                                    style="width: 100%;height: 100%;object-fit: fill;">
                            </template>
                        </div>
                        <span class="color-999 font-14 mt-10">建议尺寸比例600px * 600px</span>
                    </div>
                </el-form-item>

                <el-form-item label="课程简介" prop="description">
                    <el-input placheolder="请输入课程简介" type="textarea" v-model="courseForm.description" resize="none"
                        maxlength="150" show-word-limit :autosize="{ maxRows: 5, minRows: 5 }"></el-input>
                </el-form-item>

                <el-form-item label="课程详情" prop="intro">
                    <wangEditor height="500px" :key="timestamp" v-model="courseForm.intro"></wangEditor>
                </el-form-item>

                <el-form-item prop="sellWay">
                    <template slot="label">
                        <span class="color-666">售卖方式</span>
                    </template>
                    <template>
                        <div class="color-666 font-14 flex-col" style="margin-top: 13px;">
                            <el-radio-group v-model="courseForm.sellWay">
                                <el-radio :label="0">免费</el-radio>
                                <el-radio :label="1">付费</el-radio>
                                <el-radio :label="2">加密</el-radio>
                                <el-radio :label="3">指定学员</el-radio>
                            </el-radio-group>
                            <template v-if="courseForm.sellWay == 0">

                            </template>

                            <template v-if="courseForm.sellWay == 1">
                                <div class="mt-20">
                                    <el-form-item prop="discountPrice">
                                        <template slot="label">
                                            <span class="color-666">商品价格</span>
                                        </template>
                                        <template>
                                            <el-input placeholder="请输入课程优惠后价格" v-model.trim="courseForm.discountPrice"
                                                style="width: 200px;">
                                            </el-input><span class="ml-5">元</span>
                                        </template>
                                    </el-form-item>

                                    <el-form-item prop="sellingPrice" style="margin-top: 25px;">
                                        <template slot="label">
                                            <span class="color-666">划线价格</span>
                                        </template>
                                        <template>
                                            <el-input placeholder="请输入课程原价格" v-model.trim="courseForm.sellingPrice"
                                                :disabled="!courseForm.discountPrice" style="width: 200px;">
                                            </el-input><span class="ml-5">元</span>
                                        </template>
                                    </el-form-item>
                                </div>
                            </template>

                            <template v-if="courseForm.sellWay == 2">
                                <div class="mt-20">
                                    <el-form-item prop="password">
                                        <template slot="label">
                                            <span class="color-666">密码设置</span>
                                        </template>
                                        <template>
                                            <el-input placeholder="请设置3-12位数字，字母或汉字" v-model.trim="courseForm.password"
                                                style="width: 270px;">
                                            </el-input>
                                        </template>
                                    </el-form-item>
                                </div>
                            </template>

                            <template v-if="courseForm.sellWay == 3">
                                <div class="mt-20">
                                    <span class="color-999 font-14">仅被指定学员可免费学习课程，添加指定学员请前往【学员列表】手动操作</span>
                                </div>
                            </template>

                            <div v-if="courseForm.sellWay != 3" class="mt-20 flex ai-center"><span
                                    class="color-666 font-14 mr-20">有效期</span>
                                <el-radio-group v-model="dateradio">
                                    <el-radio :label="0">长期有效</el-radio>
                                    <el-radio :label="1">自定义</el-radio>
                                </el-radio-group>

                                <div class="font-14 color-666 ml-10">
                                    <span>领取后</span><el-input placeholder="请输入" v-model.number.trim="courseForm.validTime"
                                        style="width: 200px; margin:0 5px;" :disabled="!dateradio">
                                    </el-input><span>天有效</span>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-form-item>

                <el-form-item v-if="isRelease">
                    <template slot="label">
                        <span class="color-666">上架设置</span>
                    </template>
                    <template>
                        <div class="color-666 font-14 flex-col" style="margin-top: 13px;">
                            <el-radio-group v-model="shelvesradio">
                                <el-radio :label="1">立即上架</el-radio>
                                <!-- <el-radio :label="2">定时上架</el-radio> -->
                                <el-radio :label="3">下架</el-radio>
                            </el-radio-group>
                            <!-- <template v-if="shelvesradio == 1">
                                <div class="flex-col mt-15">
                                    <span style="color: #C9CDD4;">更多设置</span>
                                    <div class="flex">
                                        <el-checkbox v-model="checked">定时下架</el-checkbox>
                                        <div class="ml-10"> <el-date-picker v-model="date1" :key="500" type="datetime"
                                                popper-class="my-course-date1" placeholder="选择日期时间"
                                                @focus="courseTimeFocus(1)" value-format="yyyy-MM-dd HH:mm:ss"
                                                :disabled="!checked">
                                            </el-date-picker></div>
                                    </div>
                                </div>
                            </template> -->
                            <!-- <template v-if="shelvesradio == 2">
                                <div class="flex-col mt-15">
                                    <div>
                                        <el-form-item prop="upTime">
                                            <template slot="label">
                                                <span class="color-666">上架时间</span>
                                            </template>
                                            <template>
                                                <el-date-picker v-model="date2" type="datetime" :key="510"
                                                    popper-class="my-course-date2" placeholder="选择日期时间"
                                                    @focus="courseTimeFocus(2)" value-format="yyyy-MM-dd HH:mm:ss">
                                                </el-date-picker>
                                            </template>
                                        </el-form-item>
                                    </div>
                                    <span class="mt-15" style="color: #C9CDD4;">更多设置</span>
                                    <div class="flex">
                                        <el-checkbox v-model="checked1">定时下架</el-checkbox>
                                        <div class="ml-10"> <el-date-picker v-model="date3" type="datetime"
                                                popper-class="my-course-date3" value-format="yyyy-MM-dd HH:mm:ss"
                                                @focus="courseTimeFocus(3)" placeholder="选择日期时间" :key="520"
                                                :disabled="!checked1">
                                            </el-date-picker></div>
                                    </div>
                                </div>
                            </template> -->
                            <template v-if="shelvesradio == 3">
                            </template>
                        </div>
                    </template>
                </el-form-item>

                <el-form-item>
                    <template slot="label">
                        <span class="color-666">隐藏设置</span>
                    </template>
                    <template>
                        <div class="color-666 font-14 flex-col">
                            <div class="flex ai-center"><el-checkbox v-model="courseForm.hide">隐藏</el-checkbox> <span
                                    class="color-999 font-14 ml-10">不可通过搜索或列表进行访问，仅通过链接式访问</span></div>
                        </div>
                    </template>
                </el-form-item>
            </el-form>

        </div>

        <div class="pt-20 pl-40">


            <template v-if="$route.query.type == 'edit'">
                <hyButton
                    :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px' }"
                    size="small" width="110px" height="36px" :plain="true" @click="nextStep('courseForm', true)">下一步
                </hyButton>

                <hyButton
                    :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px' }"
                    size="small" width="80px" height="36px" :plain="true" @click="nextStep('courseForm')">修改</hyButton>

                <hyButton
                    :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px' }"
                    size="small" width="80px" height="36px" :plain="true" @click="goToPage('/userCenter/myCourse')">取消
                </hyButton>
            </template>

            <template v-else>
                <hyButton
                    :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px' }"
                    size="small" width="120px" height="36px" :plain="true" @click="nextStep('courseForm')">保存并下一步</hyButton>
            </template>
        </div>
        <choiceImgorVideo :choiceDialog="choiceFlag" @updateFlag="updateFlag" :type="2" @currentObj="currentImg">
        </choiceImgorVideo>
    </div>
</template>


<script>
import pick from 'lodash/pick';
import choiceImgorVideo from './choiceImgorVideo.vue'
export default {
    name: 'HuanyuCollegeNuxtWebCreateCourseStepOne',
    components: {
        choiceImgorVideo
    },
    data() {
        const validateIntro = (rules, value, callback) => {
            if (delHtml(value)) {
                callback()
            } else {
                const imgStrs = value.match(/<img.*?>/g)
                if (imgStrs && imgStrs.length) { // 内容只有图片时
                    callback()
                } else {
                    callback(new Error('请填写课程详情'))
                }
            }

            // 过滤html代码、空格、回车 空白字符
            function delHtml(str) {
                str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
                str = str.replace(/[\r\n]/g, '')
                str = str.replace(/\s/g, '')
                str = str.replace(/&nbsp;/ig, '')
                return str
            }
        }

        const validateSellingPrice = (rules, value, callback) => {
            if (value) {
                if (value == 0 || value == 0.0 || value == 0.00) {
                    callback(new Error('价格不能为0'))
                    return
                }
                let regNum = /^\d+(.\d{1,2})?$/;
                let regCh = /[\u4e00-\u9fa5]/g;
                if (regCh.test(value)) {
                    callback(new Error('请勿输入中文'))
                    return
                }
                if (!regNum.test(value)) {
                    callback(new Error('请输入正确的格式，最多保留两位小数'))
                    return
                }
                this.$refs['courseForm'].validateField('discountPrice')
                callback()
            } else {
                callback()
            }
        }

        const validateDiscountPrice = (rules, value, callback) => {
            if (value) {
                if (value == 0 || value == 0.0 || value == 0.00) {
                    callback(new Error('价格不能为0'))
                    return
                }
                let regNum = /^\d+(.\d{1,2})?$/;
                let regCh = /[\u4e00-\u9fa5]/g;
                if (regCh.test(value)) {
                    callback(new Error('请勿输入中文'))
                    return
                }
                if (!regNum.test(value)) {
                    callback(new Error('请输入正确的格式，最多保留两位小数'))
                    return
                }
                if (this.courseForm.sellingPrice) {
                    if (Number(value) >= Number(this.courseForm.sellingPrice)) {
                        callback(new Error('商品价格不能大于或等于划线价格'))
                    } else {
                        callback()
                    }
                } else {
                    callback()
                }
            } else {
                callback(new Error('请输入商品价格'))
                return
            }
        }
        return {
            choiceFlag: false, // 选择素材弹窗是否展示
            timestamp: '', // 防止富文本框不响应问题
            dateradio: 0, // 有效期选择 
            shelvesradio: 3, // 上下架状态
            checked: false, // 是否定时下架
            checked1: false, // 是否定时下架
            date1: '', // 立即上架时的定时下架时间
            date2: '', // 定时上架的定时上架时间
            date3: '', // 定时上架的定时下架时间
            courseForm: { // 新建视频课程表单
                curriculumType: 2,
                name: "", // 课程名称
                sortUid: "", // 知识话题uid
                pictureUid: "",  //封面uid
                description: "", // 课程简介
                sellWay: 0, // 售卖方式0-免费 1-付费 2-加密 3-指定学员
                intro: '', // 课程详情
                sellingPrice: '', // 原售价
                discountPrice: '', // 折扣价
                password: '', // 密码
                validTime: '', // 有效期
                pictureUrl: '',// 封面url
                hide: false, // 是否隐藏
            },
            rules: {
                name: [{ required: true, message: '请输入课程名称', trigger: ['change', 'blur'] }],
                sortUid: [{ required: true, message: '请选择知识话题', trigger: ['change', 'blur'] }],
                pictureUid: [{ required: true, message: '请选择封面', trigger: ['change', 'blur'] }],
                description: [{ required: true, message: '请填写课程简介', trigger: ['change', 'blur'] }],
                intro: [
                    { required: true, validator: validateIntro, trigger: ['change', 'blur'] },
                ],
                sellWay: [
                    { required: true, message: '请填写售卖', trigger: ['change', 'blur'] }
                ],
                sellingPrice: [
                    { validator: validateSellingPrice, trigger: ['change', 'blur'] },
                ],
                discountPrice: [
                    { required: true, validator: validateDiscountPrice, trigger: ['change', 'blur'] },
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: ['change', 'blur'] }
                ],
            },
            knowledgeList: [],//知识话题
            oldCourseObj: {}, // 保存查看课程时的对象，后面用来比较是否改了课程
            oldDateRadio: null,
            manualState: null,
        }
    },
    watch: {
        'courseForm.sellWay': {
            handler: function (val) {
                let formName = 'courseForm'
                if (val == 0 || val == 3) {
                    if (val == 3) {
                        this.courseForm.validTime = ''
                    }
                    this.courseForm.sellingPrice = 0
                    this.courseForm.discountPrice = 0
                    this.courseForm.password = null
                    this.$refs[formName].clearValidate('sellingPrice')
                    this.$refs[formName].clearValidate('discountPrice')
                    this.$refs[formName].clearValidate('password')
                } else if (val == 1) {
                    this.courseForm.password = null
                    this.$refs[formName].clearValidate('password')
                } else if (val == 2) {
                    this.courseForm.sellingPrice = 0
                    this.courseForm.discountPrice = 0
                    this.$refs[formName].clearValidate('sellingPrice')
                }
            },
            deep: true
        },
        'shelvesradio': {
            handler: function (val) {
                if (val == 1 || val == 3) {
                    this.date2 = ''
                    this.date3 = ''
                    this.checked1 = false
                } else if (val == 2) {
                    this.checked = false
                }
            },
            deep: true
        },
        // 'courseDetail': {
        //   handler: function (val) {
        //     if (Object.keys(val).length) {
        //       const data = this.$deepCopy(val)
        //       this.courseForm = data
        //       this.shelvesradio = data.shelvesradio
        //       this.firstFrame = data.firstFrame
        //       this.checked = Boolean(data.date1)
        //       this.checked1 = Boolean(data.date3)
        //       this.date1 = data.date1
        //       this.date2 = data.date2
        //       this.date3 = data.date3
        //     }
        //   },
        //   deep: true
        // },
        'checked': {
            handler: function (val) {
                if (!val) {
                    this.date1 = ''
                }
            },
            deep: true
        },
        'checked1': {
            handler: function (val) {
                if (!val) {
                    this.date3 = ''
                }
            },
            deep: true
        },
        'dateradio': {
            handler: function (val) {
                if (!val) {
                    this.courseForm.validTime = 0
                }
            },
            deep: true
        }
    },
    computed: {
        isRelease() {
            return this.$route.query.type == 'edit' && this.manualState == 2
        }
    },
    mounted() {
        const courseUid = this.$store.state.myCreateNormalCourseCurrentUid || ''
        if (courseUid) {
            this.getCourseDetail(courseUid)
        }
        this.getSelectList()
    },
    methods: {
        // 保存并下一步
        nextStep(formName, check) {
            if (check) {
                const data = JSON.parse(JSON.stringify(this.courseForm))
                const result = this.findChangedProperties(this.oldCourseObj, data)
                if (Object.keys(result).length || this.oldDateRadio != this.dateradio) {
                    this.$confirm('此时离开你已编辑的课程内容将丢失，是否离开', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$store.commit('setMyCreateNormolCourseStep', 'createCourseStepTwo')
                    }).catch(() => {

                    });
                } else {
                    this.$store.commit('setMyCreateNormolCourseStep', 'createCourseStepTwo')
                }
                return
            }
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 防抖
                    this.$debounce(() => {
                        const data = JSON.parse(JSON.stringify(this.courseForm))
                        data.hide = data.hide ? '1' : '0'
                        data.status = this.shelvesradio == 1 ? '1' : this.shelvesradio == 3 ? '2' : '1'
                        data.upRelease = this.shelvesradio == 1 ? '0' : this.shelvesradio == 3 ? '0' : '1'
                        data.downRelease = this.checked || this.checked1 ? '1' : '0'
                        data.validTime = this.courseForm.sellWay == 3 ? 0 : data.validTime
                        data.downTime = this.shelvesradio == 1 ? this.date1 : this.shelvesradio == 2 ? this.date3 : ''
                        data.upTime = this.shelvesradio == 2 ? this.date2 : ''
                        data.speakerUid = this.$store.state.userInfo && this.$store.state.userInfo.uid
                        data.intro = encodeURIComponent(data.intro)
                        delete data.pictureUrl
                        // const submitFun = this.$store.state.myCreateNormalCourseCurrentUid ? 'UpdateMyCurriculum' : 'addMyCurriculum'
                        this.$request.myCourse['addMyCurriculum'](data).then((res) => {
                            if (res.data.code == 200) {
                                this.$Elemessage('success', '保存成功')
                                this.$store.commit('setMyCreateNormalCourseCurrentUid', res.data.data)
                                this.$store.commit('setMyCreateNormolCourseStep', 'createCourseStepTwo')
                            }
                        }).catch((err) => {
                            this.$message.warning(err.data.message)
                        })
                    }, 1000)();
                }
            })
        },
        // 打开素材中心弹窗
        openChoice() {
            this.choiceFlag = true
        },
        // 关闭素材中心弹窗
        updateFlag(value) {
            this.choiceFlag = value
        },
        // 选中的当前图片
        currentImg(img) {
            if (img) {
                const { fileUrl, fileUid } = img
                this.courseForm.pictureUrl = fileUrl
                this.courseForm.pictureUid = fileUid
                this.$refs['courseForm'].clearValidate('pictureUid')
            }
        },
        getBaseUrl() {
            return (localStorage.getItem('picBaseUrl'))
        },
        //获取知识话题
        async getSelectList() {
            const res2 = await this.$axios.get(this.$api.getSelectBlogTopic);
            if (res2.data.code == 200) {
                //过滤掉没有二级分类的
                this.knowledgeList = res2.data.data.filter(option => option.blogSortList && option.blogSortList.length > 0);
            }
        },
        //知识体系二级选择
        handleChange(e) {
            // console.log('选择知识体系', e)
            this.courseForm.sortUid = e && e[e.length - 1]
        },
        // 获取课程详情
        getCourseDetail(uid) {
            const params = { uid }
            this.$request.myCourse.getVideoCourseDetailByUid(params).then((res) => {
                if (res.data.code == 200) {
                    let formData = pick(res.data.data, ['uid','hide', 'description', 'name', 'sortUid', 'pictureUid', 'sellWay', 'sellingPrice', 'discountPrice', 'password', 'validTime', 'pictureUrl', 'intro', 'curriculumType']);
                    const { status, validTime, manualState,upRelease,downRelease,downTime,upTime,hide } = res.data.data
                    if (formData.hide == 0) {
                        this.$set(formData , 'hide',false)
                    } else if (formData.hide == 1) {
                        this.$set(formData , 'hide',true)
                    }
                    this.courseForm = JSON.parse(JSON.stringify(formData))
                    console.log('courseForm', this.courseForm)
                    this.oldCourseObj = JSON.parse(JSON.stringify(this.courseForm))
                    this.shelvesradio = status == 2 ? 3 : 1
                    if (upRelease == 1) {
                        this.shelvesradio = 2
                        this.date2 = upTime
                    }
                    if(downRelease == 1) {
                        if(this.shelvesradio == 1) {
                            this.checked = true
                            this.date1 = downTime
                        } else if(this.shelvesradio == 2) {
                            this.checked1 = true
                            this.date3 = downTime
                        }
                    }
                    console.log(this.courseForm.validTime);
                    this.dateradio = validTime ? 1 : 0
                    this.oldDateRadio = this.dateradio
                    this.timestamp = new Date().getTime()
                    this.manualState = manualState
                }
            })
        },
        // 找到对象发送变化的属性
        findChangedProperties(oldObject, newObject) {
            const changedProperties = {};
            const keys = Object.keys(newObject);

            for (const key of keys) {
                if (oldObject[key] !== newObject[key]) {
                    changedProperties[key] = newObject[key];
                }
            }
            return changedProperties;
        },
        // 通过传进来的路由路径进行跳转
        goToPage(url) {
            if (!url) return
            this.$store.commit('setMyCreateNormalCourseCurrentUid', '')
            this.$store.commit('setMyCreateNormolCourseStep', 'createCourseStepOne')
            this.$router.push({
                path: url
            });
        },
        // 选择时间时的清空功能
        courseTimeFocus(type) {
            this.$nextTick(() => {
                const parentDiv = document.querySelector(`.my-course-date${type}`)
                if (parentDiv) {
                    const ele = parentDiv.querySelector('.el-picker-panel__footer')
                    if (ele) {
                        const btnTxt = ele.querySelector('.el-button--text')
                        if (btnTxt) {
                            btnTxt.innerHTML = '清空'
                            btnTxt.addEventListener('click', () => {
                                this[`date${type}`] = ''
                            })
                        }
                    }
                }
            })
        },
    }
}

</script>

<style lang="scss" scoped>
.container-one {
    .container {

        .demo-courseForm {
            /deep/ .el-form-item__label {
                color: $color666;
            }

            /deep/ .el-radio__label {
                color: $color666;
            }

            .courseForm-input {
                /deep/ .el-input__inner {
                    padding: 0 50px 0 15px;
                }
            }
        }
    }

    .choice-img-area {
        .img-div {
            width: 150px;
            height: 150px;
            border: 1px dashed #D4DBE0;
            box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
            border-radius: 4px;
            line-height: 18px;
            cursor: pointer;
        }

        .img-div:hover {
            border-color: $colorMain;
        }
    }
}
</style>

<!-- <style>
.my-course-date>.el-picker-panel__footer>.el-button--text {
    display: none !important;
}
</style> -->