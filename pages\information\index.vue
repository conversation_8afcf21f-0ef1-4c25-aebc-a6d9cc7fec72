<template>
  <div class="information min-w-1440">
    <div class="bannner">
      <informationBanner></informationBanner>
    </div>
    <div class="flex jc-center">
      <div class="w-1440">
        <div class="top-tab flex ai-center w-100">
          <p class="font24 f-w-400 mr-30 title-2">领域</p>
          <div>
            <el-menu :default-active="activeIndex" background-color="#F5F8FC" mode="horizontal" @select="handleSelect">
              <information-menu v-for="item in Menus" :item="item" :key="item.uid"></information-menu>
            </el-menu>
          </div>
        </div>
      </div>
    </div>
    <div class="flex jc-center" style="background: #ffffff">
      <div class="w-1440">
        <div class="content-body flex w-100">
          <!-- 左侧 -->
          <div class="body-left flex-col flex-7" style="margin-top: 24px; overflow: hidden">
            <div class="information-item w-100 flex w-100 ai-center mb-20" v-for="(item, index) in ArticleList"
              :key="index" @click="toDetail(item)">
              <div v-if="menu.name != '快讯'" class="item-poster flex-3" ref="imgwidth" :style="{
                height: ImgHeight + 'px',
              }">
                <div class="w-100 h-100">
                  <img class="w-100 h-100" style="border-radius: 4px" object-fit="none"
                    :src="item.fileUidUrl" />
                </div>
              </div>
              <div class="item-info flex-7 flex-col jc-between h-100 ml-20 cursor pb-20 pt-10 pr-40">
                <div class="font16 flex ai-center f-w-500" style="color: #333333">
                  <div v-if="menu.name == '快讯'" class="round">

                  </div>
                  {{ item.title }}
                </div>
                <div class="font12 f-w-400 mt-10 ellipsis2" style="color: #999999" v-html="filterContent(item.content)">
                </div>
                <div class="mt-10 flex ai-center jc-between">
                  <p class="font12 f-w-400" style="color: #999999">
                    {{ item.auditTime }}
                  </p>
                  <div class="tags flex ai-center" v-if="item.topicName">
                    <div class="tags-item font12 f-w-400">
                      {{ item.topicName }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="flex ai-center jc-center font14 f-w-400 mt-40" style="color: #9e9e9e">
              {{ isTotal ? "没有更多了" : "加载更多" }}
            </div> -->
            <!-- 分页器 -->
            <div v-if="ArticleList.length" class="pagination-left">
              <el-pagination
                @size-change="handleLeftSizeChange"
                @current-change="handleLeftCurrentChange"
                background
                :current-page="searchParams.currentPage"
                :page-size="searchParams.pageSize"
                :page-sizes="[10, 20, 30, 40]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
              </el-pagination>
            </div>
          </div>
          <!-- 右侧 -->
          <div class="body-right pl-40 flex-3" style="margin-top: 24px">
            <div class="rank-item">
              <div class="flex ai-center jc-between mb-40">
                <p class="font16 f-w-500" style="color: #333333">社区版主</p>
                <!-- <div class="font12 f-w-400 flex ai-center" style="color: #999999">
                  更多
                  <i class="el-icon-arrow-right font12" style="margin-left: 8px; color: #999999"></i>
                </div> -->
              </div>
              <div v-if="communityList.length > 0">
                <div class="rank-list flex ai-center" v-for="item in communityList" :key="item.uid">
                  <div style="width: 70px; height: 70px; overflow: hidden">
                    <img class="w-100 h-100" style="border-radius: 50%;cursor: pointer" :src="item.avatarUrl" @click="toDetailPersonal(item)"/>
                  </div>
                  <div class="rank-info ml-15 flex-col flex-1">
                    <p class="font14 f-w-400" style="color: #333333">
                      {{ item.name }}
                    </p>
                    <div class="ellipsis2 font12 f-w-400" style="margin-top: 4px; color: #999999">
                      {{ item.description }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="w-100 flex ai-center jc-center" v-else>
                <el-empty :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
              </div>
            </div>
            <!-- <div class="rank-item">
              <div class="flex ai-center jc-between mb-40">
                <p class="font16 f-w-500" style="color: #333333">实验室主任</p>
                <div class="font12 f-w-400 flex ai-center" style="color: #999999">
                  更多
                  <i class="el-icon-arrow-right font12" style="margin-left: 8px; color: #999999"></i>
                </div>
              </div>
              <div v-if="laboratoryList.length > 0">
                <div class="rank-list flex ai-center cursor" v-for="item in laboratoryList" :key="item.uid"
                  @click="toUserDetail(item)">
                  <div style="width: 70px; height: 70px">
                    <img class="w-100 h-100" style="border-radius: 50%" :src="$getFullImage(item.avatarUrl)" />
                  </div>
                  <div class="rank-info ml-15 flex-col flex-1">
                    <p class="font14 f-w-400" style="color: #333333">
                      {{ item.name }}
                    </p>
                    <div class="ellipsis2 font12 f-w-400" style="margin-top: 4px; color: #999999">
                      {{ item.description }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="w-100 flex ai-center jc-center" v-else>
                <el-empty :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
              </div>
            </div> -->

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import informationBanner from "./component/informationBanner.vue";
import InformationMenu from "./component/informationMenu.vue";
export default {
  name: "",
  components: {
    informationBanner,
    InformationMenu,
  },
  data() {
    return {
      activeIndex: "",
      Menus: [], //二级菜单
      isTotal: false, //是否是最后一页
      total: 0,
      ImgHeight: 0,
      searchParams: {
        currentPage: 1,
        pageSize: 10,
        fromSystemUid: "",
        isDomain: 1,
      },
      ArticleList: [],
      infoList: [],
      laboratoryList: [],
      communityList: [],
      tabsFlag: 0,
      tabsList: [],
      menu: ''
    };
  },
  computed: {},
  created() {
    this.getMenus();
  },
  mounted() {
    this.getRightList();
    // 滚动监听方法
    // document
    //   .getElementsByClassName("layout")[0]
    //   ?.addEventListener("scroll", this.scrollFn);
    window.addEventListener("resize", this.handleResize);
  },
  destroyed() {
    // 防止切换到其他页面继续监听
    // 销毁滚动
    // document
    //   .getElementsByClassName("layout")[0]
    //   ?.removeEventListener("scroll", this.scrollFn);
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 当前页
    handleLeftCurrentChange(val) {
      this.searchParams.currentPage = val
      this.getInformationArticleList()
    },
    // 页数
    handleLeftSizeChange(val) {
      this.searchParams.pageSize = val
      this.getInformationArticleList()
    },
    toDetailPersonal ({ uid }) {
      this.$router.push({
        path: '/laboratoryNew/personal',
        query: { speakerUid: uid }
      })
    },
    //去个人实验室
    toUserDetail(item) {
      if (item.speakerUid) {
        this.$router.push({
          path: '/laboratoryNew/personal',
          query: {
            speakerUid: item.speakerUid
          }
        });
      } else {
        this.$message.error('该用户暂未开通个人实验室');
      }
    },
    // 过滤html标签
    filterContent(html) {
      if (!html) return '';
      const doc = new DOMParser().parseFromString(html, "text/html");
      const images = doc.getElementsByTagName("img");
      for (let i = images.length - 1; i >= 0; i--) {
        images[i].parentNode.removeChild(images[i]);
      }
      const videos = doc.getElementsByTagName("video");
      for (let i = videos.length - 1; i >= 0; i--) {
        videos[i].parentNode.removeChild(videos[i]);
      }
      return doc.body.innerHTML;
    },
    //获取实验室主任与社区版主
    async getRightList() {
      const res1 = await this.$request.information.getLaboratoryDirector({
        currentPage: 1,
        pageSize: 4,
      });
      const res2 = await this.$request.information.getCommunityControl({
        currentPage: 1,
        pageSize: 4,
      });
      if (res1.data.code == this.$code.SUCCESS) {
        this.laboratoryList = res1?.data?.data.records || [];
      }
      if (res2.data.code == this.$code.SUCCESS) {
        this.communityList = res2?.data?.data.records || [];
      }
      console.log(
        " 获取实验室主任与社区版主",
        this.laboratoryList,
        this.communityList
      );
    },
    // 动态获取图片宽度
    handleResize() {
      const width = this.$refs.imgwidth
        ? this.$refs.imgwidth[0].offsetWidth
        : 0;
      const height = Math.floor(width * (9 / 16)); //动态获取图片宽度 * 0.7
      this.ImgHeight = height;
      // console.log('图片宽度', width, height)
    },
    //下滑事件
    scrollFn() {
      // 获取盒子DOM
      let el = document.getElementsByClassName("layout")[0];
      if (!el) return;
      // 获取窗口可视范围高度
      let clientHeight =
        window.innerHeight ||
        Math.min(
          document.documentElement.clientHeight,
          document.body.clientHeight
        );
      // 请求数据
      if (clientHeight + el.scrollTop >= el.scrollHeight - 500) {
        console.log("滚动请求");
        this.loadMoreFollow();
      }
    },
    //下拉加载
    loadMoreFollow() {
      if (!this.isTotal) {
        this.searchParams.currentPage++;
        this.getInformationArticleList();
      }
    },
    resetParams() {
      this.isTotal = false;
      this.searchParams.currentPage = 1;
      this.getInformationArticleList();
    },
    //获取文章列表
    getInformationArticleList() {
      this.$request.information
        .getInformationArticle(this.searchParams)
        .then((res) => {
          if (res.data.code == this.$code.SUCCESS) {
            const { records = [], total = 0, size } = res.data?.data || {};
            if (records.length > 0) {
              records.forEach((item) => {
                item.content = this.extractPlainText(item.content);
              });
            }
            this.ArticleList = records || [];
            this.total = total;
            this.isTotal = this.ArticleList.length == total ? true : false;
          }
          console.log("最后一页", this.isTotal);
          console.log("获取文章列表1", this.ArticleList);
        });
    },
    extractPlainText(html) {
      const regex = /(<([^>]+)>)/gi;
      return html.replace(regex, "");
    },
    //菜单栏选中
    handleSelect(e) {
      console.log("菜单选中", e);
      if (e != "全部") {
        this.menu = this.Menus.find((item) => item.uid == e);
      } else {
        this.menu = "";
      }
      this.searchParams.fromSystemUid = e == "全部" ? null : e;
      this.ArticleList = [];
      this.resetParams();
    },
    //获取菜单导航
    getMenus() {
      this.$request.information
        .getInformationTerritory({ columnName: "领域" })
        .then((res) => {
          console.log("获取菜单导航", res);
          if (res.data.code == this.$code.SUCCESS) {
            this.Menus = res.data?.data || [];
            this.Menus.unshift({ name: "全部", uid: "全部" });
            this.activeIndex = "全部";
            this.searchParams.fromSystemUid = null;
            this.resetParams();
            setTimeout(() => {
              this.handleResize();
            }, 1000);
          }
        });
    },
    toDetail(item) {
      // this.$router.push({
      //   path: "/information/articleDetails",
      //   query: { uid: item.uid, articleSource: item.articleSource },
      // });
      let routeData = this.$router.resolve({
        path: "/information/articleDetails",
        query: { uid: item.uid, articleSource: item.articleSource },
      })
      window.open(routeData.href,'_blank')
    },
  },
};
</script>

<style lang="scss" scoped>
.title-2 {
  font-size: 30rpx;
  font-family: FZZongYi-M05S;
  font-weight: 600;
  font-style: italic;
  color: #006eff;
  line-height: 50rpx;
}

.information {
  .top-tab {
    background: #f5f8fc;

    /deep/ .el-menu .is-active {
      background-color: #006eff !important;
      color: #fff !important;
    }

    /deep/ .el-menu.el-menu--horizontal {
      border: none;
    }

    .tabs-item {
      line-height: 40px;
      padding: 9px 16px 9px 16px;
      color: #666666;
      display: flex;
      align-items: center;
    }

    .tabs-item-act {
      background: #006eff;
      color: #ffffff;
    }
  }

  .content-body {
    padding-bottom: 40px;

    .body-left {
      border-right: 1px solid #e6eaed;

      .information-item {
        // height: calc(90%);

        .item-poster {
          width: 100%;
          overflow: hidden;
        }

        .item-info {
          overflow: hidden;

          .tags {
            .tags-item {
              color: #666666;
              margin-right: 20px;
              padding: 3px 8px 3px 8px;
              background: #f4f6f7;
              border-radius: 2px;
            }
          }
        }
      }

      .pagination-left {
        text-align: center;
      }
    }

    .body-right {
      .rank-item {
        .rank-list {
          width: 90%;
          // width: 280px;
          height: 94px;
          border-bottom: 1px solid #e6eaed;

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }

  .content {
    background: #ffffff;
    padding-bottom: 40px;
  }
}

.round {
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background: #006eff;
  margin-right: 10px;
}
</style>
