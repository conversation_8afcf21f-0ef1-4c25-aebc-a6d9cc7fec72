<template>
  <div>
    <div class="mt-50 data" ref="reviewContent" :style="boxStyle">
      <div class="flex ai-center jc-between">
        <div class="flex ai-center">
          <span class="font-24 color-black f-w-600 mr-10">讲堂</span>
          <img src="../../../assets/newVersion/title-1.png" alt="">
        </div>
      </div>

      <div class="flex flex-wrap jc-start">
        <div v-for="(item, index) in dataList" :key="index" class="flex-col mt-20 review-item" v-reactive="[280, 210]"
          style="margin: 15px 15px 15px 0;">
          <div class="liveBox" v-reactive="[280, 157]">
            <img :src="item.logoPath" class="review-img cursor" v-reactive="[280, 157]"
              @click="toLecture('/lectureHall/lectureRoomDetail', { liveUid: item.uid })">
            <div class="cover-tips advance font12 f-w-400 flex ai-center" style="color: #fff;"
              v-if="item.liveStatus == 2">
              <p>{{ liveStatus[item.liveStatus] }}</p>
            </div>
            <div class="bottom-tips" v-if="item.liveStatus == 2">
              <i class="el-icon-view" style="font-size: 12px;color: #fff;"></i>
              <span class="font12 f-w-400" style="margin-left:3px;color: #fff;">{{ $formatDateString(item.startTime)
              }}</span>
            </div>
          </div>
          <div class="mt-10 ellipsis2 color-333 font-16px f-w-400" style="min-height:44px">{{ item.name }}</div>
        </div>
      </div>

      <el-empty v-if="!dataList || dataList.length < 1" :image="require('@/assets/images/certification/empty.png')"
        description="暂无数据"></el-empty>
    </div>
    <div class="mt-50 speaker">
      <div class="flex ai-center mb-15">
        <div class="font-24 color-black mr-10 f-w-600">讲者</div>
        <img src="../../../assets/newVersion/title-4.png" alt="">
      </div>
      <div v-if="speakerList && speakerList.length > 0" class="flex flex-wrap">
        <div v-for="(item, index) in   speakerList  " :key="index" class="mt-10 recom-item"
          :class="{ 'mr-20': (index + 1) % 2 != 0 }">
          <div class="flex" style="height: 100%;">
            <div class="flex curri-main cursor" @click="toPersonDetail(item.uid)">
              <img ref="teacherHeader" :src="item.picturePath" class="tea-head mr-12">
            </div>
            <div style="width:67%;margin-left:5px;">
              <div class="font-20 color-black f-w-600 mr-8 nowrap tea-name">{{ item.name }}</div>
              <div class="font-12 color-999 flex ai-center mt-10 mb-20">
                <div><span class="color-006EFF f-w-600 mr-4">0</span>门课程</div>
                <div class="line"></div>
                <div><span class="color-006EFF f-w-600 mr-4">0</span>位学员</div>
              </div>
              <div class="font-14 color-999 intro " v-html='item.intro'></div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
    </div>
    <div class="mt-50 data" ref="reviewContent" :style="boxStyle">
      <div class="flex ai-center jc-between">
        <div class="flex ai-center">
          <span class="font-24 color-black f-w-600 mr-10">套餐合集</span>
          <img src="../../../assets/newVersion/title-1.png" alt="">
        </div>
      </div>
      <div class="mt-20">
        <template v-if="setMealList.length">
          <div v-for="item in setMealList">
            <setmeal :data="item" style="margin-bottom: 15px;"></setmeal>
          </div>
        </template>
        <el-empty style="margin: 0 auto;" v-else :image="require('@/assets/images/certification/empty.png')"
          description="暂无数据"></el-empty>
      </div>
    </div>
    <div class="mt-50 data" ref="reviewContent" :style="boxStyle">
      <div class="flex ai-center jc-between">
        <div class="flex ai-center">
          <span class="font-24 color-black f-w-600 mr-10">课程合集</span>
          <img src="../../../assets/newVersion/title-1.png" alt="">
        </div>
      </div>
      <div class="flex flex-wrap jc-start pt-25">
        <div v-for="(item, index) in compilationsList" :key="index" class="flex-col mt-20 review-item"
          v-reactive="[280, 293]" style="margin:0 15px 15px 0;">
          <img :src="item.pictureUrl" class="review-img cursor" v-reactive="[280, 157]">
          <div class="mt-10 ellipsis2 color-333 font-16px f-w-400" style="min-height:44px">{{ item.curriculumName }}</div>
          <div class="mt-8 flex ellipsis">
            <div v-for="(val, i) in  pointList " :key="i" class="point-bg"
              :class="{ 'mr-5': (i + 1) != pointList.length }">
              {{ val }}
            </div>
          </div>
          <div class="flex ai-center jc-between mt-12">
            <div class="font-18 f-w-700 color-FF564D"><span class="font-14 f-w-500">￥</span>{{ item.sellingPrice }}</div>
            <div class="color-999 font-12 f-w-400">{{ item.studyTotal || 0 }} 人报名</div>
          </div>
        </div>
      </div>
      <el-empty v-if="!compilationsList || compilationsList.length < 1"
        :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
    </div>
  </div>
</template>

<script>
import setmeal from '/pages/huYuanSchool/components/setmeal.vue'
export default {
  components: {
    setmeal
  },
  data() {
    return {
      liveStatus: {
        1: '立即预约',
        2: '即将直播',
        3: '进入直播',
        4: '已结束',
        5: '查看回放'
      },
      setMealList: [],
      review: require('../../../assets/newVersion/review1.png'),
      dataList: [],
      compilationsList: [],//合集
      pointList: ['数据库', 'Oracle', 'MySQL', '大数据', 'Linux'],
      params: {
        name: "",
        liveType: "",
        startTime: null,
        endTime: null,
        currentPage: 1,
        pageSize: 3,
        enterpriseUid: ''
      },
      speakerParams: {
        currentPage: 1,
        pageSize: 4,
        enterpriseUid: ''
      },
      boxStyle: '',
      speakerList: [],
      curriStyle: '',
      teacherHeaderWidth: 0
    };
  },
  computed: {
    getLeftStyle() {
      return 'left:' + this.teacherHeaderWidth + 'px'
    }
  },
  watch: {
    '$route.query': {
      handler: function (newVal) {
        // console.log('监听', newVal)
        if (newVal && newVal.enterpriseUid) {
          this.params.enterpriseUid = newVal.enterpriseUid
          this.speakerParams.enterpriseUid = newVal.enterpriseUid
          this.getData()
          this.getSpeakerData()
          this.getCompilations()
          this.getSetMealList()
          window.addEventListener('resize', this.handleResize)
          setTimeout(() => {
            this.handleResize()
          }, 300)
        }
      },
      immediate: true
    },
  },
  async mounted() {
    // this.getData()
    // this.getSpeakerData()
    // this.getCompilations()
    // this.getSetMealList()
    // window.addEventListener('resize', this.handleResize)
    // setTimeout(() => {
    //   this.handleResize()
    // }, 300)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 计算节省价格
    sparePrice(scribePrice, packagePrice) {
      let sparePriceHander = scribePrice - packagePrice;
      return isNaN(sparePriceHander) ? 0 : sparePriceHander < 0 ? 0 : sparePriceHander;
    },
    // 获取套餐列表
    getSetMealList() {
      let params = {
        currentPage: 1,
        pageSize: 2,
        uid: this.$route.query.enterpriseUid,//讲者uid
      }
      this.$request.lectureHall.getEnterpriseSet(params).then(res => {
        if (res.data.code == 0) {
          let data = res.data.data.records;
          data.forEach(item => {
            let currentCurriculumList = item.curriculumList;
            if (currentCurriculumList) {
              let curriculumList = [];
              for (let i = 0; i < currentCurriculumList.length; i += 3) {
                curriculumList.push(currentCurriculumList.slice(i, i + 3));
              }
              item.curriculumList = curriculumList;
            } else {
              item.curriculumList = [];
            }
          });
          this.setMealList = data;
          console.log("套餐列表", this.setMealList)
        }
      })
    },
    //获取企业视频合集
    getCompilations() {
      let params = {
        enterpriseUid: this.$route.query.enterpriseUid,
        currentPage: 1,
        pageSize: 6
      }
      this.$request.lectureHall.getEnterpriseSetmeal(params).then(res => {
        console.log('获取视频合集', res)
        if (res.data.code == this.$code.SUCCESS) {
          this.compilationsList = res.data.data.records
        }
      })
    },
    toPersonDetail(speakerUid) {
      this.$router.push({
        path: '/laboratoryNew/personal',
        query: {
          speakerUid: speakerUid
        }
      });
    },
    handleResize() {
      const teacherHeader = this.$refs.teacherHeader
      if (teacherHeader[0] && teacherHeader[0].offsetWidth) {
        this.teacherHeaderWidth = teacherHeader[0].offsetWidth
        this.curriStyle = 'width: calc(100% - ' + (teacherHeader[0].offsetWidth + 12) + 'px)'
      }
    },
    // reviewBgResize() {
    //   let offsetWidth = this.$refs.reviewContent.offsetWidth
    //   this.boxStyle = '--review-item-width:' + ((offsetWidth - 41) / 3) + 'px;'
    // },
    getData() {
      this.$request.lectureHall.getLiveList(this.params).then(res => {
        if (res.data.code == 200) {
          this.dataList = res.data.data.records
        }
      })
    },
    getSpeakerData() {
      this.$request.lectureHall.getEnterpiseSpeakerList(this.speakerParams).then(res => {
        if (res.data.code == 200) {
          this.speakerList = res.data.data.records
        }
      })
    },
    toLecture(path, query = {}) {
      this.$router.push({ path, query })
    },

  },
};
</script>
<style lang="scss" scoped>
.data {
  .review-item {
    // width: var(--review-item-width);
    width: calc((100% - 60px) / 3);

    .liveBox {
      position: relative;

      .review-img {
        width: 100%;
        object-fit: cover;
        border-radius: 8px;
      }

      .bottom-tips {
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 0 10px 0 20px;
        text-align: center;
        height: 24px;
        background: linear-gradient(270deg, #D500FF 0%, #0972FF 99%, #006EFF 100%);
        border-radius: 0px 0px 0px 8px;
      }

      .cover-tips {
        position: absolute;
        top: 0;
        right: 0;
      }

      .now {
        padding: 0 10px 0 10px;
        height: 24px;
        background: linear-gradient(270deg, #FF9260 0%, #F77900 0%, #FF569C 97%, rgba(247, 121, 0, 0) 100%);
        border-radius: 0px 4px 0px 16px;
      }

      .advance {
        padding: 0 10px 0 20px;
        text-align: center;
        height: 24px;
        background: linear-gradient(270deg, #FF9260 0%, #F77900 0%, #FF569C 97%, rgba(247, 121, 0, 0) 100%);
        border-radius: 0px 4px 0px 16px;
      }

    }


  }
}

.speaker {
  .recom-item {
    width: calc((100% - 20px) / 2);
    height: 200px;
    border-radius: 4px 4px 4px 4px;
    padding: 20px;
    overflow: hidden;
    background: url('../../../assets/newVersion/recom-bg.png');
    background-size: 100% 103%;
    background-repeat: no-repeat;
    position: relative;

    .gold-teahcer {
      position: absolute;
      top: 6px;
      right: 8px;
    }
  }

  .tea-name {
    width: 65px;
  }

  .tea-head {
    height: 100%;
    object-fit: cover;
    aspect-ratio: 0.7517;
  }

  .line {
    width: 0px;
    height: 12px;
    margin: 0 12px;
    border: 1px solid #E6EAED;
  }


  .curri-list {
    overflow: auto;
    scroll-behavior: smooth;
    white-space: nowrap;

  }

  .curri-item {
    display: inline-block;
    width: 106px;

    .curri-img {
      width: 106px;
      height: 60px;
      border-radius: 0px 0px 0px 0px;
      object-fit: cover;
    }

    .curri-name {
      white-space: wrap;
    }
  }

  .mt-4 {
    margin-top: 4px;
  }


  .curri-main {
    overflow: hidden;
    position: relative;


    .curri-icon:hover {
      color: $colorMain;
      cursor: pointer
    }

  }

  .icon-bg-left {
    width: 30px;
    height: 100%;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-bg-right {
    background: linear-gradient(90deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    width: 30px;
    height: 100%;
    position: absolute;
    display: flex;
    align-items: center;
    right: -10px;
    justify-content: center;
  }
}

.color-006EFF {
  color: #006EFF;
}

.line {
  height: 12px;
  opacity: 1;
  border: 1px solid #E6EAED;
  margin: 0 12px;
}

.mr-4 {
  margin-right: 4px;
}

.point-bg {
  background: #F4F6F7;
  border-radius: 2px;
  font-size: 12px;
  font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
  font-weight: 400;
  color: #999999;
  padding: 3px 8px;
}

.color-FF564D {
  color: #FF564D;
}

.intro {
  // width: 320px;
  overflow-y: scroll;
  height: 82px;
  font-size: 14px;
  color: #666;

  // 滚动条整体样式
  &::-webkit-scrollbar {
    width: 4px !important;
    display: none !important;
    /* 纵向滚动条 宽度 */
    border-radius: 5px;
    /* 整体 圆角 */
  }

  //轨道部分
  &::-webkit-scrollbar-track {
    background: #fff !important;
  }

  // 滑块部分
  &::-webkit-scrollbar-thumb {
    background: #D4DBE0;
    min-height: 167px;
    width: 2px !important;
    border-radius: 5px;
  }

  scrollbar-width: thin !important;
  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
  -ms-overflow-style: none !important;
  /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */

}
</style>
