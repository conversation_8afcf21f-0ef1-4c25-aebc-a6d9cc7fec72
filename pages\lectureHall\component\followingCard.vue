<template>
    <div class="followingCard w-100">
        <div class="head-img flex jc-center cursor">
            <img style="width: 238px;" v-if="livaData.liveType == 1"
                :src="livaData.classroomSpeaker ? livaData.classroomSpeaker.picturePath : ''">
            <img style="width: 238px;" v-if="livaData.liveType == 2"
                :src="livaData.classroomEnterprise ? livaData.classroomEnterprise.logoPath : ''">
            <div class="person-lab fa-65"><div @click.stop="toPersonInfo">TA的数字空间</div></div>
        </div>
        <div class="w-100 flex ai-center jc-center mt-20" @click.stop="toPersonInfo">
            <p class="cursor font18 f-w-bold" v-if="livaData.liveType == 1">
              {{ livaData.classroomSpeaker ? livaData.classroomSpeaker.name : '' }}
            </p>
            <p v-if="livaData.liveType == 2" class="font18 f-w-bold">
              {{ livaData.classroomEnterprise ? livaData.classroomEnterprise.name : '' }}
            </p>
            <div class="ml-10" style="width: 64px;height: 18px;">
                <img class="w-100 h-100" src="@/assets/lectureHall/teach-title.svg">
            </div>
        </div>
        <div class="font12 f-w-400 mt-5 flex jc-center" v-if="livaData.liveType == 1">
            <p>{{ livaData.classroomSpeaker ? livaData.classroomSpeaker.categoryName : '' }}</p>
        </div>
        <div class="mt-20" style="height: 90px;">
            <div class="content-html h-100 font14 f-w-400" v-if="livaData.liveType == 1"
                style="overflow: auto;color: #666666;" v-html="livaData.classroomSpeaker ?
                    livaData.classroomSpeaker.content : ''">
            </div>
            <div class="content-html h-100 font14 f-w-400" v-if="livaData.liveType == 2"
                style="overflow: auto;color: #666666;" v-html="livaData.classroomEnterprise ?
                    livaData.classroomEnterprise.intro : ''">
            </div>
        </div>
        <!-- <div v-if="livaData.liveType == 1">
            <div class="attention flex-center mt-20 cursor" v-if="livaData.classroomSpeaker.isSubscribed == 0"
                @click="followingPersonOption(1)">
                <i class="el-icon-plus" style="color: #006EFF;"></i>
                <p class="font14 f-w-400" style="margin-left: 8px;">关注</p>
            </div>
            <div class="un-attention flex-center mt-20 cursor" v-if="livaData.classroomSpeaker.isSubscribed == 1"
                @click="followingPersonOption(0)">
                <p class="font14 f-w-400" style="margin-left: 8px;">已关注</p>
            </div>
        </div>
        <div v-if="livaData.liveType == 2">
            <div class="attention flex-center mt-20 cursor" v-if="livaData.classroomEnterprise.isSubscribed == 0"
                @click="followingEnterpriseOption(1)">
                <i class="el-icon-plus" style="color: #006EFF;"></i>
                <p class="font14 f-w-400" style="margin-left: 8px;">关注</p>
            </div>
            <div class="un-attention flex-center mt-20 cursor" v-if="livaData.classroomEnterprise.isSubscribed == 1"
                @click="followingEnterpriseOption(0)">
                <p class="font14 f-w-400" style="margin-left: 8px;">已关注</p>
            </div>
        </div> -->
        <div class="mt-20">
            <img style="height: 100px;border-radius: 8px;width: 239px;" class="w-100 h-100"
                :src="livaData.qrCodePath">
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    components: {

    },
    data() {
        return {

        }
    },
    props: {
        livaData: {
            type: Object,
            default: () => ({
                speakerUid: '',
                classroomSpeaker: {
                    content: '',
                    isSubscribed: 0,
                },
                classroomEnterprise: {
                    isSubscribed: 0,
                    content: ''
                },
                liveType: 1,
                qrCodePath: ''
            })
        }
    },
    computed: {

    },
    created() {

    },
    methods: {
        //去讲师详情页面
        toPersonInfo() {
            if (this.livaData.liveType == 1) {
                this.$router.push({
                    path: '/laboratoryNew/personal',
                    query: {
                        speakerUid: this.livaData.speakerUid
                    }
                });
            }
            if (this.livaData.liveType == 2) {
                this.$router.push({
                    path: '/enterpriseLaboratoryHomepage',
                    query: {
                        enterpriseUid: this.livaData.enterpriseUid
                    }
                });
            }
        },
        //讲师关注操作 0取关 1关注
        followingPersonOption(type) {
            this.$emit('followingPersonOption', {
                type,
                uid: this.livaData.speakerUid
            })
        },
        //企业关注操作 0取关 1关注
        followingEnterpriseOption(type) {
            this.$emit('followingEnterpriseOption', {
                type,
                uid: this.livaData.classroomEnterprise.uid
            })
        }
    },
}
</script>

<style lang="scss" scoped>
.followingCard {
    max-height: 716px;
    background: linear-gradient(180deg, #F9FCFE 0%, #FFFFFF 100%);
    box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.05);
    border-radius: 4px;
    padding: 18px;

    .head-img {
        width: 238px;
        max-height: 318px;
        position: relative;
        .person-lab {
          position: absolute;
          bottom: 20px;
          width: 100%;
          display: flex;
          justify-content: center;
          div { 
            width: 145px;
            height: 36px;
            line-height: 36px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            border: 1px solid #FFFFFF;
            font-size: 14px;
            font-weight: 500;
            color: #FFFFFF;
            cursor: pointer;
            text-align: center;
            backdrop-filter: blur(10px);
          }
        }
    }

    .content-html {

        // 滚动条整体样式
        &::-webkit-scrollbar {
            display: none !important;
            width: 4px !important;
            /* 纵向滚动条 宽度 */
            border-radius: 5px;
            /* 整体 圆角 */
        }

        //轨道部分
        &::-webkit-scrollbar-track {
            background: #fff !important;
        }

        // 滑块部分
        &::-webkit-scrollbar-thumb {
            background: #D4DBE0;
            min-height: 167px;
            width: 2px !important;
            border-radius: 5px;
        }

        scrollbar-width: thin !important;
        /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
        -ms-overflow-style: none !important;
        /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
    }

    .attention {
        width: 239px;
        height: 36px;
        border-radius: 4px;
        border: 1px solid #006EFF;
        color: #006EFF;
    }

    .un-attention {
        width: 239px;
        height: 36px;
        border-radius: 4px;
        border: 1px solid #006EFF;
        color: #006EFF;
    }

}
</style>
