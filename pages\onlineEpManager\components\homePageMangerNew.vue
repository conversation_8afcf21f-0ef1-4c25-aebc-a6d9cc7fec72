<template>
  <div class="homePageMangerContainer">
    <div class="flex jc-between ai-center">
      <div class="flex ai-center fa-55 f-w-400 color-999 font-14">
        <span>主页信息</span>
        <span class="mx-10">/</span>
        <span>实验室信息</span>
      </div>
      <el-button type="text" @click="toCompany" style="padding:0">
        企业主页<i class="el-icon-arrow-right el-icon--right"></i>
      </el-button>
    </div>

    <div class="mt-12 tips flex ai-center">
      <i class="el-icon-warning mr-8" style="color: #006EFF;font-size: 20px;"></i>
      <span class="font-14 fa-55 color-666 f-w-400 ellipsis">温馨提示： 每XXX只能修改XX次实验室信息，未审核成功不计算次数</span>
    </div>
    <el-form ref="form" :model="form" label-width="120px" class="mt-20" :rules="rules">
      <el-form-item label="实验室皮肤" prop="switchTopic">
        <div class="flex">
          <div class="flex-col ai-center jc-center mr-20" v-for="(item, index) in skinList" :key="index">
            <img :src="item.img" class="skin-img" @click="form.switchTopic = item.value">
            <el-radio v-model="form.switchTopic" :label="item.value" class="mt-8">{{ item.name }}</el-radio>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="企业LOGO" prop="logoUrl">
        <template v-if="!form.logoUrl">
          <div class="upload-border upload-1 flex-col ai-center jc-center" @click="openDialog(2)">
            <i class="el-icon-plus color-main font-24"></i>
            <span class="font-12 f-w-400 fa-55 color-upload">上传照片</span>
          </div>
          <div class="fa-55 f-w-400 color-999 font-12  lh-18">上传企业LOGO，建议图片尺寸比例1:1</div>
        </template>
        <template v-else>
          <div class="upload-1 cursor" @click="openDialog(2)">
            <img :src="logoPath" style="width: 100%;height:100%;">
          </div>
        </template>
      </el-form-item>

      <el-form-item label="企业宣传视频" prop="videoUid">
        <template v-if="!form.videoUid">
          <div class="upload-border upload-2 flex-col ai-center jc-center" @click="openDialog(1)">
            <i class="el-icon-plus color-main font-24"></i>
            <span class="font-12 f-w-400 fa-55 color-upload">上传视频</span>
          </div>
          <div class="fa-55 f-w-400 color-999 font-12 lh-18">上传企业宣传视频，建议比例16:9</div>
        </template>
        <template v-else>
          <div class="upload-2 cursor" @click="openDialog(1)">
            <video :src="videoPath" style="width: 100%;height:100%;" controls />
          </div>
        </template>
      </el-form-item>

      <el-form-item label="实验室名称" prop="name" style="width: 500px;">
        <el-input v-model="form.name" placeholder="请输入实验室名称" maxlength="20" show-word-limit size="medium" clearable />
      </el-form-item>

      <el-form-item label="企业地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入企业地址" style="width:815px;" size="medium" clearable>
        </el-input>
      </el-form-item>

      <el-form-item label="联系方式" prop="phone" style="width:500px;">
        <el-input v-model="form.phone" placeholder="请输入联系方式" size="medium" clearable></el-input>
      </el-form-item>

      <el-form-item label="邮箱地址" prop="email">
        <el-input v-model="form.email" placeholder="请输入企业邮箱" style="width:815px;" size="medium" clearable>
        </el-input>
      </el-form-item>

      <el-form-item label="企业分类" prop="category" style="width: 500px;">
        <el-select v-model="form.category" placeholder="请选择" style="width: 100%;" size="medium" clearable>
          <el-option v-for="(item, index) in cateGoryList" :key="index" :label="item.name" :value="item.uid"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="企业简介" prop="intro">
        <div style="width: 815px">
          <wangEditor height="150px" :key="Date.now()" v-model="form.intro"
            :editorConfig="{ placeholder: '请输入企业简介，建议输入120-300个字' }"></wangEditor>
        </div>
      </el-form-item>

      <el-form-item label="是否展示" prop="switch">
        <el-switch v-model="form.status" :active-value="1" :inactive-value="0">
        </el-switch>
      </el-form-item>

      <el-form-item label=" ">
        <hyButton type="primary" size="small" width="120px" height="36px" @click="submit">提交修改</hyButton>
        <hyButton type="info" size="small" width="120px" height="36px" class="ml-10">取消</hyButton>
      </el-form-item>
    </el-form>

    <hyMaterial :choiceDialog="dialogShow" @updateFlag="updateFlag" :type="dialogType" @currentObj="currentImg">
    </hyMaterial>
  </div>
</template>
<script>
export default {
  name: 'homepageManger',
  meta: { title: '实验室信息' },
  data () {
    return {
      form: {
        switchTopic: 1,
        logoUrl: '',
        videoUid: '',
        name: '',
        address: '',
        phone: '',
        email: '',
        category: '',
        status: 1
      },
      rules: {
        switchTopic: [{ required: true, message: '请选择皮肤', trigger: ['blur', 'change'] }],
        logoUrl: [{ required: true, message: '请上传企业logo', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入实验室名称', trigger: ['blur', 'change'] }],
        category: [{ required: true, message: '请选择企业分类', trigger: ['blur', 'change'] }],
        intro: [{ required: true, message: '请输入企业简介', trigger: ['blur', 'change'] }],
      },
      skinList: [{
        img: require('@/assets/newVersion/companyManager/skin1.png'),
        value: 1,
        name: '默认'
      }, {
        img: require('@/assets/newVersion/companyManager/skin2.png'),
        value: 2,
        name: '政企'
      }, {
        img: require('@/assets/newVersion/companyManager/skin3.png'),
        value: 3,
        name: '学院'
      }, {
        img: require('@/assets/newVersion/companyManager/skin4.png'),
        value: 4,
        name: '国风'
      }, {
        img: require('@/assets/newVersion/companyManager/skin5.png'),
        value: 5,
        name: '科技'
      }],
      cateGoryList: [],
      dialogShow: false,
      dialogType: 2,
      logoPath: '',
      videoPath: ''
    }
  },
  async mounted () {
    await this.$nextTick(() => { })
    this.getCompanyInfo()
  },
  methods: {
    getCompanyInfo () {
      const param = { enterpriseUid: this.$route.query.c || this.$store.getters.companyUid }
      this.$request.lectureHall.getEnterpriseInfo(param).then(res => {
        if (res.data.code == 200) {

        }
      })
    },
    toCompany () {
      this.$router.push({
        path: '/enterpriseLaboratoryHomepage',
        query: { enterpriseUid: this.$route.query.c || this.$store.getters.companyUid }
      })
    },

    submit () {
      this.$refs.form.validate(valid => {
        if (!valid) return

      })
    },

    // 1视频，2图片
    openDialog (val) {
      this.dialogType = val
      this.dialogShow = true
    },
    // 关闭素材中心弹窗
    updateFlag (value) {
      this.dialogShow = value
    },

    // 选择素材
    currentImg (img) {
      if (img) {
        const { fileUrl, fileUid } = this.dialogType == 2 ? img : img[0]
        const url = fileUrl

        console.log('选择素材', url, fileUrl, fileUid)

        if (this.dialogType == 1) {
          this.form.videoUid = fileUid
          this.videoPath = url
        } else {
          this.form.logoUrl = fileUid
          this.logoPath = url
        }
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.tips {
  background: #EBF0F7;
  border-radius: 4px 4px 4px 4px;
  padding: 9px 12px;
}

.skin-img {
  width: 143px;
  height: 80px;
  cursor: pointer;
}

.upload-border {
  border: 1px dashed #D4DBE0;
  cursor: pointer;
  margin-bottom: 4px;
}

.upload-border:hover {
  border-color: $colorMain;
}

.upload-1 {
  width: 100px;
  height: 100px;
}

.upload-2 {
  width: 160px;
  height: 90px;
}

.color-upload {
  color: rgba(0, 0, 0, 0.4);
  line-height: 18px;
  margin-top: 10px;
}

.lh-18 {
  line-height: 18px;
}
</style>
