<template>
  <!-- 微信绑定手机号弹窗 -->
  <el-dialog
    class="dialog"
    title="微信绑定手机号"
    :visible.sync="showDialog"
    width="500px"
    :close-on-click-modal="false"
    :modal="true"
  >
    <el-form :model="bindForm" ref="bindForm" :rules="bindRules">
      <el-form-item prop="mobile">
        <el-input
          class="form-input"
          type="text"
          v-model="bindForm.mobile"
          placeholder="手机号"
          clearable
        ></el-input>
      </el-form-item>
      <!-- 微信扫码注册暂不需要设置密码 -->
      <!-- <el-form-item prop="password">
        <el-popover
          placement="left"
          title="密码提示"
          width="350"
          trigger="focus"
          content="您设置的密码需要同时包含大写字母，小写字母，数字，以及特殊字符（#?!@$^&*-.+），密码长度为9-16位。">
            <el-input
            slot="reference"
            class="form-input"
            type="password"
            autocomplete="new-password"
            v-model="bindForm.password"
            placeholder="设置密码：大写+小写+数字+特殊字符9-16位"
            show-password
            clearable
          ></el-input>
        </el-popover>
      </el-form-item>
      <el-form-item prop="checkPassword">
        <el-input
          class="form-input"
          type="password"
          v-model="bindForm.checkPassword"
          placeholder="再次设置密码"
          autocomplete="new-password"
          show-password
          clearable
        ></el-input>
      </el-form-item> -->
       <el-form-item prop="varifycode">
        <div class="flex-row">
          <el-input
            v-model="bindForm.varifycode"
            class="form-input code"
            type="text"
            placeholder="验证码"
            clearable
          ></el-input>
          <el-button
            :class="[
              'code-btn',
              'flex-center',
              'cursor',
              canGetCode ? 'isget' : '',
            ]"
            :loading="sendLoading"
            @click="sendCode"
          >
            <span v-if="canGetCode">获取验证码</span>
            <span v-else style="color: #999">{{ codeCountDown }}s重新发送</span>
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        :loading="loading"
        type="primary"
        style="width: 100%;letter-spacing: 4px;"
        @click="submitBind"
        >绑定</el-button
      >
      <div class="clause flex-row ai-center">
        <i
          v-if="checkClause"
          class="el-icon-success cursor"
          style="font-size: 16px; color: #006eff"
          @click="checkClause = false"
        ></i>
        <i
          v-else
          class="el-icon-circle-check cursor"
          style="font-size: 16px"
          @click="checkClause = true"
        ></i>
        <span style="margin-left: 5px">我已审慎阅读并同意</span>
        <router-link
          class="file cursor"
          target="_blank"
          :to="{ path: '/notice/disclaimer' }"
          >《版权和隐私条款》</router-link
        >
      </div>
    </div>
  </el-dialog>
</template>

<script>
let timer = null;

export default {
  name: "bindDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    // 验证手机号
    const validateMobile = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请您输入正确的手机号"));
      } else {
        if (this.$store.state.sqlReg.test(value)) {
          callback(new Error("请不要输入特殊字符"));
        } else {
          if (this.$store.state.mobileReg.test(value)) {
            callback();
          } else {
            callback(new Error("请您输入正确的手机号"));
          }
        }
      }
    };
    // 验证密码
    const validatePassword = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请设置您的密码"));
      } else if (this.$store.state.passwordReg.test(value) === false) {
        callback(
          new Error("请您设置9-16位数的密码，包含数字、大小写字母、特殊字符")
        );
      } else {
        callback();
      }
    };
    // 验证确认密码
    const confirmPassword = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请您再次输入密码"));
      } else if (value !== this.bindForm.password) {
        callback(new Error("请您输入一致的密码"));
      }
      callback();
    };
    return {
      showDialog: false,
      bindForm: {
        mobile: "",
        varifycode: "",
        validCodeUuid: "",
        password: "",
        checkPassword: "",
        weixinopenid: "",
      },
      bindRules: {
        mobile: [
          {
            validator: validateMobile,
            trigger: ["blur", "change"],
          },
        ],
        varifycode: [
          {
            required: true,
            message: "请输入您的验证码",
            trigger: ["blur", "change"],
          },
        ],
        // password: [
        //   {
        //     validator: validatePassword,
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // checkPassword: [
        //   {
        //     validator: confirmPassword,
        //     trigger: ["blur", "change"],
        //   },
        // ],
      },
      canGetCode: true,
      codeCountDown: 60,
      sendLoading: false,
      loading: false,
      checkClause: false,
    };
  },
  watch: {
    visible(val) {
      this.showDialog = val;
    },
  },
  created() {
    this.showDialog = true
    if (timer) clearInterval(timer);
  },
  methods: {
    // 发送验证码
    sendCode() {
      this.$refs.bindForm.validateField("mobile", (errorMsg) => {
        if (!errorMsg) {
          if (!this.canGetCode) return;
          this.sendLoading = true
          
          this.$axios({
            method: "get",
            url: this.$api.sendCode,
            params: { reqParam: this.$JsEncrypt(this.bindForm.mobile), sendCodeType: this.$store.state.sendCodeType['WX_SACN_CODE_BING_MOBILE'] },
          }).then((res) => {
            // this.$message.success(res.data.message);
            this.bindForm.validCodeUuid = res.data.data.validCodeUuid
            this.canGetCode = false
            this.sendLoading = false
            clearInterval(timer);
            this.codeCountDown = 60;
            timer = setInterval(() => {
              if (this.codeCountDown > 0) {
                this.codeCountDown -= 1;
              } else {
                this.canGetCode = true;
                this.codeCountDown = 60;
                clearInterval(timer);
              }
            }, 1000);
          }).catch(err => {
            this.canGetCode = true
            this.sendLoading = false
            this.$message.error({ message: err.data.message, duration: 1500 });
          });
        }
      });
    },
    // 微信登录绑定手机号
    submitBind() {
      this.$refs.bindForm.validate((valid) => {
        if (valid) {
          if (!this.checkClause) {
            this.$message.warning("请先勾选同意版权和隐私条款");
            return;
          }
          this.loading = true;
          let params = {};
          params.mobile = this.$JsEncrypt(this.bindForm.mobile);
          // params.passWord = this.$JsEncrypt(this.bindForm.password);
          params.varifycode = this.bindForm.varifycode;
          params.validCodeUuid = this.bindForm.validCodeUuid;
          params.wxOpendId = this.$store.state.wxOpenid;
          this.$axios({
            method: "post",
            url: this.$api.wxScanCodeBindMobile,
            data: params,
          }).then((res) => {
            this.loading = false;
            // this.$message.success({
            //   message: res.data.message,
            //   duration: 1000,
            // });
            this.$refs.bindForm.resetFields();
            this.$store.commit("changeUserInfo", res.data.data.userInfo);
            this.$store.commit("changeIsLogin", true);
            localStorage.setItem('token', res.data.data.Authorization)
            // localStorage.setItem('userInfo', JSON.stringify(res.data.data.userInfo))
            this.$store.commit("changeUserInfo", res.data.data.userInfo);
            this.$store.commit('setAuthenticationInfo', {
              userInfo: res.data.data.userInfo,
              token: res.data.data.Authorization
            });
            this.$bus(this).$emit("loginSuccess");
          }).catch(err => {
            this.loading = false
            this.$message.error({ message: err.data.message, duration: 1500 });
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
$hy-color: #006eff;
.dialog {
  /deep/.el-dialog  {
    border-radius: 6px;
    .el-dialog__header {
      padding: 30px 40px 10px;
    }
    .el-dialog__headerbtn {
      top: 34px;
      right: 40px;
    }
    .el-dialog__body {
      padding: 20px 40px;
    }
    .el-dialog__footer {
      padding: 0px 40px 30px;
    }
  }
  .dialog-footer {
    /deep/.el-button--primary {
      background-color: $hy-color;
    }
    .clause {
      font-size: 12px;
      color: #666;
      margin-top: 10px;
      .file {
        color: $hy-color;
        text-decoration: none;
      }
    }
    .clause:hover {
      color: $hy-color;
    }
  }
  .form {
    margin-top: 20px;
  }
  .code-btn {
    width: 120px;
    height: 46px;
    background-color: #f4f6f7;
    border-radius: 3px;
    margin-left: 10px;
    overflow: hidden;
    font-size: 14px;
    color: $hy-color;
    border: 1px solid transparent;
  }
  .code-btn.isget:hover {
    border-color: $hy-color;
  }
  .form-input.code {
    flex: 1;
  }
  .form-input {
    /deep/.el-input__inner {
      height: 46px;
      background-color: #f4f6f7;
      border-color: transparent;
      color: $hy-color;
    }
    /deep/.el-input__inner:hover {
      border-color: #d4dbe0;
    }
    /deep/.el-input__inner:focus {
      border-color: $hy-color;
      background-color: #fff;
    }
  }
  .form-code {
    width: 100%;
    height: 46px;
  }
}
</style>
