<template>
  <div class="konwledgeTarget">
    <div v-if="filterList.length > 0">
      <div class="top flex-space-between mt-20">
        <div class="top-right">
          <hySubsectionNum width="315px" :fixedItemWidthEnable="true" itemWidth="129px" height="36px"
            :typeSwitchArr="filterList" :typeFlag.sync="active" @change="levelChange" :disabled="false">
          </hySubsectionNum>
        </div>
      </div>
      <div class="center w-100 mt-20 flex flex-wrap jc-start">
        <div class="item-block flex-center-col" v-for="item in selectList" :key="item.uid">
          <img class="head" :src="item.avatar ? item.avatar : $store.state.defaultAvater">
          <p class="title mt-30 font16">{{ item.name }}</p>
        </div>
      </div>
    </div>
    <div class="body" v-else>
      <el-empty style="margin-top: 20px;" description="暂无数据"></el-empty>
    </div>
  </div>
</template>

<script>
export default {
  name: 'konwledgeTarget',
  components: {

  },
  data() {
    return {
      active: 0,
      filterList: [],
      selectList: [],
      listPage: {
        curriculumName: '',
        current: 1,
        pageSize: 10,
      },
    }
  },
  props: {
    uid: {
      type: String,
      default: ''
    }
  },
  computed: {

  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.$debounce(() => {
          this.getTrainKnowledgeDetail()
        }, 200)();
      })
    },
    //获取知识点分类详情
    async getTrainKnowledgeDetail() {
      console.log('目标人群知识点Uid', this.uid)
      const { uid } = this
      try {
        const { data } = await this.$axios.post(this.$api.gerTrainKnowledgeInfo, { uid, contentType: 5 })
        if (data.code === 200) {
          const { records } = data.data || {}
          let list = []
          records?.[0]?.info?.targetCrowdList.forEach(element => {
            list.push({
              uid: element.uid,
              id: element.uid,
              name: element.name,
              num: `(${element.position.length})`,
              label: `${element.name}`,
              icon: element.icon,
              position: element.position
            })
          });
          this.filterList = list
          this.selectList = list[0].position
          this.active = list[0].id
          console.log('获取知识分类详情', list)
        }
      } catch (err) {
        this.filterList = []
        console.log('获取知识分类详情失败', err)
      }
    },
    //获取目标行业下的岗位
    async getIndustryTarget() {
      const { uid } = this
      try {
        const { data } = await this.$axios.get(this.$api.getTrainTargetPeople, { params: { uid } })
        if (data.code === 200) {
          console.log('获取岗位成功', data)
        }
      } catch (err) {
        console.log('获取目标人群行业岗位失败', err)
      }
    },
    // 初始化查询
    initQuery() {
      this.listPage.current = 1
      this.selectList = []
    },
    //获取
    levelChange(v) {
      console.log('切换行业', v)
      this.active = v.id
      this.selectList = this.filterList[v.index]?.position
    }
  },
}
</script>

<style lang="scss" scoped>
.konwledgeTarget {
  width: 100%;

  .top {
    .top-right {
      /deep/ .swithTitle {
        span {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }

  .center {
    width: 100%;

    .item-block {
      width: 23%;
      height: 252px;
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #D4DBE0;
      margin-bottom: 20px;
      margin-right: 18px;

      .head {
        width: 109px;
        height: 109px;
        border-radius: 50%;
      }

      .title {
        color: #333333;
        font-weight: 500;
      }
    }
  }
}
</style>