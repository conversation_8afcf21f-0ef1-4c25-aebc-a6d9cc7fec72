import prefix from '@/service/prefix'

// 表单提交
export function trainVerifySignUpApply (data) {
  return this.$axios({
    url: `${prefix.huanyu_web}/trainVerifySignUp/apply`.getFullApiUrl(),
    data: data,
    method: 'post'
  })
}
// 发送验证码
export function trainVerifySignUpSendCode (params) {
  return this.$axios({
    url: `${prefix.huanyu_web}/trainVerifySignUp/sendCode`.getFullApiUrl(),
    params,
    method: 'get'
  })
}
// 文件上传
export function trainVerifySignUpUpFile (data) {
  return this.$axios({
    url: `${prefix.huanyu_file}/file/uploadFile`.getFullApiUrl(),
    data: data,
    method: 'post'
  })
}
// qr
export function trainVerifySignUa (data) {
  return this.$axios({
    url: `${prefix.huanyu_web}/trainVerifySignUp/ua`.getFullApiUrl(),
    data: data,
    method: 'get'
  })
}
// 报名类型
export function getListByDictType (data) {
  return this.$axios({
    url: `${prefix.huanyu_web}/sysDictData/getListByDictType?dictType=sign_up_type`.getFullApiUrl(),
    data: data,
    method: 'post'
  })
}
