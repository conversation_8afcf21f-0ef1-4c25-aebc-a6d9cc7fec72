<template>
  <div class="s-content">
    <div class="flex ai-center jc-between">
      <hySubsection v-if="type == 2" width="160px" height="36px" :typeSwitchArr="typeList" :typeFlag="activeType"
        @change="changeType" ref="section" itemWidth="80px" />
      <div v-else></div>
      <hySearchInput placeholder="请输入关键字" :inputVal.sync="name"
                        @realSearch="getData" width="280px" height="36px"></hySearchInput>
    </div>

    <div v-if="dataList && dataList.length > 0" class="flex-wrap flex ai-center jc-start">
      <div v-for="(item, index) in dataList" :key="item.uid" class="data-item mt-15 flex-col jc-between"
        :class="{ 'item-mx': (index - 1) % 3 == 0 || index == 1 }">
        <div style="width: 238px;height: 180px;overflow: hidden;">
          <img class="study-img h-100 w-100" :src="item.url">
        </div>
        <span class="ellipsis black-font">{{ item.name }}</span>
        <div class="flex ai-center jc-between">
          <el-button class="study-btn" type="primary" v-if="studyStatus != 2" @click="toStudy(item)">开始学习</el-button>
          <el-button class="study-btn" type="primary" plain @click="toPlan(item)">职业规划</el-button>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" class="mt-40"></el-empty>
  </div>
</template>
<script>

export default {
  props: {
    type: {
      type: String,
      default: '1'
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      name: '',
      activeType: 1,
      studyStatus: '',
      typeList: [
        { id: 1, label: '培训中' },
        { id: 2, label: '已完成' },
      ],
    };
  },
  computed: {

  },
  mounted() {
  },
  methods: {
    init() {
      this.name = ''
      this.getData()
    },
    getData() {
      const param = {
        name: this.name,
        currentPage: 1
      }
      if (this.type == 2) {
        param.studyStatus = this.studyStatus
      }
      this.$emit('search', param)
    },

    changeType(val) {
      this.studyStatus = val.id
      this.getData()
    },

    // 开始学习
    toStudy(data) {
      this.$router.push({
        path: "/certification/certificateDetail",
        query: {
          uid: data.categoryUid,
          activeName: 'plan'
        },
      });
    },
    // 跳转页面通过路由地址
    gotoPageByPath(path, data) {
      this.$router.push({
        path,
        query: data
      })
    },
    // 职业规划
    toPlan(data) {
      if (!data.curriculumType) {
        this.$message.warning('暂无职业规划，敬请期待')
        return
      }
      this.$router.push({
        path: '/userCenter/careerPlan',
        query: data
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.item-mx {
  margin-left: 57px;
  margin-right: 57px;
}

/deep/ .el-input--small {
  height: 36px;
}
</style>
