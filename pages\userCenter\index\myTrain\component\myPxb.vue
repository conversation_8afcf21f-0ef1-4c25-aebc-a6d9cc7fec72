<!-- 我的培训班组件 -->
<template>
  <div class="myPxb">
    <!-- 导航 -->
    <div class="pxb-nav">
      <div @click="navChange(item)" :class="listPage.type === item.value ? 'is-active' : ''"
        v-for="(item, index) in navList" :key="index">{{ item.name }}</div>
    </div>
    <!-- 列表 -->
    <div class="course-wrap">
      <div v-if="selectList && selectList.length" class="course-list flex-row flex-wrap font-14">
        <div :class="['course-item', 'cursor', (index + 1) % 3 == 0 ? 'item-3' : '']" v-for="(item, index) in selectList"
          :key="index">
          <el-image v-if="item.fileUrl" class="course-img" :src="item.fileUrl"></el-image>
          <el-image v-else class="course-img" src="/images/emptyImg.jpg"></el-image>
          <div class="course-bottom">
            <div class="flex ai-center">
              <div class="cb-title ellipsis" :title="item.name">{{ item.name }}</div>
              <el-tag v-if="item.registerStatus === 1" class="shz" type="success" size="small">审核中</el-tag>
              <el-tag v-if="item.registerStatus === 2" class="shtg" type="success" size="small">审核通过</el-tag>
              <el-tag v-if="item.registerStatus === 3" class="yjs" type="success" size="small">未通过</el-tag>
              <el-tag v-if="item.registerStatus === 4" class="pxz" type="success" size="small">培训中</el-tag>
              <el-tag v-if="item.registerStatus === 5" class="yjs" type="success" size="small">结束</el-tag>
            </div>
            <div class="color-999 font-12 mt-5 mb-20">
              <span>培训时间</span>
              <span class="ml-16">{{ $formatDate(new Date(item.trainingStartTime), "YY-mm-dd") }} - {{ $formatDate(new
                Date(item.trainingEndTime), "YY-mm-dd") }}</span>
            </div>
            <div v-if="item.registerStatus === 1" @click="toDetail(item)" style="color: #999;"><el-button type="primary">查
                看</el-button></div>
            <div v-if="item.registerStatus === 2" @click="toPay(item)" style="color: #999;"><el-button
                type="primary">去付款</el-button></div>
            <div v-if="item.registerStatus === 3" @click="toDetail(item)" style="color: #999;"><el-button
                type="primary">重新提交</el-button></div>
            <div v-if="item.registerStatus === 4" @click="toDetail(item)" style="color: #999;"><el-button
                type="primary">去学习</el-button></div>
            <!-- <div v-if="item.registerStatus === 5" @click="toPay(item)" style="color: #999;"><el-button type="primary">续费</el-button></div> -->
            <div v-if="item.registerStatus === 5" style="color: #999;opacity: 0;height: 39px;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="selectList.length > 0" class="flex ai-center jc-between py-15">
      <div class="color-999 font-14">每页 {{ listPage.pageSize }} 条，共 {{ total }} 条数据</div>
      <el-pagination background layout="prev, pager, next" style="margin-right: -10px;"
        @current-change="handleCurrentChange" :current-page="listPage.page" :page-size="listPage.pageSize"
        :total="total"></el-pagination>
    </div>
    <!-- 空状态 -->
    <div v-if="selectList.length <= 0" class="my-50 flex-col ai-center">
      <img v-lazy="require('~/assets/empty/usercenter-training-empty.png')" alt="" style="width: 260px;height: 160px;">
      <div class="color-666 font-14 mt-20">
        暂无数据
      </div>
    </div>

    <!-- 重新提交弹窗 -->
    <template v-if="resetSaveObj.DialogVisible">
      <signUp :DialogVisible="resetSaveObj.DialogVisible" isReWrite :isResetStatus="0" :classUid="resetSaveObj.classUid"
        @cancel="cancel"></signUp>
    </template>
  </div>
</template>

<script>
import signUp from '../../../../certification/component/signUp.vue'
export default {
  name: 'myPxb',
  components: { signUp },
  data() {
    return {
      navList: [{ name: '审核中', value: 1 }, { name: '审核通过', value: 2 }, { name: '培训中', value: 4 }, { name: '未通过', value: 3 }, { name: '已结束', value: 5 }],
      selectList: [],
      total: 0,
      listPage: {
        currentPage: 1,
        pageSize: 9,
        type: 1
      },
      //重新提交
      resetSaveObj: {
        DialogVisible: false,
        classUid: "",
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    cancel() {
      this.resetSaveObj.DialogVisible = false;
      this.getList();
    },
    async resetSaveBtn(item) {
      // 其他人代报名
      if (item.reasonStatus == 1) {
        this.$alert('你的好友已帮你报名，并成功付款，你已成功加入培训班，请忽略此条报名信息', '温馨提示', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
        return;
      }
      this.resetSaveObj.classUid = item.uid;
      this.resetSaveObj.DialogVisible = true;
    },
    // 跳转支付
    toPay(item) {
      this.$store.commit('setCourseBreadcrumb', [{ label: '个人中心' }, { label: '我的培训' }]);
      this.$router.push({ path: "/certification/pay", query: { suid: item.signUid } })
    },
    //获取列表
    getList() {
      this.$axios({ url: this.$api.getTrainList, params: this.listPage, method: 'get' }).then(res => {
        if (res.data.code == 200) {
          this.selectList = res.data.data.records
          this.total = res.data.data.total
        } else {
          this.selectList = []
          this.total = 0
        }
      }).catch(err => { })
    },
    toDetail(item) {
      this.$router.push({
        path: "/certification/certificateDetail",
        query: {
          uid: item.certificateUid,
          activeName: 'plan'
        },
      });
    },
    //nav切换
    navChange(item) {
      this.listPage.type = item.value;
      this.listPage.currentPage = 1;
      this.getList()
    },
    //切换页数
    handleCurrentChange(newSize) {
      this.listPage.currentPage = newSize;
      this.getList();
    }
  }
};
</script>

<style lang="scss" scoped>
//nav导航样式
.pxb-nav {
  background-color: #f4f6f7;
  padding: 4px;
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  user-select: none;
  cursor: pointer;

  div {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    width: 88px;
    height: 36px;
    color: #999999;
    transition: all 0.3s;
  }

  .is-active {
    color: #006eff !important;
    background-color: #ffffff;
  }
}

// 列表样式
.course-wrap {
  .course-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .course-item {
      margin-right: 45px;
      border-radius: 6px 6px 6px 6px;
      overflow: hidden;
      margin-top: 16px;
      background: #ffffff;
      width: 280px;

      .course-img {
        width: 100%;
        height: 210px;
        vertical-align: top;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        transition: opacity 0.5s;
      }

      .course-bottom {
        border: 1px solid #e6eaed;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
        padding: 16px;
      }

      .shz {
        color: #006EFF;
        background-color: rgba(0, 110, 255, 0.1);
        margin-left: 8px;
      }

      .shtg {
        color: #22AD36;
        background-color: rgba(83, 206, 101, 0.1);
        margin-left: 8px;
      }

      .yjs {
        color: #333333;
        background-color: #F4F6F7;
        margin-left: 8px;
      }

      .pxz {
        color: #F77900;
        background-color: hsla(29, 100%, 48%, 0.1);
        margin-left: 8px;
      }
    }

    .cb-title {
      color: #333333;
      font-size: 16px;
      font-weight: 500;
    }

    .course-item:hover {
      .course-bottom {
        .cb-title {
          color: #006eff;
        }
      }

      .course-img {
        opacity: 0.7;
      }
    }

    .course-item.item-3 {
      margin-right: 0;
    }
  }
}
</style>
