<template>
  <div class="user-content">
    <el-form class="user-form" ref="form" :model="form" :rules="rules">
      <el-form-item label="文章标题" class="full-item" prop="title">
        <template slot="label">
          <span>文章标题</span>
          <span class="ml-10">{{ form.title.length }}/60</span></template>
        <div class="article-title-box" :style="{ border: titleVerifyFlag ? '' : '1px solid #F56C6C' }">
          <div class="topic" v-if="topicArticleFlag">
            <i style='color:#4290f7;'>#</i><span style='color:#b9b9b9;margin-left:6px;'>{{ topicInfo.content }}</span>
            <div class="tag">{{ topicInfo.blogSortName }}</div>
          </div>
          <el-input class="article-title" type="textarea" v-model="form.title" minlength="1" maxlength="60"
            placeholder="请输入文章标题(1-60字)">
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="文章标题描述" class="full-item" prop="summary">
        <el-input type="textarea" v-model="form.summary" minlength="5" maxlength="200" placeholder="请输入文章标题描述">
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="文章封面" class="full-item" prop="coverImagePath">
        <el-upload class="avatar-uploader" :show-file-list="false" :action="$api.fileCheckPictures"
          :on-error="_uploadImgError" :before-upload="_beforeUpload" :on-success="_uploadImgSuccess"
          :on-remove="_uploadImgRemove" :auto-upload="true" :data="imgUploadParams" :headers="imgUploadHeaders">
          <div class="upload-icon-wrap">
            <img v-if="form.coverImagePath" :src="form.coverImagePath" style="width: 100%; height: 100%" />
            <div v-else class="upload-icon-wrap">
              <i class="el-icon-picture avatar-uploader-icon"></i>
              <span>点击添加封面图片</span>
            </div>
          </div>
          <div class="el-upload__tip" slot="tip">
            建议尺寸750*560或比例4:3，小于5M的JPG、PNG格式图片
          </div>
        </el-upload>
      </el-form-item> -->
      <el-form-item label="封面图" class="full-item" prop="coverImagePath">
        <div class="choice-img-area font-12 color-999">
          <div class="img-div flex-col jc-center ai-center" @click="openChoice">
            <template v-if="!form.coverImagePath">
              <i class="iconfont icon-add1 color-main"></i>
              <span style="color: rgba(0,0,0,0.4);">选择图片</span>
            </template>
            <template v-else>
              <img v-lazy="form.coverImagePath" alt=""
                style="width: 100%;height: 100%;object-fit: fill;">
            </template>
          </div>
          <span class="color-999 font-14 mt-10">建议尺寸750*560或比例4:3</span>
        </div>
      </el-form-item>
      <el-form-item label="文章详情" class="full-item" prop="content">
        <wangEditor height="500px" :key="timestamp" v-model="form.content"></wangEditor>
      </el-form-item>
      <el-form-item label="发布栏目">
        <el-select v-model="form.publishColumnUid" :clearable="true" placeholder="请选择" @change="changePublishColumn"
          @clear="form.fromSystemUid = ''">
          <el-option :label="item.name" :value="item.uid" v-for="(item, index) in systemList" :key="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="来自体系">
        <el-select v-model="form.fromSystemUid" :clearable="true" :disabled="isFromSystemUid"
          :placeholder="isFromSystemUid ? '请先选择发布栏目' : '选择来自体系'">
          <el-option :label="item.name" :value="item.uid" v-for="(item, index) in columnList" :key="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="知识话题">
        <el-cascader v-model="form.knowledgeTopic" :options="knowledgeList" @change="handleChange" :props="{
          value: 'uid',
          label: 'sortName',
          children: 'blogSortList'
        }"></el-cascader>
      </el-form-item>
      <!-- <el-form-item label="文章标签：" label-width="110px" prop="tagUid">
        <el-select v-model="dynamicTagsSelect" multiple placeholder="请选择标签" @change="tagsChange"
          :multiple-limit="tagsLimit - dynamicTags.length" :disabled="tagsLimit === dynamicTags.length"
          @remove-tag="removeTag">
          <el-option v-for="item in dynamicTagsAll" :key="item.uid" :label="item.content" :value="item.content">
          </el-option>
        </el-select>
        <span class="ml-10">自定义标签</span>
        <el-tag :key="idx" v-for="(tag, idx) in dynamicTags" closable :disable-transitions="false"
          @close="handleClose(tag)">
          {{ tag }}
        </el-tag>
        <el-input class="input-new-tag" v-if="inputVisible" v-model="inputValue" ref="saveTagInput" size="small"
          @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
        </el-input>
        <el-button v-if="!inputVisible && !inputDisable" class="button-new-tag" size="small" @click="showInput">+
          添加自定义标签
        </el-button>
        <span class="ml-40">{{ isSelectNumb }}/{{ tagsLimit }}</span>
      </el-form-item> -->
      <!-- 如果未携带tpuid -->
      <!-- <el-form-item v-if="!topicArticleFlag" label="分类栏目：" label-width="110px" prop="blogSortUid" required>
        <el-select v-model="form.blogSortUid" placeholder="选择分类">
          <el-option :label="item.sortName" :value="item.uid" v-for="item in typeColumns" :key="item.uid">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item v-if="!topicArticleFlag" label="文章类型：" label-width="110px" prop="isOriginal" required>
        <el-radio-group v-model="form.isOriginal" class="common-radio">
          <el-radio label="1">原创</el-radio>
          <el-radio label="0">转载</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="内容属性：" label-width="110px" prop="hasArt">
        <el-select v-model="form.hasArt" clearable class="mr10 el-select-blogSort" placeholder="请选择内容属性">
          <el-option value="0" label="资讯类" />
          <el-option value="1" label="技术类" />
        </el-select>
      </el-form-item>
      <div class="releasePosition">
        <el-form-item v-if="!topicArticleFlag" label="发布位置：" label-width="110px" prop="releaseLocation" required>
          <el-radio-group v-model="form.releaseLocation" class="common-radio">
            <el-radio :label="'0'">个人</el-radio>
            <el-radio :label="'1'" v-if="userAuth">企业</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.releaseLocation == 1" label="" label-width="20px" prop="enterpriseUid" required>
          <el-select v-model="form.enterpriseUid" placeholder="选择企业">
            <el-option v-for="(item, index) in userJoinCompanyArr" :key="index" :label="item.labName"
              :value="item.companyUid"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <el-form-item v-if="!topicArticleFlag" label="发布设置：" label-width="110px" prop="releaseSet" required>
        <el-radio-group v-model="form.releaseSet" class="common-radio">
          <el-radio label="0">立即发布</el-radio>
          <!-- <el-radio label="2">定时发布</el-radio> -->
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.releaseSet == '2'" label="发布时间：" label-width="110px" prop="setReleaseTime" required>
        <el-date-picker v-model="form.setReleaseTime" type="datetime" placeholder="选择日期时间"
          value-format="yyyy-MM-dd HH:mm:ss" @change="onChagen">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="!topicArticleFlag" label="发布形式：" label-width="110px" prop="releaseType" required>
        <el-radio-group v-model="form.releaseType" class="common-radio">
          <el-radio label="0">全部可见</el-radio>
          <el-radio label="1">仅我可见</el-radio>
          <el-radio label="2">粉丝可见</el-radio>
          <el-radio label="3" v-if="userAuth">对成员可见</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-button class="comfirm-btn" @click="submit" :loading="loading">
        {{ isEdit ? "修改" : "确定发布"
        }}{{ loading ? `(${loadingTick})` : "" }}</el-button>
      <el-button class="normal-btn" @click="showPreview"> 发布预览</el-button>
      <el-button class="normal-btn" @click="saveDraft(1)"> 存草稿</el-button>
    </el-form>
    <!-- 图片不合规提示 -->
    <el-dialog class="violate-dialog" title="提示" :visible.sync="dialogVisible" width="30%">
      <div>
        您上传的图片<span style="color: red">{{ imgViolationText }}</span>，请重新选择图片
      </div>
      <div v-if="imgViolationTips" style="color: #999; margin-top: 20px">
        违规信息提示：{{ imgViolationTips }}
      </div>
      <span slot="footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 文章不合规提示 -->
    <el-dialog class="violate-dialog" title="提示" :visible.sync="artDialogVisible" width="30%">
      <div>
        文章内容<span style="color: red">{{ artViolationText }}</span>，请修改
      </div>
      <div v-if="artViolationTips" style="color: #999; margin-top: 20px">
        违规信息提示：{{ artViolationTips }}
      </div>
      <span slot="footer">
        <el-button type="primary" @click="artDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <choiceImgorVideo :choiceDialog="choiceFlag" @updateFlag="updateFlag" :type="2" @currentObj="currentImg">
    </choiceImgorVideo>
    <!--文章预览组件-->
    <!-- <hyArticlePreview v-if="form.content.length !== 0" :key="updateKey" :isOriginal="form.isOriginal" :title="form.title"
      :sortName="sortName" :tagList="[]" :summary="form.summary" :content="form.content" :visible.sync="previewShow">
    </hyArticlePreview> -->
    <el-dialog title="预览文章" :visible.sync="previewShow" width="85%">
      <hyArticlePreview v-if="previewShow" :form="form" :knowledgeName="knowledgeName"> </hyArticlePreview>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewShow = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 初始化参数
const initForm = {
  topicUid: "",
  hasArt: '0',
  publishColumnUid: '',//发布栏目uid
  fromSystemUid: "",//来自体系uid
  knowledgeTopic: '',//知识话题uid
  enterpriseUid: "",
  releaseLocation: "", //发布位置(默认个人)
  coverImagePath: "",
  // blogSortUid: "", //博客分类UID
  content: "", //博客内容
  fileUid: "", //标题图片UID
  isOriginal: "", //是否原创（0:不是 1：是）
  isPublish: "1", //是否发布：0：否，1：是
  releaseSet: "0", //发布设置 0立即发布1保存草稿2定时发布,
  releaseType: "", //发布形式(0:全部可见；1：自己可见；2：粉丝可见)
  summary: "", //博客简介
  // tagUid: "", //标签uid
  title: "", //博客标题
  type: "0", //类型【0 博客， 1：推广】
  setReleaseTime: "", //设置发布时间
  //其他参数的预处理字段
  releaseLocationArray: ["0"], //发布位置的双向绑定，提交时候需要填充到"发布位置"中
};
import choiceImgorVideo from './choiceImgorVideo.vue'
import isEmpty from 'lodash/isEmpty';
export default {
  components: {
    choiceImgorVideo
  },
  computed: {
    isFromSystemUid() {
      return isEmpty(this.form.publishColumnUid) ? true : false
    },
    topicArticleFlag({ tpuid, form }) {
      return Boolean(tpuid || form.topicUid);
    }
  },
  data() {
    var validateCoverImg = (rule, value, callback) => {
      if (value === "") {
        if (this.isEdit) callback();
        callback(new Error("请上传文章封面图"));
      } else {
        callback();
      }
    };
    var validateLabel = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请添加文章标签"));
      } else {
        callback();
      }
    };
    var checkedReleaseTime = (rule, value, callback) => {
      if (this.form.releaseSet !== "2") callback();
      if (Date.now() >= new Date(value)) {
        callback(new Error("发布时间不能比现在早"));
      } else {
        callback();
      }
    };
    return {
      knowledgeName: "",//知识话题名称
      timestamp: '',
      titleVerifyFlag: true,
      updateKey: new Date().getTime(),
      tpuid: "",
      activeNav: "article",
      isClear: false,
      form: JSON.parse(JSON.stringify(initForm)),
      status: "",
      rules: {
        hasArt: [
          { required: true, trigger: 'blur', message: '请选择内容属性' }
        ],
        title: [
          {
            required: true, trigger: "blur", validator: (rule, value, callback) => {
              if (!value) {
                this.titleVerifyFlag = false;
                callback(new Error('请输入文章标题'));
              } else if (value?.length > 60) {
                this.titleVerifyFlag = false;
                callback(new Error('长度在 1 到 60 个字符'));
              } else {
                this.titleVerifyFlag = true;
                callback();
              }
            }
          }
        ],
        epUid: [{ required: true, message: "请选择企业", trigger: "blur" }],
        releaseLocation: [
          { required: true, message: "请选择发布位置", trigger: "blur" },
        ],
        summary: [
          { required: true, message: "请输入文章标题描述", trigger: "blur" },
        ],
        coverImagePath: [
          { required: true, validator: validateCoverImg, trigger: "change" },
        ],
        content: [
          { required: true, message: "请输入文章详情", trigger: "blur" },
        ],
        // blogSortUid: [
        //   { required: true, message: "请选择分类栏目", trigger: "change" },
        // ],
        tagUid: [{ required: true, validator: validateLabel, trigger: "blur" }],
        isOriginal: [
          { required: true, message: "请选择文章类型", trigger: "change" },
        ],
        releaseSet: [
          { required: true, message: "请选择发布设置", trigger: "change" },
        ],
        enterpriseUid: [
          { required: true, message: "请选择企业", trigger: "change" },
        ],
        releaseType: [
          { required: true, message: "请选择发布形式", trigger: "change" },
        ],
        setReleaseTime: [
          { required: true, message: "请选择发布时间", trigger: "blur" },
          { required: true, validator: checkedReleaseTime, trigger: "blur" },
        ],
      },
      typeColumns: [],
      systemList: [],//发布栏目
      columnList: [],//来自体系
      knowledgeList: [],//知识话题
      // dynamicTagsAll: [], //所有标签信息,后台获取
      // dynamicTagsSelect: [], //下拉框标签已选择
      // dynamicTags: [], //自定义标签
      tagsLimit: 4,
      isSelectNumb: 0,
      inputVisible: false,
      inputDisable: false,
      inputValue: "",
      loading: false, //先把loading去掉
      isEdit: false,
      dialogVisible: false,
      imgViolationText: "",
      imgViolationTips: "",
      artDialogVisible: false,
      artViolationText: "",
      artViolationTips: "",
      editorDOM: {},
      editContent: "",
      imgUploadParams: {
        filedatas: {},
        userUid: "",
        projectName: "blog",
        sortName: "web",
        source: "picture",
        token: "",
      },
      imgUploadHeaders: {
        Authorization: "",
      },
      choiceFlag:false,
      userInfo: {},
      userAuth: null,
      userJoinCompanyArr: [],
      previewShow: false,
      sortName: "",
      loadingTick: 30,
      loadingTickLoop: null,
      topicInfo: {}
    };
  },
  async created() {
    console.log('创建', this.form)
    this.getTypeColumn();
    this.userJoinCompanyList();
    this.getSelectList(1)
    // 赋值话题id
    this.tpuid = this.$route.query.tpuid;
  },
  async mounted() {
    // this.getTagList();
    this.userInfo = this.$store.state.userInfo || {};
    //初始化用户信息 到 图片上传参数
    this.imgUploadParams.userUid =
      this.$store.state.userInfo && this.$store.state.userInfo.uid || null;
    this.imgUploadParams.token = localStorage.getItem("token") || null;
    this.imgUploadHeaders.Authorization = localStorage.getItem("token") || null;
    console.log('获取详情', this.$route.query.uid)
    this.$route.query.uid && await this.contentGetBlogByUid(this.$route.query.uid);
    this.isEdit = this.$route.query.uid ? true : false;
    //获取话题信息
    this.form.topicUid || this.tpuid ? await this.getTopicArticle() : null;
    //如果是新增文章，先查看是否有上次暂存的文章信息
    if (!this.isEdit && localStorage.getItem("articleStorage")) {
      this.form = JSON.parse(localStorage.getItem("articleStorage"))
      console.log("content", this.form);
      if (this.form.publishColumnUid) {
        this.getSelectList(2, this.form.publishColumnUid)
      }
      localStorage.removeItem("articleStorage")
    }
    console.log('表单', this.form)
  },
  methods: {
    // 关闭素材中心弹窗
    updateFlag(value) {
      this.choiceFlag = value
    },
    // 选中的当前图片
    currentImg(img) {
      if (img) {
        const { fileUrl, fileUid } = img
        this.form.coverImagePath = fileUrl
        this.form.fileUid = fileUid
        this.$refs['form'].clearValidate('fileUid')
      }
    },
    //选择发布栏目
    changePublishColumn(e) {
      this.form.fromSystemUid = ''
      this.getSelectList(2, e)
    },
    //获取筛选数据
    async getSelectList(num, uid) {
      if (num == 1) {
        const res1 = await this.$axios.get(this.$api.getSelectColumn);
        if (res1.data.code == 200) {
          this.systemList = res1.data.data;
          console.log('获取发布栏目', this.systemList)
        }
        const res2 = await this.$axios.get(this.$api.getSelectBlogTopic);
        console.log('获取知识话题', res2)
        if (res2.data.code == 200) {
          //过滤掉没有二级分类的
          this.knowledgeList = res2.data.data.filter(option => option.blogSortList && option.blogSortList.length > 0);
        }
      }
      if (num == 2) {
        const res = await this.$axios.get(this.$api.getSelectColumnSystem, { params: { uid } });
        console.log('获取来自体系', res)
        if (res.data.code == 200) {
          this.columnList = res.data.data;
        }
      }
    },
    //知识体系二级选择
    handleChange(e) {
      // console.log('选择知识体系', e)
      this.knowledgeName = ''
      if (e.length > 0) {
        const leve1 = this.knowledgeList.filter(item => item.uid == e[0])
        const leve2 = leve1[0].blogSortList.filter(item => item.uid == e[1])
        this.knowledgeName = leve2[0].sortName
        // console.log('选择知识体系', leve1, leve2)
      }
      this.form.knowledgeTopic = e[e.length - 1]
    },
    // 存草稿
    saveDraft(type) {
      // 存草稿类型
      this.form.releaseSet = 1;
      // 提交
      this.submit();
    },
     // 打开素材中心弹窗
    openChoice() {
      this.choiceFlag = true
    },
    // 话题信息
    async getTopicArticle() {
      let result = await this.$axios({
        url: this.$api.getTopicArticle,
        method: "get",
        params: {
          topicUid: this.tpuid || this.form.topicUid,
        },
      });
      if (result.data.code == 200) {
        this.topicInfo = result.data.data;
      }
    },
    userJoinCompanyList() {
      //判断用户加入了哪些企业
      const data = {
        currentPage: 1,
        pageSize: 10
      }
      this.$axios({
        method: 'post',
        url: this.$api.queryMyLabInfo,
        params: data
      }).then((res) => {
        if (res.data.code == 200 && res.data.data.createList.length || res.data.data.joinList.length) {
          this.userJoinCompanyArr = [...res.data.data.createList].concat(res.data.data.joinList)
          this.userJoinCompanyArr = this.userJoinCompanyArr.filter((item) => { // 过滤掉没有companyUid的企业
            if (item.companyUid) {
              return item
            }
          })
          // 该用户下存在企业
          this.userAuth = true;
        }
      })
    },
    onChagen() {
      //console.log("sdfsdf", this.form.setReleaseTime);
    },
    // 创建富文本编辑器
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    // 切换发布类型
    handleClickNav(tab) {
      this.activeNav = tab.name;
    },
    // 获取分类栏目
    getTypeColumn() {
      this.$axios({
        method: "get",
        url: this.$api.getBlogSortList,
      }).then((res) => {
        this.typeColumns = res.data.data;
      });
    },
    // 上传封面图片
    _uploadImgError(err, file, fileList) {
      this.$message.error("文件上传失败，请重试！");
    },
    _uploadImgSuccess(res, file) {
      //console.log("res", res);
      //console.log("file", file);
      if (res.code == 200) {
        // 图片审核通过
        //console.log("ok");
        // if (status == 400) this.form.status = "4"; // 把待审核状态传给后端
        this.form.fileUid = res.data[0].uid;
        this.form.coverImage = res.data[0];
        // this.form.coverImagePath = URL.createObjectURL(file.raw);
        this.form.coverImagePath = localStorage.getItem("picBaseUrl") + res.data[0].picUrl;
        this.$refs.form.validateField("coverImagePath");
      } else if (res.code == 601) {
        this.$alert("图片疑似违规，请重新上传！", "提示", {
          confirmButtonText: "确定",
        });
      } else if (res.code == 500) {
        // 图片违规或疑似违规
      }
    },
    _beforeUpload(file) {
      this.imgUploadParams.filedatas = file;
      let types = ["image/jpg", "image/png", "image/jpeg"];
      const isJPG = types.includes(file.type);
      const isLt5M = file.size / 1024 / 1024 < 3;
      if (!isJPG) {
        this.$message.error("上传图片只能是 jpg或png 格式!");
      }
      if (!isLt5M) {
        this.$message.error("上传图片大小不能超过 3MB!");
      }
      return isJPG && isLt5M;
    },
    _uploadImgRemove(file, fileList) {
      this.form.coverImage = "";
      this.form.coverImagePath = "";
      this.$refs.form.validateField("coverImagePath");
    },
    locationFn() {
      //发布位置下拉框事件
      this.form.releaseLocation = this.form.releaseLocationArray.join(",");
    },
    // 确认发布
    async submit() {
      // let _that = this
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          //判断是编辑还是新增
          let api = this.$route.query.uid
            ? this.$api.contentEditBlog
            : this.$api.contentAddBlog;
          let {
            coverImagePath,
            coverImage,
            releaseLocationArray,
            ...currentForm
          } = this.form;
          //console.log("this.form.setReleaseTime", this.form.setReleaseTime);
          if (!this.form.setReleaseTime || this.form.setReleaseTime == "null") {
            //这里判断是否要设置定时发布时间，如果没有就设为''
            currentForm.setReleaseTime = "";
          }
          let params = this.$route.query.uid
            ? { ...currentForm, ...{ uid: this.$route.query.uid } }
            : currentForm;
          // 绑定话题分类
          // this.topicInfo.topicSortUid ? params.blogSortUid = this.topicInfo.topicSortUid : null;
          //绑定对应话题
          this.tpuid
            ? (params.topicUid = this.tpuid)
            : this.form.tpuid
              ? this.form.tpuid
              : null;
          // 话题文章默认参数
          if (this.topicArticleFlag) {
            params.isOriginal = 1;
            params.releaseSet = 0;
            params.releaseType = 0;
            params.releaseLocation = 0;
          }

          //base64加密上传的富文本内容

          params["content"] = this.$base64.encode(encodeURIComponent(params["content"]));
          this.loading = true;
          this.loadingTickFn();
          await this.$axios({
            method: "post",
            url: api,
            data: params,
          })
            .then((res) => {
              this.loading = false;
              //console.log("myres", res);
              if (res.data.code == 200) {
                // 添加或者编辑成功删除本地存储的数据
                localStorage.removeItem("articleStorage");
                this.$message.success(
                  this.$route.query.uid ? "编辑成功" : "发布成功"
                );
                this.$refs["form"].resetFields();
                // this.form.tagUid = "";
                this.dynamicTagsSelect = [];
                this.form.content = "";
                // this.editorDOM.txt.clear()
                //console.log("e12"); 
                //新增保存清空
                this.form.knowledgeTopic = ''
                this.form.fromSystemUid = ''
                this.form.publishColumnUid = ''

                if (res.data.data.status === 4) {
                  //提示疑似信息
                  this.$alert(
                    "文章存在疑似违规内容，已提交至后台审核，请您耐心等待！",
                    "提示",
                    {
                      confirmButtonText: "确定",
                      showClose: false,
                    }
                  ).then(() => {
                    this.$router.push({
                      path: "/userCenter/articleManage",
                      query: { release: res.data.data.status, autoJump: 1 },
                      params: {
                        autoJump: 1
                      }
                    });
                  });
                  //不点确定的话5秒进入详细页
                  setTimeout(() => {
                    this.$router.push({
                      path: "/userCenter/articleManage",
                      query: { release: res.data.data.status, autoJump: 1 },
                      params: {
                        autoJump: 1
                      }
                    });
                  }, 5000);
                  return;
                }
                if (!this.$route.query.uid && this.$route.query.redirect) {
                  this.$router.push({
                    path: decodeURIComponent(this.$route.query.redirect),
                    query: {
                      autoJump: 1
                    }
                  });
                } else {
                  this.$router.push({
                    path: "/userCenter/articleManage",
                    query: { release: res.data.data.status, autoJump: 1 },
                    params: {
                      autoJump: 1
                    }
                  });
                }
              } else if (res.data.code === 601) {
                let result = JSON.parse(res.data.data).data.data;
                //console.log("result", result);
                let violationText = "";
                this.artViolationTips = "";
                result.forEach((ret) => {
                  violationText += `${ret.msg}、`;
                  if (ret.hits) {
                    let tipsArr = ret.hits.map((item) => {
                      if (item.words.length) {
                        return item.words.join("、");
                      }
                    });
                    this.artViolationTips += `${tipsArr.join("、")}、`;
                  }
                  if (ret.stars) {
                    let tipsArr = ret.stars.map((item) => item.name);
                    this.artViolationTips += tipsArr.join("、");
                  }
                });
                this.artViolationTips = this.artViolationTips.substring(
                  0,
                  this.artViolationTips.length - 1
                );
                this.artViolationText = violationText.substring(
                  0,
                  violationText.length - 1
                );
                this.artDialogVisible = true;
              } else if (res.data.code === 201) {
                this.$alert(res.data.message, "提示", {
                  confirmButtonText: "确定",
                  showClose: false,
                });
              }
            })
            .catch((res) => {
              // this.resetTick()
              if (res.data.code === 201) {
                this.$alert(res.data.message, "提示", {
                  confirmButtonText: "确定",
                  showClose: false,
                });
              }
              this.loading = false;
            });
          this.resetTick();
        } else {
          return false;
        }
      });
    },
    //获取文章详情
    async contentGetBlogByUid(uid) {
      //用于编辑文章用
      await this.$axios
        .get(this.$api.contentGetBlogByUid, { params: { uid } })
        .then((res) => {
          // console.log("文章详情 res", res);
          try {
            if (res.data.code == 200) {
              let data = res.data.data;
              //填充数据到页面中
              Object.keys(this.form).forEach((i) => {
                //把返回数据中跟form表单相同的字段填充至form中
                this.form[i] = data[i] != undefined ? data[i] + "" : "";
              });
              this.timestamp = new Date().getTime();
              this.status = data.status;
              //填充标签
              // res.data.data.tagList.forEach((i) => {
              //   if (i.source === "ADMIN") {
              //     this.dynamicTagsSelect.push(i.content);
              //   }
              //   if (i.source === "USER") {
              //     this.dynamicTags.push(i.content);
              //   }
              // });
              console.log('111111asdasd ', this.form)
              if (this.form.publishColumnUid) {
                this.getSelectList(2, this.form.publishColumnUid)
              }
              //填充发布位置的绑定数组
              this.form.releaseLocation = this.form.releaseLocation * 1;
              this.form.releaseLocation !== "" &&
                (this.form.releaseLocationArray = (
                  this.form.releaseLocation + ""
                ).split(","));
              this.locationFn();
              //设置头像
              this.form.coverImagePath = data.photoUrl;
              console.log('表单回填', this.form)
              // this.checkTagsNumb();
            }
          } catch (err) {
            console.log('文章详情错误', err)
          }
        })
        .catch((err) => {
          // Message.warning(err.data.message)
        });
    },
    // 跳转文章详情
    toDetail(id) {
      this.$router.push({
        path: "/laboratory/blogDetail",
        query: { articleId: id },
      });
    },
    //文章预览
    showPreview() {
      // let a = this.typeColumns.find((i) => {
      //   return i.uid === this.form.blogSortUid;
      // });
      // this.sortName = a ? a.sortName : this.topicInfo.blogSortName ? this.topicInfo.blogSortName : "未设置的标签";
      this.previewShow = !this.previewShow;
    },
    //还原倒计时
    resetTick() {
      this.loadingTickLoop && clearInterval(this.loadingTickLoop);
      this.loadingTick = 30;
    },
    //按钮loading倒计时
    loadingTickFn() {
      let fn = () => {
        // console.log("this.loadingTick", this.loadingTick);
        if (this.loadingTick === 0) {
          this.loadingTickLoop && clearInterval(this.loadingTickLoop);

          this.loading = false;
          this.$message.warning("发布超时,请重新发布。");
          this.loadingTick = 30;
        } else {
          this.loadingTick--;
        }
      };
      this.loadingTickLoop && clearInterval(this.loadingTickLoop);
      this.loadingTickLoop = setInterval(fn, 1000);
    },
    /* 
      弃用函数
    */
    //合并标签
    setTagArray() {
      //把两组标签数据合成一组 :ADMIN 表示下拉框选择的   :USER表示用户手动添加的
      let arr1 = [];
      this.dynamicTagsSelect.forEach((i) => {
        arr1.push(`${i}:ADMIN`);
      });
      let arr2 = [];
      this.dynamicTags.forEach((i) => {
        arr2.push(`${i}:USER`);
      });
      return [...arr1, ...arr2].join(",");
    },
    removeTag() { },
    //检查标签数量
    checkTagsNumb() {
      this.form.tagUid = this.setTagArray();
      this.isSelectNumb =
        this.dynamicTagsSelect.length + this.dynamicTags.length;
      this.inputDisable = false;
      //判断当前已选标签的总数，等于4个就不可再添加
      if (this.dynamicTagsSelect.length + this.dynamicTags.length === 4) {
        this.inputDisable = true;
      }
    },
    //获取标签信息
    getTagList() {
      this.$axios
        .get(this.$api.tagGetTagList)
        .then((res) => {
          //console.log("resrseres", res);
          if (res.data.code == 200 || res.data.code == "success") {
            // console.log('111r',res.data);
            this.dynamicTagsAll = res.data.data;
          }
        })
        .catch((err) => { });
    },
    //删除自定义标签
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      // this.form.tagUid = this.dynamicTags.join(",");
      this.checkTagsNumb();
    },
    //输入自定义标签 
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    //输入自定义标签 
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
        this.checkTagsNumb();
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    //选择标签 
    tagsChange() {
      this.checkTagsNumb();
    },
  },
  beforeDestroy() {
    //实现暂存功能
    if (!this.isEdit) {
      localStorage.setItem("articleStorage", JSON.stringify(this.form))
    }
  }
};
</script>

<style lang="scss" scoped>
.user-content {
  margin-top: 20px;
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 20px;
}

/deep/ .wangEditorContent {
  img {
    max-width: 100% !important;
  }

  video {
    max-width: 100% !important;
  }

  blockquote {
    background-color: var(--w-e-textarea-slight-bg-color);
    border-left: 8px solid var(--w-e-textarea-selected-border-color);
    display: block;
    font-size: 100%;
    line-height: 1.5;
    margin: 10px 0;
    padding: 10px;
  }

  code {
    background-color: #f2faff !important;
    border: 0px solid #999 !important;
    // white-space: normal !important;
    // word-wrap: break-word !important;
    // overflow: hidden !important;
  }

  pre {
    box-sizing: border-box;
    background-color: #f2faff !important;
    border: 0px solid #999 !important;
    border-radius: 3px;
    display: block;
    width: 100%;
    padding: 10px;
    white-space: pre-wrap;
  }

  table {
    border-collapse: collapse;
  }

  table th {
    background-color: var(--w-e-textarea-slight-bg-color);
    font-weight: 700;
    text-align: center;
    padding: 5px 5px;
    min-height: 20px;

    span {
      text-align: center;
    }
  }

  table td,
  table th {
    border: 1px solid var(--w-e-textarea-border-color);
    line-height: 1.5;
    min-width: 30px;
    padding: 5px 5px;
    text-align: center;

    span {
      text-align: center;
    }
  }
}

.part-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16px;
}

.violate-dialog {
  /deep/.el-dialog__header {
    background-color: #fff !important;
    border-bottom: 1px solid #eee;

    .el-dialog__title {
      color: #333333 !important;
    }

    .el-dialog__close {
      color: #333333 !important;
    }
  }
}

.user-form {
  .releasePosition {
    display: flex;
    align-items: center;
  }

  /deep/.el-form-item__label {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }

  .article-title-box {
    width: max-content;
    border: 1px solid #DCDFE6;
    border-radius: 4px;

    .article-title {
      /deep/.el-textarea__inner {
        border: none !important;
      }
    }

    .topic {
      margin-top: 4px;
      padding: 0 15px;

      .tag {
        color: #b9b9b9;
        font-size: 12px;
        display: inline-block;
        padding: 1px 3px;
        background: #229ff7;
        color: #ffffff;
        border-radius: 6px;
        margin-left: 5px;
      }
    }
  }

  /deep/.el-textarea__inner {
    height: 90px;
    width: 580px;
    resize: none;
  }

  /deep/.el-form-item__content {
    line-height: 22px;
  }

  /deep/.el-upload__tip {
    margin-top: 0;
  }

  .common-radio {
    line-height: 40px;

    /deep/.el-radio {
      line-height: 40px;
    }

    /deep/.el-radio__inner {
      width: 16px;
      height: 16px;
    }

    /deep/.el-radio__label {
      vertical-align: middle;
    }
  }

  /deep/.el-input--suffix {
    width: 450px;
  }
}

.full-item {
  /deep/.el-form-item__label {
    float: none;
  }

  /deep/.el-form-item__content {
    float: none;
  }
}

.quill-editor {
  height: 420px;
  overflow-y: auto;
}

.common-input {
  width: 200px;
}

.comfirm-btn {
  height: 38px;
  // line-height: 36px;
  width: 140px;
  color: #ffffff;
  background-color: #1f6bde;
  border-radius: 19px;
}

.normal-btn {
  height: 38px;
  // line-height: 36px;
  width: 140px;
  color: #666;
  background-color: #f2f2f2;
  border-radius: 19px;
}

.el-tag+.el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 4px;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  margin-top: 4px;
  // vertical-align: bottom;
}

.avatar-uploader /deep/.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader /deep/.el-upload:hover {
  border-color: #409eff;
}

.upload-icon-wrap {
  position: relative;
  width: 300px;
  height: 178px;
  color: #999;
  @include flex(center, center, column);
}

.avatar-uploader-icon {
  font-size: 32px;
  line-height: 32px;
  margin-bottom: 5px;
  width: 32px;
  height: 32px;
  // width: 300px;
  // height: 178px;
  // line-height: 178px;
  // text-align: center;
}

.upload-icon-change {
  position: absolute;
  color: #fff;
}

.avatar {
  width: 300px;
  height: 178px;
  display: block;
}
.choice-img-area {
  .img-div {
    width: 300px;
    height: 178px;
    border: 1px dashed #D4DBE0;
    box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
    border-radius: 4px;
    line-height: 18px;
    cursor: pointer;
  }
  .img-div:hover {
    border-color: $colorMain;
  }
}
</style>
