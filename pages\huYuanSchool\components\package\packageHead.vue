<template>
  <div class="detail-msg p-20 flex mt-25">
    <img
      src="../../../../assets/newVersion/courseDetail/msg-bg.png"
      class="msg-bg"
    />
    <div class="detail-video mr-20 flex-center flex-col">
      <img :src="parentThis.detailInfo.pictureUrl" alt="" />
      <!-- <div class="is-play">
        <img src="../../../../assets/newVersion/courseDetail/video-play.png">
      </div> -->
    </div>
    <div class="flex-1">
      <div
        class="flex-wrap flex ai-center color-black fa-65 font-28 mb-8 f-w-500"
      >
        <span style="max-width: calc(100% - 50px)" class="ellipsis">{{
          parentThis.detailInfo.setMealName
        }}</span>
        <div class="ml-10 flex ai-end">
          <div class="name-line name-line1 mr-5"></div>
          <div class="name-line name-line2 mr-5"></div>
          <div class="name-line name-line3 mr-5"></div>
        </div>
      </div>
      <div
        class="mb-8 color-666 font-14 fa-55 f-w-400 ellipsis"
        v-html="parentThis.detailInfo.setMealDetails"
      ></div>
      <div class="flex ai-center">
        <el-avatar
          size="small"
          :src="parentThis.detailInfo.speakerPictureUrl"
        ></el-avatar>
        <span class="mx-8 color-black fa-65 font-14 f-w-500">{{
          parentThis.detailInfo.speakerName
        }}</span>
        <img
          src="../../../../assets/newVersion/courseDetail/goldTeacher.png"
          style="width: 75px; height: 20px"
        />
        <span class="ml-8 color-666 f-w-400 font-12 ellipsis flex-1">{{
          parentThis.detailInfo.setMealBrief
        }}</span>
      </div>

      <div class="flex ai-center mt-8">
        <div class="flex ai-center mr-20">
          <el-rate
            v-model="parentThis.detailInfo.curriculumScore"
            disabled
            :colors="['#006EFF', '#006EFF', '#006EFF']"
            disabled-void-icon-class="el-icon-star-off"
            disabled-void-color="#006EFF"
            :allow-half="true"
          />
          <span class="ml-5 color-666 font-14 fa-55">
            {{
              parentThis.detailInfo.curriculumScore
                ? parentThis.detailInfo.curriculumScore % 1 === 0
                  ? parentThis.detailInfo.curriculumScore + ".0"
                  : parentThis.detailInfo.curriculumScore
                : "0.0"
            }}
          </span>
        </div>

        <span class="f-w-500 color-666 fa-65 font-14">{{
          parentThis.detailInfo.heat || 0
        }}</span>
        <span class="f-w-400 color-999 fa-55 font-14">人学习</span>
        <div class="line"></div>

        <span class="f-w-500 color-666 fa-65 font-14">{{
          parentThis.detailInfo.videoDuration
        }}</span>
        <span class="f-w-400 color-999 fa-55 font-14">课时</span>
        <div class="line"></div>

        <span class="f-w-500 color-666 fa-65 font-14">{{
          parentThis.detailInfo.curriculumTotal
        }}</span>
        <span class="f-w-400 color-999 fa-55 font-14">课程数量</span>
      </div>

      <div class="flex ai-center mt-8">
        <div class="f-w-400 color-error fa-55 font-12">套餐价</div>
        <div
          class="f-w-700 color-error fa-85 font-20"
          v-if="parentThis.detailInfo.mealPriceType != 1"
        >
          {{
            parentThis.detailInfo.mealPriceType == 0
              ? "免费"
              : parentThis.detailInfo.mealPriceType == 2
              ? "加密"
              : "指定学员"
          }}
        </div>
        <div
          class="f-w-700 color-error fa-85 font-20"
          v-if="parentThis.detailInfo.mealPriceType == 1"
        >
          {{
            parentThis.detailInfo.packagePrice == 0
              ? "免费"
              : `￥${parentThis.detailInfo.packagePrice || 0}`
          }}
        </div>
        <!-- <el-tag v-if="parentThis.detailInfo.isBuy == 1" type="info">已购买</el-tag> -->

        <hyTag
          v-if="parentThis.detailInfo.termDay != 0"
          type="danger"
          class="ml-12"
        >
          有效期：{{ parentThis.detailInfo.termDay || 0 }}天
        </hyTag>
        <hyTag v-else type="danger" class="ml-12">有效期：永久有效</hyTag>
        <span class="ml-12 font-12 fa-55 all-price"
          >总价￥{{ parentThis.detailInfo.scribePrice || 0 }}</span
        >
        <!-- <hyTag type="warning" class="ml-12">每满300减30</hyTag> -->
      </div>
    </div>

    <!-- v-if="courseStatus == 2" -->
    <hyButton
      @click="imPay"
      type="primary"
      size="small"
      width="160px"
      height="44px"
      class="buy-btn"
    >
      <span v-if="parentThis.detailInfo.mealPriceType != 1">
        {{
          parentThis.detailInfo.mealPriceType == 0
            ? "免费领取"
            : parentThis.detailInfo.mealPriceType == 2
            ? "输入密码"
            : "指定学员"
        }}
      </span>
      <span v-if="parentThis.detailInfo.mealPriceType == 1">
        {{ parentThis.detailInfo.packagePrice == 0 ? "免费领取" : `立即购买` }}
      </span>
    </hyButton>
    <el-dialog
      title="提示"
      :visible.sync="passwordBool"
      width="30%"
      :before-close="handleClose"
    >
      <div
        class="open-video flex-col ai-center jc-center"
        style="color: #444"
      >
        <div class="mb-8 fa-55">视频为加密视频，需要输入正确密码才可观看</div>
        <div class="flex jc-center">
          <el-input
            type="password"
            v-model.trim="passwordInput"
            placeholder="请输入内容"
            style="width: 400px"
            size="medium"
            class="mr-12"
          />
          
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="passwordBool = false">取 消</el-button>
        <hyButton
            type="primary"
            width="120px"
            height="36px"
            @click="submitLockForm"
          >
            确 定
          </hyButton>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  inject: ["parentThis"],
  data() {
    return {
      passwordInput: "",
      passwordBool: false,
      isBuy: 0
    };
  },
  computed: {
    courseStatus() {
      // 1已购买，2待购买，3免费，4加密，5指定学员
      const param = this.parentThis.detailInfo;
      if (param.isBuy == 1) return 1;
      if (param.mealPriceType == 1 && param.packagePrice != 0) return 2;
      if (
        (param.mealPriceType == 1 && param.packagePrice == 0) ||
        param.mealPriceType == 0
      )
        return 3;
      if (param.mealPriceType == 2) return 4;
      if (param.mealPriceType == 3) return 5;
      return 6;
    },
  },
  watch: {
    "$route.query": {
      handler: function (newVal) {
        if (newVal && newVal.uid) {
          // 详情信息
        }
      },
      immediate: true,
    },
  },
  methods: {
    imPay() {
      const param = this.parentThis.detailInfo;
      if (param.isBuy == 1){
        this.parentThis.isBuy = 1
      } else if(this.parentThis.detailInfo.mealPriceType == 1 && this.parentThis.detailInfo.packagePrice != 0){
        this.imPay2()
      }
      if (param.mealPriceType == 1 && param.packagePrice != 0) return 2;
      if (
        (param.mealPriceType == 1 && param.packagePrice == 0) ||
        param.mealPriceType == 0
      ) {
        this.$request.curriculum
          .freeReceiveSetMeal({ setMealUid: this.$route.query.uid })
          .then((res) => {
            if (res.data.code == 200) {
              this.$message.success("领取成功");
              this.parentThis.isBuy = 1
            }
          });
      }
      if (param.mealPriceType == 2) {
        // 加密 弹出密码框
        this.passwordBool = true
      }
      if (param.mealPriceType == 3) {
        // 指定学员
      }
    },
    // 立即购买
    imPay2 () {
      if (!this.$store.getters.isLogin) {
        this.$confirm("您还未登录，请先登录后再购买?", "提示", {
          confirmButtonText: "前往登录",
          cancelButtonText: "再逛逛",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
          })
          .catch(() => {
            // 拒绝前往登录界面
            return;
          });
      } else {
        this.$store.commit('setCourseBreadcrumb', [{ label: this.parentThis.detailInfo.setMealName }]);
        this.$router.push({
          path: '/certification/pay',
          query: {
            puid: this.$route.query.uid
          }
        });
      }
    },
    // 提交密码
    submitLockForm() {
      if (!this.passwordInput) {
        return;
      }
      const params = {
        password: this.passwordInput,
        uid: this.$route.query.uid,
      };
      this.$request.curriculum
        .checkSetMealAuth(params)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("解锁成功");
            this.passwordBool = false;
            this.parentThis.passwordType = true
          }
        })
        .catch((err) => {
          this.$message.warning("密码错误，请重新输入");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-msg {
  width: 100%;
  height: 202px;
  position: relative;
  background: linear-gradient(315deg, #f1f7ff 0%, #ffffff 49%, #f1f7ff 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #ffffff;

  .msg-bg {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  div {
    // position: relative;
    z-index: 1;
  }

  .buy-btn {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 2;
  }
}

.name-line {
  background: linear-gradient(360deg, #c052ff 0%, #006eff 100%);
  border-radius: 1px 1px 1px 1px;
  width: 6px;
}

.name-line1 {
  height: 11px;
}

.name-line2 {
  height: 26px;
}

.name-line3 {
  height: 18px;
}

.line {
  width: 1px;
  height: 12px;
  background: #e6eaed;
  border-radius: 1px 1px 1px 1px;
  opacity: 1;
  margin: 0 8px;
}

.all-price {
  text-decoration: line-through;
  color: #bdc7cf;
}

.detail-video {
  width: 24.3%;
  height: 100%;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
}

.is-play {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px 20px 20px 20px;
  cursor: pointer;

  img {
    width: 30px;
    height: 30px;
    margin: 5px;
  }
}
</style>
