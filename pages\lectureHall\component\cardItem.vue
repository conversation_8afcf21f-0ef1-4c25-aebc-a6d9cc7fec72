<template>
    <div class="cardItem flex mb-20 w-100 cursor">
        <div class="flex w-100 box" @click="checkData">
            <div class="flex-4 flex">
                <div class="item-cover flex-1 p-10" ref="imgwidth" :style="{ height: ImgHeight + 'px', }">
                    <img class="w-100 h-100" style="border-radius: 5px;" :src="data.logoPath">
                    <div class="cover-tips advance font12 f-w-400 flex ai-center" style="color: #fff;"
                        v-if="data.liveStatus == 1 || data.liveStatus == 2">
                        <p>预告</p>
                        <div style="height:10px;width: 1px;border-radius: 20px;background-color: #fff;margin-left: 5px;">
                        </div>
                        <p style="margin-left: 5px;"> {{ $formatDateString(data.startTime) }} </p>
                    </div>
                    <div class="cover-tips now font12 f-w-400 flex ai-center" style="color: #fff;"
                        v-if="data.liveStatus == 3">
                        <p style="margin-left: 5px;"> 直播中 </p>
                    </div>
                </div>
            </div>
            <div class="flex flex-6 flex-col">
                <div class="flex-col card-info">
                    <div class="">
                        <p class="font18 f-w-500 ellipsis"
                            :class="['font18', 'f-w-500', 'ellipsis', isCheck ? 'check-result' : '']">
                            {{ data.name }}
                        </p>
                        <div class="font14 f-w-400 ellipsis2 " style="margin-top: 8px;color: #666666;">
                            {{ data.intro }}
                        </div>
                    </div>
                    <div class="flex ai-center" style="color: #666666;margin-top: 8px;">
                        <div>直播大咖：</div>
                        <div class="ml-15 flex ai-center">
                            <img style="width: 24px;height: 24px;border-radius: 50%;" v-if="data.liveType == 1"
                                :src="data.classroomSpeaker ? data.classroomSpeaker.picturePath : `~/assets/images/userCenter/huanyu-logo.png`">
                            <!-- <img style="width: 24px;height: 24px;border-radius: 50%;" v-if="data.liveType == 2"
                                :src="data.classroomEnterprise ? data.classroomEnterprise.logoPath : `~/assets/images/userCenter/huanyu-logo.png`"> -->
                            <span style="margin-left: 15px;" v-if="data.liveType == 1">
                                {{ data.classroomSpeaker ? data.classroomSpeaker.name || '' : '' }}
                            </span>
                            <span style="margin-left: 15px;" v-if="data.liveType == 2">
                                {{ data.classroomEnterprise ? data.classroomEnterprise.name || '' : '' }}
                            </span>
                        </div>
                    </div>
                    <div class="mt-15">
                        <!-- 1待开播 2即将开播 -->
                        <el-button class="button flex-center mt-15 cursor" @click="liveStatusOption(data)"
                            v-if="[1].includes(data.liveStatus)">{{ data.isSubscribed == 0 ?
                                liveStatus[data.liveStatus] : '预约成功'
                            }}</el-button>
                        <el-button class="button flex-center mt-15 cursor" @click="liveStatusOption(data)"
                            v-if="[2].includes(data.liveStatus)">即将开播</el-button>
                        <!-- 3直播中 4结束 5回放 -->
                        <el-button class="button flex-center mt-15 cursor" @click="liveStatusOption(data)"
                            v-if="[3, 4, 5].includes(data.liveStatus)">
                            <div class="flex ai-center">
                                <img v-if="data.liveStatus == 3" src="../../../assets/lectureHall/living.gif"
                                    style="height: 20px; margin-right: 5px;">
                                <span>{{ liveStatus[data.liveStatus] }}</span>
                            </div>
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    components: {

    },
    data() {
        return {
            ImgHeight: 0,
            liveStatus: {
                1: '立即预约',
                2: '即将开播',
                3: '进入直播',
                4: '已结束',
                5: '查看回放'
            },
        }
    },
    props: {
        data: {
            type: Object,
            default() {
                return {}
            }
        },
        isCheck: {
            type: Boolean,
            default: false
        }
    },
    computed: {

    },
    async mounted() {
        window.addEventListener('resize', this.handleResize)
        // window.addEventListener('resize', this.getBigBox)
        setTimeout(() => {
            // this.getBigBox()
            this.handleResize()
        }, 300)
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize)
    },
    created() {

    },
    methods: {
        handleResize() {
            const width = this.$refs.imgwidth ? this.$refs.imgwidth.offsetWidth : 0
            const height = Math.floor(width * 0.6) //动态获取图片宽度 * 0.6
            this.ImgHeight = height
            // console.log('图片宽度', width)
            // console.log('图片高度', height)
        },
        //直播状态行为
        liveStatusOption(item) {
            // console.log('点击直播状态', item)
            this.$emit('changeCard', item)
        },
        //选中数据
        checkData() {
            this.$emit('checkDataOne', this.data)
            // this.$emit('checkDataOne', this.data)
        }
    },
}
</script>

<style lang="scss" scoped>
.check-result {
    font-weight: bold !important;
    color: #006EFF !important;
}

.border-check {
    border: 1px dashed #b8d5fb
}

.button {
    width: 120px;
    height: 36px;
    background: #FF4D5B;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: #fff;
}

.cardItem {
    // height: 224px;

    background-image: url('@/assets/lectureHall/liveBG.png');
    background-size: cover;

    .box {
        padding: 14px 12px 8px 12px;

        .item-cover {
            position: relative;

            .cover-tips {
                position: absolute;
                top: 10px;
                right: 10px;
            }

            .advance {
                padding: 0 10px 0 20px;
                text-align: center;
                height: 24px;
                background: linear-gradient(270deg, #FF9260 0%, #F77900 0%, #FF569C 97%, rgba(247, 121, 0, 0) 100%);
                border-radius: 0px 4px 0px 16px;
            }

            .now {
                padding: 0 10px 0 10px;
                height: 24px;
                background: linear-gradient(270deg, #FF9260 0%, #F77900 0%, #FF569C 97%, rgba(247, 121, 0, 0) 100%);
                border-radius: 0px 4px 0px 16px;
            }
        }

        .card-info {
            padding: 25px 0 0 15px;
            max-width: 600px;
            min-width: 400px;
        }
    }

}
</style>