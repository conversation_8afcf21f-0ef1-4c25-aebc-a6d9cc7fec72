<template>
  <div class="mt-12">
    <div class="detail-msg p-20 mb-30">
      <img src="../../../../assets/newVersion/courseDetail/msg-bg.png" class="msg-bg">
      <div class="flex-wrap flex ai-center color-black fa-65 font-28 mb-10 f-w-500">
        <span style="max-width: calc(100% - 50px);" class="ellipsis">{{ detailInfo.name }}</span>
        <div class="ml-10 flex ai-end">
          <div class="name-line name-line1 mr-5"></div>
          <div class="name-line name-line2 mr-5"></div>
          <div class="name-line name-line3 mr-5"></div>
        </div>
      </div>

      <div class="flex ai-center">
        <el-avatar size="small" :src="detailInfo.speakerPictureUrl"></el-avatar>
        <span class="ml-8 color-black fa-65 font-14 f-w-500">{{ detailInfo.speakerName }}</span>
        <span class="ml-8 color-666 f-w-400 font-12 ellipsis flex-1">{{ detailInfo.description }}</span>
      </div>

      <div class="flex ai-center mt-10">
        <div class="flex ai-center mr-20">
          <el-rate v-model="detailInfo.curriculumScore" disabled :colors="['#006EFF', '#006EFF', '#006EFF']"
            disabled-void-icon-class="el-icon-star-off" disabled-void-color="#006EFF" :allow-half="true" />
          <span class="ml-5 color-666 font-14 fa-55">
            {{ detailInfo.curriculumScore ? detailInfo.curriculumScore % 1 === 0 ?
              detailInfo.curriculumScore + '.0' :
              detailInfo.curriculumScore : '0.0' }}
          </span>
        </div>

        <span class="f-w-500 color-666 fa-65 font-14">{{ detailInfo.studyTotal || 0 }}</span>
        <span class="f-w-400 color-999 fa-55 font-14">人学习</span>
        <div class="line"></div>

        <span class="f-w-500 color-666 fa-65 font-14">{{ detailInfo.classHour }}</span>
        <span class="f-w-400 color-999 fa-55 font-14">课时</span>
        <div class="line"></div>

        <span class="f-w-500 color-666 fa-65 font-14">{{ 0 }}</span>
        <span class="f-w-400 color-999 fa-55 font-14">个资料</span>
        <div class="line"></div>
      </div>

      <div class="flex ai-center mt-16">
        <div class="f-w-400 color-error fa-55 font-12" v-if="detailInfo.sellWay == 1 || detailInfo.sellingPrice != 0">
          <!-- 套餐价 -->
          课程价
        </div>
        <div class="f-w-700 color-error fa-85 font-20" v-if="detailInfo.sellWay != 1">
          {{ detailInfo.sellWay == 0 ? '免费' : detailInfo.sellWay == 2 ? '加密' : '指定学员' }}
        </div>
        <div class="f-w-700 color-error fa-85 font-20" v-if="detailInfo.sellWay == 1">
          {{ detailInfo.discountPrice == 0 ? '免费' : `￥${detailInfo.discountPrice}` }}
        </div>
        <!-- <el-tag v-if="detailInfo.isBuy == 1" type="info">已购买</el-tag> -->

        <hyTag v-if="detailInfo.validTime != 0" type="danger" class="ml-12">有效期：{{ detailInfo.validTime }}天</hyTag>
        <hyTag v-else type="danger" class="ml-12">有效期：永久有效</hyTag>
        <span class="ml-12 font-12 fa-55 all-price">总价￥{{ detailInfo.sellingPrice || 0 }}</span>
        <!-- <hyTag type="warning" class="ml-12">每满300减30</hyTag> -->
      </div>


      <!-- <hyButton type="primary" size="small" width="160px" height="44px" class="buy-btn" @click="imPay"> -->
      <hyButton v-if="courseStatus == 2" @click="imPay" type="primary" size="small" width="160px" height="44px"
        class="buy-btn">
        立即购买
      </hyButton>
    </div>

    <div class="video-msg w-100 flex" v-aspect="[1180, 563]">
      <div class="video-main">
        <div class="video-play">
          <div v-if="!isPlaying" class="is-play flex ai-center jc-center cursor" @click="playVideo">
            <img src="../../../../assets/newVersion/courseDetail/video-play.png">
          </div>

          <customVideo ref="video" @onPlayerPause="onPlayerPause" @onPlayerPlay="onPlayerPlay"
            :playerOptions="video_config" :key="timer" @onPlayerEnded="onPlayerEnded"
            @onPlayerLoadeddata="onPlayerLoadeddata" />

          <template v-if="!hasAuth">
            <div class="video-bg px-70 py-45">
              <img v-lazy="$store.state.webConfig.logoPhoto" class="logo" />
              <div class="mt-25 font-36 fa-75 color-333 ellipsis2">{{ activeVideoMsg.name }}</div>

              <div v-if="courseStatus == 2" class="open-video flex-col ai-center jc-center" style="color:#444">
                <div class="mb-8 fa-55">暂无该课程权限</div>
                <div class="mb-8 fa-55">请确认是否已购买该课程</div>
                <hyButton type="primary" width="120px" height="36px" @click="imPay">
                  立即购买
                </hyButton>
              </div>

              <div v-if="courseStatus == 3" class="open-video flex-col ai-center jc-center" style="color:#444">
                <hyButton type="primary" width="120px" height="36px" @click="getCurriculum">
                  立即领取
                </hyButton>
              </div>

              <div v-if="courseStatus == 5" class="open-video flex-col ai-center jc-center" style="color:#444">
                <div class="mb-8 fa-55">暂无该课程权限</div>
                <div class="mb-8 fa-55">请确认绑定的手机号是否正确，或联系客服开通权限</div>
                <hyButton type="primary" width="120px" height="36px" @click="openChat">
                  联系客服
                </hyButton>
              </div>

              <div v-if="courseStatus == 4" class="open-video flex-col ai-center jc-center" style="color:#444">
                <div class="mb-8 fa-55">视频为加密视频，需要输入正确密码才可观看</div>
                <div class="flex jc-center">
                  <el-input type="password" v-model.trim="passwordInput" placeholder="请输入内容" style="width:400px;"
                    size="medium" class="mr-12" />
                  <hyButton type="primary" width="120px" height="36px" @click="submitLockForm">
                    确 定
                  </hyButton>
                </div>
              </div>

            </div>
          </template>
        </div>

        <div class="video-config flex ai-center ml-12">
          <img src="../../../../assets/newVersion/courseDetail/download.png" class="cursor">
          <span class="color-666 fa-55 font-12 mr-12 ml-5 cursor active-color" @click="donwloadVideo">离线观看</span>
          <img src="../../../../assets/newVersion/courseDetail/collect.png" class="cursor">
          <span class="color-666 fa-55 font-12 mr-8 ml-5 cursor active-color" @click="collectCurriculum">
            {{ detailInfo.isCollect == 1 ? '取消收藏' : '收藏' }}
          </span>

          <el-switch v-model="isAutoPlay" style="scale: 0.6" />
          <span class="color-666 fa-55 font-12 cursor">自动播放下一集</span>
        </div>
      </div>

      <div class="video-directory px-20">
        <el-collapse accordion v-model="activeNames">
          <el-collapse-item v-for="(item, index) in directoryList" :key="index" :name="item.uid">
            <template slot="title">
              <el-tooltip v-if="item.name.length > 18" class="item" effect="dark" :content="`${item.name}`"
                placement="top">
                <span class="color-333 font-14 f-w-400 fa-65 ellipsis">{{ item.name }}</span>
              </el-tooltip>
              <span v-else class="color-333 font-14 f-w-400 fa-65 ellipsis">{{ item.name }}</span>

            </template>
            <div v-if="item.videoList">
              <div class="flex ai-center jc-between video-item color-999" v-for="(details, i) in item.videoList" :key="i"
                :class="{ 'active-video': details.uid == activeVideoUid }" @click="clickVideo(details, item.uid)">
                <div class="cursor flex ai-center">
                  <img v-if="details.uid == activeVideoUid" src="../../../../assets/newVersion/courseDetail/playing.png">
                  <img v-else src="../../../../assets/newVersion/courseDetail/waiting-play.png">
                  <span class="font-14 f-w-400 fa-55 ml-5 ellipsis">{{ details.name }}</span>
                  <hyButton v-if="details.isFreeLook == 1" type="primary" width="62px" height="22px" class="ml-5">
                    <template>
                      <span style="font-size: 10px !important; letter-spacing: 0px;">免费试看</span>
                    </template>
                  </hyButton>
                </div>

                <span class="font-14 f-w-400 fa-55">
                  {{ getTime(details.duration) }}
                </span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    },
    directoryList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      score: 4.5,
      isAutoPlay: false,
      // directoryList: [],
      activeDirectory: '', // 播放页所属章节,
      activeNames: '', // 展开的章节
      activeVideoUid: '',
      activeVideoMsg: { videoUrl: "" },
      isPlaying: false,
      video_config: { //播放器配置
        containerWidth: '100%',
        controls: true,
        muted: false,//是否静音
        language: 'zh',
        width: '100%',
        loadingSpinner: false,
        errorDisplay: false,
        height: '100%',
        sources: [{
          type: "video/mp4",
          src: "https://cdn.theguardian.tv/webM/2015/07/20/150716YesMen_synd_768k_vp8.webm"
        }],
        poster: "",//封面图
        controlBar: {
          children: [
            { name: 'playToggle' },
            { name: 'currentTimeDisplay' },
            { name: 'progressControl' },
            { name: 'durationDisplay' },
            { // 倍数播放
              name: 'playbackRateMenuButton',
              playbackRates: [0.5, 1.0, 1.5, 2.0, 3.0],//播放速度
            },
            { name: 'volumePanel', inline: false, },
            // { name: 'pictureInPictureToggle' },
            { name: 'fullscreenToggle' },
          ]
        }
      },
      timer: '',
      passwordInput: '',
      rightPassword: false, // 加密输入密码正确
    };
  },
  computed: {
    hasAuth () { // 是否有观看权限，免费课程需领取
      // if (this.detailInfo.sellWay == 0) {
      //   return true
      // }
      // if (this.detailInfo.sellWay == 1 && this.detailInfo.discountPrice == 0) {
      //   return true
      // }
      if (this.courseStatus == 7) {
        return true
      }
      if (this.courseStatus == 8) {
        return true
      }
      if (this.detailInfo.isBuy == 1) {
        return true
      }
      if (this.rightPassword) {
        return true
      }
      if (this.activeVideoMsg.isFreeLook == 1) {
        return true
      }

      return false
    },
    courseStatus () {
      // 1已购买，2待购买，3免费，4加密，5指定学员
      const param = this.detailInfo
      if (param.speakerUid == this.$store.state.userInfo.speakerUid) return 7
      if (param.isBuy == 1) return 1
      if (param.sellWay == 1 && param.discountPrice != 0) return 2
      if ((param.sellWay == 1 && param.discountPrice == 0) || param.sellWay == 0) return 3
      if (param.sellWay == 2) return 4
      if (param.sellWay == 3 && param.namedUser == 0) return 5
      if (param.sellWay == 3 && param.namedUser == 1) return 8
      return 6
    }
  },
  watch: {
    activeDirectory (newVal) {
      if (newVal) this.activeNames = this.activeDirectory
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.timer = new Date().getTime()
      if (this.directoryList && this.directoryList.length > 0) {
        if (this.$route.query.pUid) {
          const parent = this.directoryList.find(item => item.uid == this.$route.query.pUid)
          let video
          if (parent) video = parent.videoList.find(item => item.uid == this.$route.query.videoUid)
          if (video) this.clickVideo(video, parent.uid)
        } else {
          if (!this.directoryList || !this.directoryList[0] || !this.directoryList[0].videoList || !this.directoryList[0].videoList[0]) return
          this.clickVideo(this.directoryList[0].videoList[0], this.directoryList[0].uid)
        }
      }
    })

  },
  methods: {


    // 切换课时
    clickVideo (row, uid) {
      this.activeDirectory = uid
      this.activeVideoUid = row.uid
      this.activeVideoMsg = row
      this.timer = new Date().getTime()
      if (row.videoUrl) this.video_config.sources[0].src = row.videoUrl
      this.video_config.poster = row.picturePasterUrl ? row.picturePasterUrl : ''
      this.isPlaying = false
      this.rightPassword = false
    },

    // 播放视频
    playVideo () {
      this.isPlaying = true
      const ele = document.querySelector('.vjs-big-play-button')
      ele && ele.click()

    },
    // 暂停视频
    onPlayerPause () {
      this.isPlaying = false
    },
    onPlayerPlay () {
      this.isPlaying = true
    },

    onPlayerLoadeddata () {
      if (!this.isAutoPlay) return
      if (!this.hasAuth) return
      this.playVideo()
    },

    // 播放完毕是否下一个视频
    onPlayerEnded () {
      if (!this.isAutoPlay) return
      let directoryIndex = 'none' // 章节下标
      let activeDirectory = this.directoryList.find((item, index) => { // 章节信息
        if (this.activeDirectory == item.uid) {
          directoryIndex = index
          return true
        }
      })
      let videoDetail // 视频信息
      let directoryUid // 章节uid
      activeDirectory && activeDirectory.videoList.forEach((item, index) => {
        // 遍历至播放视频，且不是章节最后一个视频时
        if (this.activeVideoUid == item.uid && index != activeDirectory.videoList.length - 1) {
          videoDetail = activeDirectory.videoList[index + 1]
          directoryUid = activeDirectory.uid
        }
      })

      // videoDetail与directoryUid无则为章节最后一个视频，且不是最后一章时
      if ((!directoryUid || !videoDetail) && directoryIndex != this.directoryList.length - 1) {
        activeDirectory = this.directoryList[directoryIndex + 1]
        videoDetail = activeDirectory.videoList[0]
        directoryUid = activeDirectory.uid
      }

      if (directoryUid && videoDetail) { this.clickVideo(videoDetail, directoryUid) }
    },

    // 下载视频
    donwloadVideo () {
      if (!this.hasAuth) return this.$message.warning('暂无权限下载')
      this.$debounce(() => {
        this.$downladResetFileName(this.video_config.sources[0].src, this.activeVideoMsg.name)
      })();
    },

    // 立即购买
    imPay () {
      if (!this.$store.getters.isLogin) {
        this.$confirm("您还未登录，请先登录后再购买?", "提示", {
          confirmButtonText: "前往登录",
          cancelButtonText: "再逛逛",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
          })
          .catch(() => {
            // 拒绝前往登录界面
            return;
          });
      } else {
        this.$store.commit('setCourseBreadcrumb', [{ label: this.detailInfo.name }]);
        this.$router.push({
          path: '/certification/pay',
          query: {
            vuid: this.detailInfo.uid
          }
        });
      }
    },

    // 立即领取
    getCurriculum () {
      this.$request.curriculum.freeReceiveCurriculum({ curriculumUid: this.$route.query.uid }).then((res) => {
        if (res.data.code == 200) {
          this.$message.success('领取成功');
          this.detailInfo.isBuy = 1
        }
      })
    },

    // 联系客服
    openChat () {
      const ele = document.querySelector('.launchButton');
      ele && ele.click()
    },

    // 提交密码
    submitLockForm () {
      if (!this.passwordInput) {
        return
      }
      const params = {
        password: this.passwordInput,
        uid: this.$route.query.uid
      }
      this.$request.curriculum.checkCurriculumAuth(params).then((res) => {
        if (res.data.code == 200) {
          this.$message.success('解锁成功');
          this.rightPassword = true
        }
      }).catch((err) => {
        this.$message.warning('密码错误，请重新输入');
      })
    },

    // 视频时长
    getTime (val) {
      let value = val
      let time = []
      let day = parseInt(value / 86400)
      let hour = parseInt((value % 86400) / 3600)
      let min = parseInt(((value % 86400) % 3600) / 60)
      let sec = parseInt(((value % 86400) % 3600) % 60)
      time[0] = day > 0 ? day : 0
      time[1] = hour > 0 ? hour : 0
      time[2] = min > 0 ? parseFloat(min) : 0
      time[3] = sec > 0 ? parseFloat(sec) : 0
      let durationHour = time[1] > 0 ? (time[1] > 9 ? time[1] : '0' + time[1]) + ':' : ''
      let durationMin = time[2] > 0 ? (time[2] > 9 ? time[2] : '0' + time[2]) + ':' : '00:'
      let durationSec = time[3] > 0 ? (time[3] > 9 ? time[3] : '0' + time[3]) : '00'
      return durationHour + durationMin + durationSec
    },

    // 收藏
    collectCurriculum () {
      let requestFun
      let params
      if (this.detailInfo.isCollect == 1) {
        requestFun = 'cancelCurriculumBlogByUid'
        params = { uids: this.$route.query.uid }
      } else {
        requestFun = 'collectCurriculumByUid'
        params = { uid: this.$route.query.uid }
      }

      this.$request.curriculum[requestFun](params).then(res => {
        if (res.data.code == 200) {
          this.$message.success(this.detailInfo.isCollect == 1 ? '取消收藏成功' : '收藏成功')
          if (this.detailInfo.isCollect == 1) {
            this.detailInfo.isCollect = 0
          } else {
            this.detailInfo.isCollect = 1
          }
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-msg {
  width: 100%;
  height: max-content;
  position: relative;
  background: linear-gradient(315deg, #F1F7FF 0%, #FFFFFF 49%, #F1F7FF 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #FFFFFF;

  .msg-bg {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  div {
    position: relative;
    z-index: 1;
  }

  .buy-btn {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 2;
  }
}




.name-line {
  background: linear-gradient(360deg, #C052FF 0%, #006EFF 100%);
  border-radius: 1px 1px 1px 1px;
  width: 6px;
}

.name-line1 {
  height: 11px;
}

.name-line2 {
  height: 26px;
}

.name-line3 {
  height: 18px;
}

.line {
  width: 1px;
  height: 12px;
  background: #E6EAED;
  border-radius: 1px 1px 1px 1px;
  opacity: 1;
  margin: 0 8px;
}

.all-price {
  text-decoration: line-through;
  color: #BDC7CF;
}

.video-msg {
  background: linear-gradient(315deg, #F1F7FF 0%, #FFFFFF 49%, #F1F7FF 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #FFFFFF;
  overflow: hidden;

  .video-main {
    height: 100%;
    width: calc(100% * (880 / 1180));

    .video-play {
      height: calc(100% - 28px);
      width: 100%;
      background: #1A1B2B;
      position: relative;
      z-index: 1;

      .is-play {
        width: 64px;
        height: 64px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 64px 64px 64px 64px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 3;
      }

      /deep/ .video-js {
        width: 100%;
        height: 100%;

        .vjs-current-time,
        .vjs-duration {
          display: block;
        }
      }

      .video-bg {
        background: rgba(255, 255, 255, 0.7);
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;

        .logo {
          width: 140px;
        }

        .open-video {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;

          /deep/ .el-input__inner {
            background-color: transparent !important;
          }
        }
      }

    }

    .video-config {
      height: 28px;
    }


  }

  .video-directory {
    width: calc(100% * (300 / 1180));
    padding-top: 0;
    padding-bottom: 0;
    height: calc(100% - 38px);
    margin-top: 10px;
    overflow: auto;

    // 滚动条整体样式
    &::-webkit-scrollbar {
      display: none;
    }


    scrollbar-width: thin !important;
    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    -ms-overflow-style: none !important;
    /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */

  }
}

::v-deep {
  .el-collapse {
    border: none;
  }

  .el-collapse-item {
    margin-bottom: 8px;

    .el-collapse-item__header {
      padding: 0;
      height: 40px;
      background: transparent;
      color: #666;


      .el-icon-arrow-right:before {
        content: "\e790";
        color: #999999;
      }

      .el-collapse-item__arrow.is-active {
        transform: rotate(180deg);
      }
    }

    .el-collapse-item__header:hover {
      color: #006EFF !important;

      .el-icon-arrow-right:before {
        color: #006EFF !important;
      }
    }

    .el-collapse-item__wrap {
      background: transparent;
      border: none;
      // padding: 10px 20px 10px 52px;
    }

    .el-collapse-item__content {
      padding: 0;
    }

  }


}

.video-item {
  padding: 4px 7px;
  border-radius: 4px 4px 4px 4px;

  div:nth-child(1) {
    width: calc(100% - 60px);
  }
}

.video-item:hover {
  color: #006EFF !important;
}

.active-video {
  background: rgba(0, 110, 255, 0.05);
  color: #006EFF !important;
}
</style>
