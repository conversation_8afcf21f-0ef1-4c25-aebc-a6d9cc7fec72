<template>
    <div class="container" :style="{ width: playerOptions.containerWidth, height: playerOptions.height }">
        <div class="video-js vjs-big-play-centered" :playsinline="playsinline" @play="onPlayerPlay($event)"
            @pause="onPlayerPause($event)" @ended="onPlayerEnded($event)" @loadeddata="onPlayerLoadeddata($event)"
            @waiting="onPlayerWaiting($event)" @playing="onPlayerPlaying($event)" @timeupdate="onPlayerTimeupdate($event)"
            @canplay="onPlayerCanplay($event)" @canplaythrough="onPlayerCanplaythrough($event)" @ready="playerReadied"
            @statechanged="playerStateChanged($event)" v-video-player:myVideoPlayer="defaultPlayerOptions">
        </div>
    </div>
</template>
 
<script>
export default {
    props: {
        playerOptions: {
            type: Object,
            default: () => (
                {//播放器配置
                    containerWidth: '900px',
                    controls: true,
                    muted: false,//是否静音
                    language: 'zh',
                    width: '900px',
                    loadingSpinner: false,
                    errorDisplay: false,
                    height: '500px',
                    playbackRates: [0.5, 1.0, 1.5, 2.0],//播放速度
                    sources: [{
                        type: "video/mp4",
                        src: "https://cdn.theguardian.tv/webM/2015/07/20/150716YesMen_synd_768k_vp8.webm"
                    }],
                    poster: "",//封面图
                }
            )
        }
    },
    data() {
        return {
            playsinline: true,
            defaultPlayerOptions: {//播放器配置
                controls: true,
                loadingSpinner: false,
                errorDisplay: false,
                muted: true,//是否静音
                language: 'zh-CN',
                width: '900px',
                height: '500px',
                playbackRates: [0.5, 1.0, 1.5, 2.0],//播放速度
                sources: [{
                    type: "video/mp4",
                    src: "https://cdn.theguardian.tv/webM/2015/07/20/150716YesMen_synd_768k_vp8.webm"
                }],
                poster: "",//封面图
                controlBar: {
                    children: [
                        {
                            name: 'playToggle'
                        },
                        {
                            name: 'progressControl'
                        },
                        {
                            name: 'currentTimeDisplay'
                        },
                        {
                            name: 'timeDivider',
                        },
                        {
                            name: 'durationDisplay'
                        },
                        {
                            name: 'playbackRateMenuButton'  // 添加倍速播放按钮
                        },
                        {
                            name: 'fullscreenToggle'
                        },
                        {
                            name: 'volumePanel',
                            inline: false,
                        }
                    ]
                }
            }
        }
    },
    mounted() {
        if (this.playerOptions.sources[0].src) { 
            Object.assign(this.defaultPlayerOptions, this.playerOptions);
            this.$forceUpdate();
        }
     
    },
    watch: {
        playerOptions: {
            handler: function (val) {
                if (val&&val.sources && val.sources.length>0 && val.sources[0].src) {
                    Object.assign(this.defaultPlayerOptions, val);
                    this.$forceUpdate();
                }
            },
            immediate: true
        }
    },
    methods: {
        // 监听播放
        onPlayerPlay(player) {
            this.$emit("onPlayerPlay", player);
        },
        // 监听暂停
        onPlayerPause(player) {
            this.$emit("onPlayerPause", player);
        },
        // 监听停止
        onPlayerEnded(player) {
            this.$emit("onPlayerEnded", player);
        },
        // 监听加载完成
        onPlayerLoadeddata(player) {
            this.$emit("onPlayerLoadeddata", player);
        },
        // 监听视频缓存等待
        onPlayerWaiting(player) {
            this.$emit("onPlayerWaiting", player);
        },
        // 监听视频暂停后播放
        onPlayerPlaying(player) {
            this.$emit("onPlayerPlaying", player);
        },
        // 监听视频播放时长更新
        onPlayerTimeupdate(player) {
            this.$emit("onPlayerTimeupdate", player);
        },
        onPlayerCanplay(player) {
            this.$emit("onPlayerCanplay", player);
        },
        onPlayerCanplaythrough(player) {
            this.$emit("onPlayerCanplaythrough", player);
        },
        // 监听状态改变
        playerStateChanged(playerCurrentState) {
            this.$emit("playerStateChanged", playerCurrentState);
        },
        // 监听播放器准备就绪
        playerReadied(player) {
            this.$emit("playerReadied", player);
        }
    }
}
</script>
 
<style lang="scss" scoped>
.container {
    width: 900px;
    height: 500px;

    // 下面样式是修改播放按钮的
//     /deep/ .video-js{
//         color: $colorMain;
//     }
    /deep/ .video-js .vjs-big-play-button {
        // font-size: 2.5em !important;
        // line-height: 2.3em !important;
        // height: 2.5em !important;
        // width: 2.5em !important;
        // -webkit-border-radius: 2.5em !important;
        // -moz-border-radius: 2.5em !important;
        // border-radius: 2.5em !important;
        // background-color: rgb(255, 255, 255) !important;
        // border-width: 0.15em !important;
        // margin-top: -1.25em !important;
        // margin-left: -1.75em !important;
        display: none !important;
    }

    /deep/ .vjs-poster{
        background-size: cover !important;
    }

//    /deep/ .vjs-big-play-button .vjs-icon-placeholder {
//         font-size: 1.63em !important;
//     }
//     /deep/ .vjs-slider-vertical .vjs-volume-level:before{
//         left: -0.5em;
//     }

    .video-player-box {
        background: red;
    }
}
</style>