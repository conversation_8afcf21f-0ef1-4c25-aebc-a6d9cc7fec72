<template>
    <div class="scollCard w-100" ref="bigCard">
        <img src="../../../assets/lectureHall/toleft.png" class="left-icon cursor" @click="moveCarousel(-1)"
            v-show="currentIndex != 0">
        <div class="scroll-box overflow-hidden">
            <transition-group name="slide" tag="div" class="item-container">
                <div class="item-box cursor" ref="cover" v-for="(   item, index   ) in  list" :key="index" :style="{
                    transform: 'translateX' + '(' + currentOffset + 'px' + ')',
                }" @click="checkDataInfo(item)">

                    <div class="item-cover" :style="{
                        height: imgHeight + 'px',
                    }">
                        <img class="w-100 h-100" style="border-radius: 4px;" :src="item.logoPath">
                        <div class="cover-tips advance font12 f-w-400 flex ai-center" style="color: #fff;"
                            v-if="[1, 2].includes(item.liveStatus)">
                            <p>预告</p>
                            <div
                                style="height:10px;width: 1px;border-radius: 20px;background-color: #fff;margin-left: 5px;">
                            </div>
                            <p style="margin-left: 5px;"> {{ $formatDateString(item.startTime) }} </p>
                        </div>
                        <div class="cover-tips now font12 f-w-400 flex ai-center" style="color: #fff;"
                            v-if="[3, 4, 5].includes(item.liveStatus)">
                            <p style="margin-left: 5px;"> {{ liveStatus[item.liveStatus] }} </p>
                        </div>
                    </div>
                    <div class="mt-10" style="height: 48px;">
                        <p :class="['ellipsis2', 'font16', 'f-w-400', data.uid == item.uid ? 'check-result' : '']"> {{
                            item.name }}</p>
                    </div>
                    <div class="w-100 flex jc-start" style="margin-top: 10px;">
                        <el-button class="little-button   cursor" @click.stop="liveStatusOption(item)"
                            v-if="[1, 2].includes(item.liveStatus)">{{ item.isSubscribed == 0 ?
                                liveStatus[item.liveStatus] : '预约成功'
                            }}</el-button>
                        <el-button class="little-button  cursor" @click.stop="liveStatusOption(item)"
                            v-if="[3, 4, 5].includes(item.liveStatus)">
                            <div class="flex ai-center">
                                <img v-if="item.liveStatus == 3" src="../../../assets/lectureHall/living.gif"
                                    style="width: 14px; margin-right: 5px;">
                                <span>{{ liveStatus[item.liveStatus] }}</span>
                            </div>
                        </el-button>
                    </div>
                </div>
            </transition-group>
        </div>
        <img src="../../../assets/lectureHall/toright.png" class="right-icon cursor" @click="moveCarousel(1)"
            v-show="!this.atEndOfList">
    </div>
</template>

<script>
export default {
    name: '',
    components: {

    },
    data() {
        return {
            imgHeight: 0,
            currentIndex: 0,//滚动了几次
            paginationFactor: 0,//每次滚动的距离
            windowSize: 1, //显示个数：每页展示个数
            liveStatus: {
                1: '立即预约',
                2: '即将开播',
                3: '进入直播',
                4: '已结束',
                5: '查看回放'
            },
        }
    },
    computed: {
        //判断是否是第一张/最后一张
        atEndOfList() {
            return (
                this.currentOffset <=
                this.paginationFactor * -1 * (this.list.length - this.windowSize)
            );
        },
        atHeadOfList() {
            return this.currentOffset === 0;
        },
        currentOffset() {
            return - this.currentIndex * this.paginationFactor || 0
        }
    },
    async mounted() {
        window.addEventListener('resize', this.getBigBox)
        window.addEventListener('resize', this.handleResize)
        this.getBigBox()
        setTimeout(() => {
            // this.getBigBox()
            this.handleResize()
        }, 500)
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.getBigBox)
        window.removeEventListener('resize', this.handleResize)
    },
    props: {
        list: {
            type: Array,
            default: () => []
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },
    methods: {
        //获取图片宽度
        getBigBox() {
            const width = this.$refs.bigCard ? this.$refs.bigCard.offsetWidth : 0
            const itemWidth = Math.floor((width - 80) / 3)
            this.imgHeight = Math.floor(itemWidth * 0.57)
            // console.log('滑动图片长度', imgHeight)
        },
        //直播状态行为
        liveStatusOption(item) {
            console.log('点击直播状态', item)
            this.$emit('changeCard', item)
        },
        checkDataInfo(e) {
            this.$emit('checkDataTwo', e)
        },
        moveCarousel(direction) {
            if (direction === 1 && !this.atEndOfList) {
                this.currentIndex++
            } else if (direction === -1 && !this.atHeadOfList) {
                this.currentIndex--
            }
            // console.log('滚动了几次', this.currentIndex)
        },
        handleResize() {
            const cover = this.$refs.cover
            if (cover[0] && cover[0].offsetWidth) {
                const width = cover ? cover[0].offsetWidth : 0
                this.paginationFactor = width + 25
                // console.log('盒子宽度', width)
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.check-result {
    font-size: 19px !important;
    font-weight: bold !important;
    color: #006EFF !important;
}

.item-container {
    display: flex;
}

.slide-enter-active,
.slide-leave-active {
    transition: transform 0.3s ease-in-out; // 过渡效果，可根据需求修改
}

.slide-enter,
.slide-leave-to {
    transform: translateX(0); // 定义起始和结束状态
}

.border-check {
    border: 1px solid #006EFF;
}

.little-button {
    padding: 8px 15px 8px 15px;
    background: #FF4D5B;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: #fff;
}

.scollCard {
    padding: 25px 25px 10px 25px;
    background-image: url('@/assets/lectureHall/liveBG1.png');
    background-size: cover;
    position: relative;

    .left-icon {
        position: absolute;
        left: 0px;
        top: 45%;
    }

    .right-icon {
        position: absolute;
        right: 0px;
        top: 45%;
    }

    .scroll-box {
        display: flex;
        flex-wrap: nowrap;
        overflow: scroll hidden;

        .item-box {
            transition: transform 0.3s ease-in-out;
            // min-width: calc((100% - 80px) / 3);
            width: 340px;
            margin-right: 26px;
            position: relative;

            .item-cover {
                width: 100%;

                .cover-tips {
                    position: absolute;
                    top: 0;
                    right: 0;
                }

                .advance {
                    padding: 0 10px 0 20px;
                    text-align: center;
                    height: 24px;
                    background: linear-gradient(270deg, #FF9260 0%, #F77900 0%, #FF569C 97%, rgba(247, 121, 0, 0) 100%);
                    border-radius: 0px 4px 0px 16px;
                }

                .now {
                    padding: 0 10px 0 10px;
                    height: 24px;
                    background: linear-gradient(270deg, #FF9260 0%, #F77900 0%, #FF569C 97%, rgba(247, 121, 0, 0) 100%);
                    border-radius: 0px 4px 0px 16px;
                }
            }
        }


    }
}
</style>