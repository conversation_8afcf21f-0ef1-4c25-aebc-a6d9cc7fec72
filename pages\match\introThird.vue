<template>
  <div class="match-wrapper">
    <div class="match-content">
      <div class="type-area flex-center-col">
        <div class="empty-box"></div>
        <div class="entry-box flex-center" style="justify-content: space-evenly;">
          <template v-for="item in matchList">
            <div
              @mouseleave="item.picEnName === 'AUDIENCE_ENTRANCE' ? closeSubBtn() : ''" v-if="item.status"
              :key="item.id" @click="handleMatchClick(item)"
              :style="{'background': 'url('+item.pictureUrl+')','background-size': '100% 100%'}"
              :class="[{ 'match': item.picEnName === 'AUDIENCE_ENTRANCE', 'sign_enter': item.picEnName === 'SIGN_ENTRY' && !item.linkUrl }]"
            >
              {{ item.picName }}
              <div v-if="item.picEnName === 'SIGN_ENTRY' && !item.linkUrl" class="title-des">（暂未开始）</div>
              <div v-if="matchWatchFlag && item.picEnName === 'AUDIENCE_ENTRANCE'" :class="{ 'match_div': matchWatchFlag }">
                <template v-if="audience_entrance">
                  <div v-if="i.tag == 3 || i.tag == 2" @click.stop="audienceEntrance(i)" class="px-10"
                    v-for="(i, idx) in audience_entrance" :key="idx"><span style="font-size:18px !important;">{{ i.name}}</span></div>
                </template>
              </div>
            </div>
          </template>
          <!-- <div class="official-div">
            报名入口
          </div>
          <div @click="handleMatchClick" :class="{'audience-div':true, 'match':matchWatchFlag}">
            观众入口
            <div v-if="matchWatchFlag" class="match_div">
              <div>晋级赛</div>
              <div class="mt-15">总决赛</div>
            </div>
          </div>
          <div class="audience-div">
            排序榜
          </div>
          <div class="audience-div">
            选手查询
          </div>
          <div @click="handleWrUpload" class="audience-div">
            WP上传
          </div> -->
          <!-- <div class="official-entry" @click="gotoMatch"></div>
          <div class="official-board" @click="rankingShow = true"></div>
          <div class="official-query" @click="playerShowFn"></div> -->
        </div>


        <div class="about" id="nav1">
          <div class="_about">
            <div class="titles">
              •&nbsp;&nbsp;&nbsp;关于大赛&nbsp;&nbsp;&nbsp;•
            </div>
            <div class="aboutContent">
              <img style="width: 300px" src="../../assets/2022/match_file1.png" alt="" @click="toPdf1" />
              <div style="flex: 1; margin-left: 30px">
                为贯彻落实新时期产业工人队伍建设改革方案、国家网络空间安全战略，依据《中华人民共和国网络安全法》、 《关键信息基础设施安全保护条例》，第三届“指挥官杯”能源互联网主动防御安全技能竞赛通过“以赛促能、以赛促防”方式，持续为企业、为行业、为国家选拔最可靠、可信、可用的能源互联网人才，打造坚强的能源互联网主动防御体系。
              </div>
            </div>
            <div class="aboutDetial">
              <div>
                <span>联系人：</span>
                <span>洪杨</span>
              </div>
              <div>
                <span>电话：</span>
                <span>13718401790</span>
              </div>
            </div>
          </div>
        </div>
        <div class="organization" id="nav2">
          <div class="titles">
            •&nbsp;&nbsp;&nbsp;组织单位&nbsp;&nbsp;&nbsp;•
          </div>
          <div class="guidance">
            <div class="guidance_title">主办单位</div>
            <div class="guidance_text">
              <ul v-for="(item, index) in hostList" :key="index">
                <li>{{ item }}</li>
              </ul>
            </div>
          </div>
          <div class="guidance">
            <div class="guidance_title">技术单位</div>
            <div class="guidance_text">
              <ul v-for="(item, index) in technologyList" :key="index">
                <li>{{ item }}</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="match-obj" id="nav3">
          <div class="titles">
            •&nbsp;&nbsp;&nbsp;参赛对象&nbsp;&nbsp;&nbsp;•
          </div>
          <div class="match-part">
            <div class="match-part-list">
              <div class="match-list-image">1</div>
              <div class="match-list-title">选手范围</div>
              <div class="match-list-txt" style="text-indent: 2em;">
                突出“广泛性、基层性”的原则，参赛选手为能源行业内企业的网络安全从业骨干人员，与能源互联网安全相关的科研人员、高校师生、专业能手。
              </div>
            </div>
            <div class="match-part-list" style="margin-top: 30px">
              <div class="match-list-image">2</div>
              <div class="match-list-title">选手条件</div>
              <div class="match-list-txt">
                <p>• 熟悉并遵守国家网络安全相关法律、法规、政策等</p>
                <p>• 具有良好的思想道德素质和心理素质</p>
                <p>• 掌握过硬的网络安全知识和实操技能</p>
              </div>
            </div>
          </div>
        </div>
        <div class="match-obj" id="nav4">
          <div class="titles">
            •&nbsp;&nbsp;&nbsp;奖项设置&nbsp;&nbsp;&nbsp;•
          </div>
          <div class="match-part" style="display: flex">
            <div class="gift-left">
              <img style="margin-top: 30px" src="../../assets/2022/gift-title1.png" alt="" />
              <div style="margin-top: 10px">
                <div style="margin-top: 20px;line-height: 30px;text-indent: 2em;letter-spacing: 6px;">
                  获得个人一等奖的选手，授予“金牌选手”荣誉
                </div>
                <div style="margin-top: 20px;line-height: 30px;text-indent: 2em;letter-spacing: 6px;">
                  获得个人二等奖的选手，授予“银牌选手”荣誉
                </div>
                <div style="margin-top: 20px;margin-bottom: 30px;line-height: 30px;text-indent: 2em;letter-spacing: 6px;">
                  荣获战队一、二、三等奖的领队，授予“杰出指挥官”荣誉
                </div>
              </div>
              <!-- <div class="cursor mt-20 mb-30" style=" color: #f3be10; font-size: 20px" @click="toPdf2">
                点击查看历史赛事文件
              </div> -->
            </div>
            <div class="gift-right">
              <div>
                <img src="../../assets/2022/gift-title2.png" alt="" />
                <div style="margin-top: 15px; margin-left: 30px">
                  一等奖：红、蓝各1名；
                </div>
                <div style="margin-top: 15px; margin-left: 30px">
                  二等奖：红、蓝各2名；
                </div>
                <div style="margin-top: 15px; margin-left: 30px">
                  三等奖：红、蓝各3名。
                </div>
              </div>
              <div style="margin-top: 20px">
                <img src="../../assets/2022/gift-title3.png" alt="" />
                <div style="margin-top: 15px; margin-left: 30px">
                  一等奖：红、蓝各1支战队；
                </div>
                <div style="margin-top: 15px; margin-left: 30px">
                  二等奖：红、蓝各2支战队；
                </div>
                <div style="margin-top: 15px; margin-left: 30px">
                  三等奖：红、蓝各3支战队。
                </div>
              </div>
              <div style="margin-top: 20px">
                <img src="../../assets/2022/gift-title4.png" alt="" />
                <div style="margin-top: 15px; margin-left: 30px">
                  战队一、二、三等奖所属单位分别颁发单位一等奖、二等奖和三等奖。
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="match-obj" id="nav5">
          <div class="titles">
            •&nbsp;&nbsp;&nbsp;&nbsp;赛程安排&nbsp;&nbsp;&nbsp;&nbsp;•
          </div>
          <div class="match-part match-part-flex">
            <div class="match-part-left">
              <div class="match-part-left-item">
                <div class="match-left-title" style="margin-top: 42px;">
                  <img class="match-title-bg" :src="matchTitleBg1" alt="" />
                  <span :style="{ 'z-index': 1, 'font-size': '18px', color: '#fff',}">报名</span>
                </div>
              </div>
              <img :src="matchArrowIcon" class="match-part-arrow" style="top: 48px;" />
              <div class="match-part-left-item">
                <div class="match-left-title">
                  <img class="match-title-bg" :src="matchTitleBg2" alt="" />
                  <span :style="{ 'z-index': 1, 'font-size': '18px', color: '#fff',}">预选赛</span>
                </div>
                <div>
                  <div class="match-title-mt">比赛时间：2022年9月23日-24日</div>
                  <div class="match-title-mt">比赛形式：线上理论答题，随机抽题</div>
                </div>
              </div>
            </div>
            <div class="match-part-left" style="justify-content: end; padding-right: 190px; margin-bottom: 55px;">
              <img :src="matchArrowIcon" class="match-part-arrow vertical" />
            </div>
            <div class="match-part-left">
              <div class="match-part-left-item">
                <div class="match-left-title">
                  <img class="match-title-bg" :src="matchTitleBg4" alt="" />
                  <span :style="{ 'z-index': 1, 'font-size': '18px', color: '#fff',}">附加赛</span>
                </div>
                <div>
                  <div class="match-title-mt">比赛时间：2024年11月16日</div>
                  <div class="match-title-mt">比赛形式：线上理论+线上CTF</div>
                </div>
              </div>
              <img :src="matchArrowIcon" class="match-part-arrow reverse" style="top: 48px;" />
              <div class="match-part-left-item">
                <div class="match-left-title">
                  <img class="match-title-bg" :src="matchTitleBg3" alt="" />
                  <span :style="{ 'z-index': 1, 'font-size': '18px', color: '#fff',}">晋级赛</span>
                </div>
                <div>
                  <div class="match-title-mt">比赛时间：2022年10月29日</div>
                  <div class="match-title-mt">比赛形式：线上CTF</div>
                </div>
              </div>
            </div>
            <div class="match-part-left" style="justify-content: start; padding-left: 170px; margin-bottom: 55px;">
              <img :src="matchArrowIcon" class="match-part-arrow vertical" />
            </div>
            <div class="match-part-left">
              <div class="match-part-left-item">
                <div class="match-left-title">
                  <img class="match-title-bg" :src="matchTitleBg5" alt="" />
                  <span :style="{ 'z-index': 1, 'font-size': '18px', color: '#fff',}">总决赛</span>
                </div>
                <div>
                  <div class="match-title-mt">比赛时间：2024年12月4日</div>
                  <div class="match-title-mt">比赛形式：AWD混合模式</div>
                </div>
              </div>
              <img :src="matchArrowIcon" class="match-part-arrow" style="top: 48px;" />
              <div class="match-part-left-item">
                <div class="match-left-title">
                  <img class="match-title-bg" :src="matchTitleBg6" alt="" />
                  <span :style="{ 'z-index': 1, 'font-size': '18px', color: '#fff',}">颁奖典礼</span>
                </div>
                <div>
                  <div class="match-title-mt">颁奖时间：2024年12月5日</div>
                </div>
              </div>
            </div>
            <!-- <div v-for="(item, index) in threeList" :key="index">
              <img v-if="item.isArrow" :src="item.arrowIcon" class="match-part-arrow" />
              <div v-else class="match-part-left-item">
                <div class="match-left-title">
                  <img class="match-title-bg" :src="item.titleBg" alt="" />
                  <span :style="{
                    'z-index': 1,
                    'font-size': '18px',
                    color: item.color,
                  }">{{ item.title }}</span>
                </div>
                <div>
                  <div class="match-title-mt" v-for="(itm, idx) in item.scheduleList" :key="idx">
                    {{ itm.title }}{{ itm.content }}
                  </div>
                </div>
                <el-button @click="toFiles(item.btn.url)" class="mt-10" size="mini" v-if="item.btn">{{ item.btn.title }}
                </el-button>
              </div>
            </div> -->
            <!-- <div class="match-part-wrap mt-20">
              <div class="match-part-right">
                <div class="match-right-title">命题征集</div>
                <div style="margin-bottom: 50px; text-indent: 2em">
                  诚邀选手积极参与本次大赛命题活动，关于命题标准与奖励详见指令者学院<a style="color: #fff" href="https://cmdr.com.cn/"
                    target="blank">（https://cmdr.com.cn/）</a>
                </div>
                <div class="match-right-title">裁判征集</div>
                <div class="match-list-txt" style="text-indent: 2em">
                  诚邀网络安全方向的专家和学者，推荐或报考电力行业劳动和技能竞赛裁判员，详见指令者学院<a style="color: #fff" href="https://cmdr.com.cn/"
                    target="blank">（https://cmdr.com.cn/）</a>
                </div>
                <div class="match-list-txt" style="text-indent: 2em">
                  依据《关于印发<电力行业劳动和技能竞赛裁判员管理知道意见>
                    的通知》（能源化学工发（2022）12号）要求，现面向各电力集团从业人员、历届参赛优胜选手选拔信息安全类竞赛裁判员。有意者请发送个人简历至邮箱，并联系工作人员，以便填报相关资料和参加培训取证（<EMAIL>）
                </div>
              </div>
              <img class="cursor" @click="toPdf3" src="../../assets/2022/match-file2.png"
                style="width: 262px; height: 367px; margin-left: 10px" alt="" />
            </div> -->
          </div>
        </div>
        <!-- <div class="unit-footer">
          <div>监制单位：</div>
          <div>
            <div>中国电力企业联合会科技开发服务中心</div>
            <div style="margin-top: 10px">
              “指挥官杯”能源互联网主动防御安全技能竞赛组委会秘书处
            </div>
          </div>
        </div> -->
      </div>
      <div :class="['match-side', this.scroll > 780 ? 'show' : '']">
        <div :class="['side-item', 'cursor', curId == item.id ? 'active' : '']" v-for="(item, index) in sideNav"
          :key="index" @click="scrollToNav(item.id)">
          <div class="side-item-bg">{{ item.name }}</div>
        </div>
      </div>
      <el-button type="primary" class="return-btn" @click="returnLast">返回</el-button>
    </div>
    <rankingBoard :visible.sync="rankingShow"></rankingBoard>
    <playerBoard :visible.sync="playerShow"></playerBoard>
    <wrFileUpload :visible.sync="uploadShow" :ts="ts" :te="te"></wrFileUpload>
  </div>
</template>
<script>
import { throws } from 'assert';

export default {
  data() {
    return {
      matchArrowIcon: require("../../assets/2022/match-arrow-icon.png"),
      matchTitleBg1: require("../../assets/2022/match-title-bg-red1.png"),
      matchTitleBg2: require("../../assets/2022/match-title-bg-red2.png"),
      matchTitleBg3: require("../../assets/2022/match-title-bg-red3.png"),
      matchTitleBg4: require("../../assets/2022/match-title-bg3.png"),
      matchTitleBg5: require("../../assets/2022/match-title-bg2.png"),
      matchTitleBg6: require("../../assets/2022/match-title-bg6.png"),
      isclick: 1,
      matchWatchFlag: false,
      matchList: [],
      reference: {
        topic: 1826,
        member: 1969,
        team: 630,
        answer: 68625,
      },
      hostList: [
        "中国电力企业联合会大数据与统计分会",
        "中国电力企业联合会科技服务中心有限责任公司",
        "中国工业互联网研究院",
        "中国电力科学研究院有限公司",
        "南方电网数字电网集团有限公司",
        "南方电网科学研究院有限责任公司"
      ],
      technologyList: [
        "国网思极网安科技（北京）有限公司",
        "南方电网数字电网集团信息通信科技有限公司",
        "中电运行（北京）信息技术有限公司",
        "中互协（北京）检测认证中心有限公司",
        "奇安信科技集团股份有限公司",
        "恒安嘉新（北京）科技股份公司",
        "武汉安域信息安全技术有限公司",
      ],
      threeList: [
        {
          titleBg: require("../../assets/2022/match-title-bg1.png"),
          title: "预选赛",
          color: "#fff",
          scheduleList: [
            { title: "比赛时间：", content: "2022年9月23日-24日" },
            {
              title: "比赛形式：",
              content: "线上理论答题，随机抽题",
            },
          ],
        },
        {
          isArrow: true,
          arrowIcon: require("../../assets/2022/match-arrow-icon.png"),
        },
        {
          titleBg: require("../../assets/2022/match-title-bg1.png"),
          title: "晋级赛",
          color: "#fff",
          scheduleList: [
            { title: "比赛时间（拟）：", content: "2022年10月29日" },
            {
              title: "比赛形式：",
              content: "线上CTF",
            },
          ]
        },
        {
          isArrow: true,
          arrowIcon: require("../../assets/2022/match-arrow-icon.png"),
        },
        {
          titleBg: require("../../assets/2022/match-title-bg3.png"),
          title: "总决赛",
          color: "#333",
          scheduleList: [
            { title: "比赛时间（拟）：", content: "2024年5月21日" },
            {
              title: "比赛形式：",
              content: "AWD混合模式",
            },
            {
              title: "比赛地点：",
              content: "北京",
            },
          ]
        },
      ],
      sideNav: [
        {
          name: "关于大赛",
          id: "nav1",
          top: 0,
        },
        {
          name: "组织单位",
          id: "nav2",
          top: 0,
        },
        {
          name: "参赛对象",
          id: "nav3",
          top: 0,
        },
        {
          name: "奖项设置",
          id: "nav4",
          top: 0,
        },
        {
          name: "赛程安排",
          id: "nav5",
          top: 0,
        },
      ],
      scroll: 0,
      curId: "nav1",
      enterBtnUrl: "",//比赛入口
      gzrk:'',
      el: '',
      rankingShow: false,
      playerShow: false,
      uploadShow: false,
      userInfo: null,
      //观众入口动态按钮
      audience_entrance: null,
      ts:'',
      te:''
    };
  },
  mounted() {
      this.getConfig()
 
    // this.getUserInfo()
    this.el = document.querySelector(".layout"); // 获取default页面，监听滚动
    this.el.addEventListener("scroll", this.handleScroll);
    this.$nextTick(() => {
      this.sideNav = this.sideNav.map((item) => {
        if (item.id == "nav6") {
          item.top = 0;
        } else {
          let targetbox = document.getElementById(item.id);
          item.top = targetbox.offsetTop - 30;
        }
        // console.log('targetbox', item);
        return item;
      });
    });

    this.matchList = [
      // { name: '报名入口', matchFlag: true, id: 1 },
      // { name: '排行榜', matchFlag: false, id: 3 },
      // { name: '选手查询', matchFlag: false, id: 4 },
      // { name: 'WP上传', matchFlag: false, wrFile: true, id: 5 },
      // { name: '观众入口', matchFlag: false, watchFlag: true, id: 2 },
    ]
  },
  beforeDestroy() {
    this.checkBtn && clearInterval(this.checkBtn)
    this.el.removeEventListener("scroll", this.handleScroll, true);

  },
  methods: {
    // 获取首页数据
    playerShowFn() {
      if (!this.$store.state.userInfo) {
        this.$message.warning('查看选手信息，请先登录')
        this.$router.push('/login')
        return
      }

      this.playerShow = true

    },
    rankingShowFn() {
      // if (!this.$store.state.userInfo) {
      //   this.$message.warning('查看晋级名单，请先登录')
      //   this.$router.push('/login')
      //   return
      // }
      this.rankingShow = true
    },
    async getUserInfo() {
      await this.$axios({
        method: "get",
        url: this.$api.userQueryMessage,
        params: { token: localStorage.getItem('token') || '' }
      }).then(res => {
        if (res.data.code == 200) {
          this.userInfo = res.data.data
        }
      }).catch(err => { });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => { });
    },
    handleScroll() {
      this.scroll = this.el.scrollTop
      for (let i = 0; i < this.sideNav.length; i++) {
        if (this.scroll >= this.sideNav[i].top - 30) {
          this.curId = this.sideNav[i].id;
        }
      }
    },
    scrollToNav(id) {
      this.curId = id;
      if (id == "nav6") {
        this.el.scrollTop = 0;
        return;
      }
      this.$nextTick(() => {
        let targetbox = document.getElementById(id);
        this.target = targetbox.offsetTop - 30;
        this.el.scrollTop = this.target;
      });
    },
    toPdf1() {
      // window.open("https://cmdr.com.cn:8443/img/%E7%AC%AC%E4%B8%89%E5%B1%8A%E2%80%9C%E6%8C%87%E6%8C%A5%E5%AE%98%E6%9D%AF%E2%80%9D%E8%83%BD%E6%BA%90%E4%BA%92%E8%81%94%E7%BD%91%E4%B8%BB%E5%8A%A8%E9%98%B2%E5%BE%A1%E5%AE%89%E5%85%A8%E6%8A%80%E8%83%BD%E7%AB%9E%E8%B5%9B%E7%9A%84%E9%80%9A%E7%9F%A5.pdf");
      window.open("https://cmdr.com.cn:8443/img/about.pdf");
    },
    toPdf2() {
      window.open(
        "https://cmdr.com.cn:8443/img/%E5%8E%86%E5%8F%B2%E8%B5%9B%E4%BA%8B%E6%96%87%E4%BB%B6.pdf"
      );
    },
    toPdf3() {
      window.open(
        "https://cmdr.com.cn:8443/img/%E5%85%B3%E4%BA%8E%E5%8D%B0%E5%8F%91%E7%94%B5%E5%8A%9B%E8%A1%8C%E4%B8%9A%E5%8A%B3%E5%8A%A8%E5%92%8C%E6%8A%80%E8%83%BD%E7%AB%9E%E8%B5%9B%E8%A3%81%E5%88%A4%E5%91%98%E7%AE%A1%E7%90%86%E6%8C%87%E5%AF%BC%E6%84%8F%E8%A7%81%E7%9A%84%E9%80%9A%E7%9F%A5.pdf"
      );
    },
    toFiles(v) {
      window.open("https://cmdr.com.cn:8443/img/%E5%85%B3%E4%BA%8E%E5%85%AC%E5%B8%83%E7%AC%AC%E4%B8%89%E5%B1%8A%E2%80%9C%E6%8C%87%E6%8C%A5%E5%AE%98%E6%9D%AF%E2%80%9D%E8%83%BD%E6%BA%90%E4%BA%92%E8%81%94%E7%BD%91%E4%B8%BB%E5%8A%A8%E9%98%B2%E5%BE%A1%E5%AE%89%E5%85%A8%E6%8A%80%E8%83%BD%E5%A4%A7%E8%B5%9B%E9%A2%84%E9%80%89%E8%B5%9B%E6%99%8B%E7%BA%A7%E5%90%8D%E5%8D%95%E5%8F%8A%E6%99%8B%E7%BA%A7%E8%B5%9B%E3%80%81%E5%86%B3%E8%B5%9B%E6%97%B6%E9%97%B4%E5%AE%89%E6%8E%92%E7%9A%84%E9%80%9A%E7%9F%A5-V3.0.pdf");
    },
    toRules() {
      // window.open("https://cmdr.com.cn:8443/img/blog/admin/pdf/2024/3/11/1767121001597075457.pdf");
      window.open("https://cmdr.com.cn:8443/img/rule.pdf");
    },
    
    async getConfig() {
      //获取比赛按钮配置信息
      await this.$axios({
        method: "post",
        url: this.$api.homeResource,
        data: ["PC_SIGN_BUTTON"]
      }).then((res) => {
        if (res.data.data.PC_SIGN_BUTTON.length) {
          res.data.data.PC_SIGN_BUTTON.forEach(i => {
            if (i.picEnName === 'WP_UPLOAD') {
              let t=i.picDefinedName.split(',')
              this.ts=t[0]
              this.te=t[1]
            }
          })
          this.matchList = res.data.data.PC_SIGN_BUTTON
          this.enterBtnUrl = res.data.data.PC_SIGN_BUTTON[0].linkUrl
          this.gzrk=res.data.data.PC_SIGN_BUTTON[4].linkUrl
        }
      });
    },
    //获取观众入口动态按钮
    async getSmallMatchList() {
      if (!this.$store.state.userInfo) {
        this.$message.warning('查看选手信息，请先登录')
        this.$router.push('/login')
        return
      }
      await this.$axios.get(this.$api.getSmallMatchList).then(res => {
        this.audience_entrance = null
        if (res.data.code === 200) {
          // console.log("xxx", this.audience_entrance)
          this.audience_entrance = res.data.data.homeStageMatchVos
        }
      })
      this.matchWatchFlag = true
    },
    //跳转比赛入口
    async getToken() {
      if (!this.userInfo) {
        //请求接口失败
        //  this.$message.warning('用户身份校验失败')
        await this.getUserInfo()
      }
      // console.log('this.userInfo', this.userInfo);
      if (!this.userInfo.realName || !this.userInfo.isAutonym) {
        this.$confirm("你还没进行实名认证,是否前去实名认证?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$router.push("/userCenter/identity");
        });
        return
      }

      //请求接口成功
      if (process.server) return;
      const token = localStorage.getItem('token')
      window.open(`${this.enterBtnUrl}/authorized?token=${token}&type=matchlist`)
      // this.$axios({
      //   method: "get",
      //   url: this.$api.userGetToken
      // }).then((res) => {
      //   if (res.data.code === 200) {
      //     // console.log('this.enterBtnUrl', this.enterBtnUrl);
      //     // console.log(`${this.enterBtnUrl}&token=${res.data.data}&source=1`);
      //     // window.open(`${this.enterBtnUrl}&token=${res.data.data}&source=1&x=${this.$buildUuid()}`);
      //     window.open(`${this.enterBtnUrl}&token=${res.data.data}&source=1`);
      //     // console.log('比赛入口',`${this.enterBtnUrl}&token=${res.data.data}&source=1`);
      //   }
      // });
    },
    gotoMatch() {
      if (!this.enterBtnUrl) {
        this.$message.warning('比赛暂未开始！')
        return
      }
      this.getToken()
    },
    returnLast() {
      this.$router.push('/');
      // this.$router.go(-1);
    },
    closeSubBtn() {
      this.matchWatchFlag = false
      // console.log('关闭毙了');
    },
    handleMatchClick(item) {
      //点击比赛入口
      item.picEnName === 'SIGN_ENTRY' && this.gotoMatch()
      //点击排行榜
      item.picEnName === 'RANK' && this.rankingShowFn()
      //点击选手查询
      item.picEnName === 'PLAYER_QUERY' && this.playerShowFn()
      //点击赛制规则
      item.picEnName === 'COMPETITION_RULES' && this.toRules()
      //点击观众入口
      item.picEnName === 'AUDIENCE_ENTRANCE' && this.getSmallMatchList()
      //点击WP上传
      item.picEnName === 'WP_UPLOAD' && this.handleWrUpload()
    },
    //用户入口点击
    audienceEntrance(v) {
      if (!this.$store.state.userInfo) {
        this.$message.warning('观看比赛,请先登录')
        this.$router.push('/login')
        return
      }
      if (v.statusDetail < 3) {
        this.$message.warning('比赛暂未开始。')
        return
      }
      if (v.statusDetail ===4) {
        this.$message.warning('比赛已结束。')
        return
      }
      if (process.server) return;
      this.$axios({
        method: "get",
        url: this.$api.userGetToken
      }).then((res) => {
        if (res.data.code === 200) {
          // console.log('跳转url', `${this.gzrk}&token=${res.data.data}&source=1&smallMatchId=${v.id}&smallMatchType=${v.tag}&type=3`);
          window.open(`${this.gzrk}&token=${res.data.data}&source=1&smallMatchId=${v.id}&smallMatchType=${v.tag}&type=3`);
          // window.open("https://zhgb.powerun.cn:1443/matchRegister/matchDetail/?matchId=29df6a09e2ab4bc98ce214505967e2c7&token=powerun%3BeyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMDZkNzI1ZTMwZWExNWIwNmU2YTNiYzBhN2Y2NTc2NmYiLCJ1c2VybmFtZSI6IjE4MTY5NjA4MDEwQHFxLmNvbSIsInBhc3N3b3JkIjoiNWIwOWNiZWU5Njc0NjY2Njg1MDMwZjk1N2U3NTgxZjEiLCJ1c2VyUGhvbmUiOiIxODE2OTYwODAxMCIsInVzZXJFbWFpbCI6IjEwNzQyMDg5QHFxLmNvbSIsInJlYWxuYW1lIjoi546L5a6-5p6XIiwiaWRjYXJkIjoiNDUyNjAxMTk4ODA5MzAwOTE1Iiwibmlja25hbWUiOiLpq5jlpKflqIHnjJvlsI_lj6_niLEiLCJoZWFkUG9ydHJhaXQiOiJodHRwczovLzEwLjQwLjAuMzU6ODYwMC9ibG9nL3dlYi9qcGcvMjAyMi85LzE0LzE2NjMxMTc0OTEwMDMuanBnIiwiaWRDYXJkRnJvbnRJbWciOiJodHRwczovLzEwLjQwLjAuMzU6ODY3Ny9ibG9nL3dlYi9qcGcvMjAyMi85LzEvMTY2MjAyOTIxNzk1OC5qcGciLCJpZENhcmRCYWNrSW1nIjoiaHR0cHM6Ly8xMC40MC4wLjM1Ojg2NzcvYmxvZy93ZWIvanBnLzIwMjIvOS8xLzE2NjIwMjkyMDI3MjMuanBnIiwiY2hlY2tGbGFnIjoiMTY2Njk3NjEwNzMxMDQ4MmYzOWVmY2ZmZDRhNWI5NmZlYzk5NjNlOWJlODRmIiwiaXNzIjoicmVzdGFwaXVzZXIiLCJhdWQiOiIwOThmNmJjZDQ2MjFkMzczY2FkZTRlODMyNjI3YjRmNiIsImV4cCI6MTY2NzAxOTMwNywibmJmIjoxNjY2OTc2MTA3fQ.vtx0BNpETkTYcLG5QUn1EIDLd4k06UIrQwwGqXdXZQ8&source=1&smallMatchId=WyGpa5fuTc6aABC1gX5p1A&smallMatchType=2&type=3")

        }
      });
    },
    handleWrUpload() {
      if (!this.$store.state.isLogin) {
        this.$confirm('请先登录后，再进入', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '再逛逛',
          type: 'warning'
        }).then(() => {
          this.$router.push({ path: '/login' })
        }).catch(() => {
          return
        });
      } else {
        this.uploadShow = !this.uploadShow
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.gift-left {
  width: 400px;
  // height: 510px;
  font-size: 18px;
  background-image: url("../../assets/2022/match-left-bg.png");
  background-size: 100% 100%;
  @include flex(flex-start, center, column);
  padding: 20px;
}

.gift-right {
  flex: 1;
  margin-left: 30px;
}

.match-wrapper {
  min-width: 1440px;
  background-color: #fff;
}

.match-content {
  background-image: url("../../assets/2022/match_bg1.jpg");
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
  width: 100%;
  max-width: 1920px;
  height: 3850px;
  margin: 0 auto;
  position: relative;
}

.match-time-box {
  background-image: url("../../assets/2022/match_titleBg.png");
  height: 27px;
  line-height: 27px;
  text-align: center;
  letter-spacing: 1.2px;
  width: 683px;
  margin: 0 auto;
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #000032;
}

.type-area {
  position: relative;
  width: 1000px;
  margin: 0 auto;

  #nav1,
  #nav2,
  #nav3,
  #nav4 {
    width: 1000px;
  }
}

.empty-box {
  width: 100%;
  height: 600px;
}

.reference-data-box {
  margin-top: 512px;
  width: 100%;
  height: 170px;
  background: rgba(9, 80, 250, 0.4);
  border-radius: 10px;
}

.reference-data-count {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0 10px 0;
  color: #333;
}

.reference-data-item {
  text-align: center;
  min-width: 220px;
  font-size: 36px;
  line-height: 40px;
  font-family: "Microsoft YaHei";
  color: #333;
}

.cross-line {
  display: inline-block;
  width: 1px;
  height: 98px;
  background-color: #ffffff;
  border-radius: 50%;
}


.entry-box {
  width: 1100px;
  height: 62px;

  >div {
    width: 189px;
    height: 61px;
    line-height: 61px;
    margin-bottom: 30px;
    background-size: 100%;
    font-size: 24px;
    color: #ffffff;
    font-family: Source Han Serif CN;
    // font-weight: 800;
    font-style: italic;
    text-align: center;
    cursor: pointer;
    margin-right: 20px;
    letter-spacing: 0.1em;
    position: relative;
    &.sign_enter {
      line-height: 45px;
    }
    &:hover {
      // filter: drop-shadow(0px 0px 10px #999)
      // box-shadow: 0px 0px 10px #333;
    }

    &:active {
      // filter: drop-shadow(0px 0px 10px #666)
    }
    .title-des {
      width: 100%;
      text-align: center;
      font-size: 13px;
      position: absolute;
      line-height: 13px;
      top: 38px;
    }
  }

  .match {
    position: relative;

    .match_div {
      width: 184px;
      // height: 160px;
      position: absolute;
      top: 72px;
      z-index: 999;
      padding: 15px;
      background-image: url("~/assets/2022/match_watch.png");
      background-size: 100% 100%;

      div {
        border-radius: 8px;
        background: #0651ED;
        height: 60px;
        line-height: 60px;
        font-size: 20px;
        font-style: normal;
        margin-top: 15px;
        letter-spacing: 0px;

      }

      div:first-child {
        margin-top: 0px !important;
      }

    }
  }

  .official-div {
    background-image: url("~/assets/2022/match_bg.png");
  }

  .audience-div {
    background-image: url("~/assets/2022/rank_bg.png");
  }

  // >div {
  //   width: 207px;
  //   height: 62px;
  //   line-height: 54px;
  //   margin-bottom: 30px;
  //   background-size: 100%;
  //   font-size: 24px;
  //   color: #ffffff;
  //   font-family: Source Han Serif CN;
  //   font-weight: 800;
  //   font-style: italic;
  //   text-align: center;
  //   cursor: pointer;
  //   margin-right: 90px;
  // }

  >div:last-child {
    margin-right: 0px;
  }

  .official-entry {
    background-image: url("~/assets/2022/match_enter_bg.png");
  }

  .official-board {
    background-image: url("~/assets/2022/match_ranking_bg.png");
  }

  .official-query {
    background-image: url("~/assets/2022/match_query_bg.png");
  }
}


.outer_circle {
  width: 100%;
  height: 200px;
  border: 4px solid transparent;
  border-radius: 20px;
  border-image: linear-gradient(80deg, #e25add, #7ca4ff) 4 4;
  clip-path: inset(0 round 20px);
}

._about {
  border-radius: 20px;
  position: relative;
  width: 999px;
  height: 607px;
  padding-top: 40px;
  margin-top: 65px;
  border: 4px solid #7ca4ff;
  background: rgba(6, 81, 237, 0.4);
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
}

.aboutContent {
  @include flex(flex-start, flex-start, row);
  width: 900px;
  font-size: 16px;
  color: #fff;
  text-indent: 2em;
  line-height: 50px;
  padding: 0 30px;
}

.titles {
  width: 446px;
  height: 67px;
  font-size: 33px;
  line-height: 67px;
  text-align: center;
  color: #fff;
  position: absolute;
  top: -30px;
  left: 276px;
  background: url("../../assets/2022/part-title.png");
}

.aboutDetial {
  width: 700px;
  padding: 10px 60px;
  margin-top: 30px;
  background: linear-gradient(0deg,
      rgba(0, 180, 255, 0.4),
      rgba(9, 80, 250, 0.4));
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  border-radius: 20px;
  color: #fff;
  font-size: 14px;
  line-height: 36px;

  a {
    color: #fff;
    font-weight: bold;
  }
}

.return-btn {
  position: absolute;
  top: 16px;
  right: 40px;
}

.organization {
  width: 100%;
  height: 320px;
  border: 4px solid #7ca4ff;
  background: rgba(6, 81, 237, 0.4);
  border-radius: 20px;
  margin-top: 60px;
  position: relative;
  flex-direction: row;
  display: flex;
  align-items: center;
  padding-top: 50px;

  .guidance {
    height: 100%;
    // flex-direction: column;
    display: flex;
    flex: 1;

    .guidance_title {
      width: 50px;
      height: 170px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      background-image: linear-gradient(rgba(6, 81, 237, 1),
          rgba(6, 81, 237, 1)),
        linear-gradient(80deg, #e25add, #7ca4ff);
      background-clip: content-box, padding-box;
      padding: 2px;
      border-radius: 30px;
      text-align: center;
      line-height: 35px;
      font-size: 25px;
      font-weight: bold;
      margin-top: 20px;
      color: #ffffff;
      margin-bottom: 15px;
      margin-left: 20px;
    }

    .guidance_text {
      flex-direction: column;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      margin-left: 40px;

      ul {
        li {
          color: #fff;
          font-size: 15px;
          line-height: 35px;
          padding-left: 20px;
          background-image: url("../../assets/2022/li_start.png");
          background-size: 10px 10px;
          background-repeat: no-repeat;
          background-position: 0px 13px;
        }
      }
    }
  }
}

.match-obj {
  position: relative;
  width: 100%;
  border: 4px solid #7ca4ff;
  background: rgba(6, 81, 237, 0.4);
  border-radius: 20px;
  margin-top: 60px;
}

.match-part {
  padding: 100px 30px 30px;
  color: #ffffff;
}

.match-part-list {
  padding-left: 38px;
  position: relative;
  color: #fff;
}

.match-left-title {
  position: relative;
  @include flex(center, center, row);
  width: 180px;
  height: 36px;

  .match-title-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    &.reverse {
      transform: rotateY(180deg);
    }
  }
}

.match-part-left {
  @include flex(space-evenly, flex-start, row);
  margin-bottom: 40px;
}

.match-part-arrow {
  width: 63px;
  height: 30px;
  position: relative;
  top: 3px;
  right: 28px;
  &.reverse {
    transform: rotateY(180deg);
  }
  &.vertical {
    transform: rotate(90deg);
  }
}

.match-part-left-item {
  width: 250px;
  @include flex(flex-start, flex-start, column);
  margin-bottom: 10px;
}

.match-part-wrap {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.match-part-right {
  width: 614px;
  height: 366px;
  padding: 0 20px;
  border-radius: 20px;
  background-image: url("../../assets/2022/match-right-bg.png");
  background-size: 100% 100%;
  @include flex(center, center, column);
  color: #fff;
}

.match-title-mt {
  margin-top: 10px;
  font-size: 15px;
}

.match-right-title {
  margin-bottom: 15px;
  font-weight: bold;
}

.unit-footer {
  @include flex(center, flex-start, row);
  margin-top: 60px;
  font-weight: 600;
}

.match-list-image {
  display: block;
  position: absolute;
  top: 0px;
  left: 0;
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  font-size: 14px;
  color: #fff;
  background-image: url("../../assets/2022/li_start.png");
}

.match-list-title {
  font-size: 16px;
  font-weight: bold;
  line-height: 32px;
}

.match-list-txt {
  font-size: 16px;
  line-height: 28px;
}

.special-box {
  margin: 0 12px 48px 12px;
  background-color: rgba(68, 125, 236, 1);
  box-shadow: 0px 43px 250px 0px #0651ed;
  border-radius: 20px;
  overflow: hidden;

  .match-part-list {
    padding-top: 10px;
    margin: 0 10px;

    .match-list-image {
      top: 20px;
    }
  }
}

.match-part-list {
  .match-list-image {
    top: 3px;
  }
}

.match-list-titleBig {
  font-size: 24px;
  line-height: 40px;
}

.match-list-txtBig {
  font-size: 18px;
  line-height: 32px;
}

.awards-box {
  margin-top: 60px;

  img {
    width: 100%;
    display: block;
    margin-left: -1px;
  }
}

.schedule {
  width: 100%;
  background: rgba(6, 81, 237, 0.4);
  border: 4px solid #7ca4ff;
  border-radius: 20px;
  position: relative;
  margin-top: 92px;
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;

  .tabs {
    width: 405px;
    height: 56px;
    position: absolute;
    top: -60px;
    right: 30px;
    flex-direction: row;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .tab {
      width: 135px;
      height: 56px;
      text-align: center;
      line-height: 56px;
      color: #fff;
      font-size: 22px;
      cursor: pointer;
      background: url("../../assets/2022/blue_tab.png") no-repeat;
    }

    .tabNoClick {
      width: 135px;
      height: 56px;
      text-align: center;
      line-height: 56px;
      color: #260202;
      font-size: 22px;
      cursor: pointer;
      background: url("../../assets/2022/white_tab.png") no-repeat;
    }
  }

  .titles {
    top: -30px;
    left: 23px;
  }

  ul {
    margin-top: 52px;
    width: 919px;
    height: 330px;
    overflow: auto;

    li {
      display: grid;
      grid-template-columns: 40px auto;
      color: #333;

      .numberLi {
        width: 40px;
        height: 22px;
        color: #fff;
        text-align: center;
        position: relative;
        background: url("../../assets/2022/li_start.png") no-repeat;
        margin-top: 3px;

        span {
          position: absolute;
          top: 1px;
          left: 8px;
        }
      }

      .rightLi {
        line-height: 30px;

        .scheduleTitle {
          font-size: 16px;
          font-weight: 700;
        }

        .scheduleContent {
          font-size: 16px;
          line-height: 26px;
          word-break: break-word;
        }
      }
    }
  }
}

.match-side.show {
  @include flex(space-around, center, column);
  position: fixed;
  right: 100px;
  top: 50%;
  transform: translateY(-50%);
  width: 160px;
  height: 466px;
  background: url("../../assets/2022/match_size_bg1.png") center no-repeat;
  background-size: 100% 100%;
  padding: 64px 14px;
  z-index: 999;

  .side-item {
    position: relative;
    width: 145px;
    height: 25%;
    background-color: transparent;
    text-align: center;
    line-height: 60px;
    font-size: 24px;
    background-image: linear-gradient(to right, #83cdff, #ffffff);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    border-bottom: 1px solid #83b0dc;
  }

  .side-item.active {
    .side-item-bg {
      height: 100%;
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  .side-item:last-child .side-item-bg::after {
    content: "";
    display: block;
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 10px;
    background-size: 100% 100%;
    z-index: 3;
  }
}

.match-side {
  display: none;
}

.match-footer {
  margin-top: 368px;
  color: #ffffff;
  font-size: 16px;
  line-height: 30px;
  display: flex;
  justify-content: center;
}

.reference-used {
  text-align: center;
  color: #063fb4;
  font-size: 16px;
}

.dec-txt {
  font-size: 16px;
  font-weight: 400;
}

.school-obj {
  height: 340px;

  .title {
    background-color: #0950fa;
    color: #fff;
    text-align: center;
    letter-spacing: 10px;
  }

  .honor-box {
    img {
      width: 83px;
      height: auto;
    }

    >p {
      color: #fff;
    }

  }

}
</style>
