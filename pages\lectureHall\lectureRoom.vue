<template>
    <div class="lectureRoom">
      <div class="flex jc-center">
          <div class="w-1440 flex">
                <!-- 左侧 -->
                <div class="flex-8 flex-col pr-20" style="overflow: hidden;">
                    <div class="flex ai-center">
                        <!-- <img class="mr-10 live-icon1" src="@/assets/lectureHall/liveIcon.svg" /> -->
                        <p class="font24 f-w-600">直播</p>
                    </div>
                    <div class="w-100 mt-20" v-if="liveList.length != 0">
                        <liveCard :list="liveList" :checkData.sync="livaData" @changeCard="changeCard"></liveCard>
                    </div>
                    <div class="w-100 mt-20" v-else>
                        <el-empty :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
                    </div>
                    <div class="mt-30">
                        <div class="flex ai-center">
                            <p class="font24 f-w-600">直播详情</p>
                            <img class="ml-10 live-icon1" :src="require('@/assets/lectureHall/title-icon2.svg')" />
                        </div>
                        <div class="mt-30 pl-20">
                            <div class="font-14 f-w-400" v-html="livaData.content || ''">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 右侧 -->
                <div class="flex-2 flex-col" v-if="livaData.uid">
                    <div class="flex ai-center">
                        <p class="font24 f-w-600">关注Ta</p>
                        <img class="ml-10 live-icon1" src="@/assets/lectureHall/title-icon.svg" />
                    </div>
                    <div class="mt-20">
                        <followingCard :livaData="livaData" @followingPersonOption="followingPersonOption"
                            @followingEnterpriseOption="followingEnterpriseOption"></followingCard>
                    </div>
                    <div class="mt-25">
                        <div class="flex ai-center">
                            <p class="font24 f-w-600">直播海报</p>
                            <img class="ml-10 live-icon1" src="@/assets/lectureHall/title-icon1.svg" />
                        </div>
                        <div class="mt-25" style="width: 280px;">
                            <img style="width: 280px;" :src="livaData.livePosterPath">
                        </div>
                    </div>
                </div>
                <div class="flex-2 flex-col" v-else>
                    <el-empty :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
                </div>
          </div>
        </div>
        <el-dialog :visible.sync="dialogPlayback" :before-close="handleClose">
            <video v-if="playbackPath" width="100%" ref="vueMiniPlayer" height="100%" controls>
                <source :src="playbackPath" type="video/mp4" />
            </video>
        </el-dialog>
        <!-- <div class="qrcode-body" @click="qrcodeShow = false" v-show="qrcodeShow">
            <div class="qrcode-box" v-if="qrcodeShow">
                <img class="w-100 h-100" :src="$getFullImage(qrCodePath)">
            </div>
            <div class="qrcode-path mt-15" v-if="qrcodeShow">
              
            </div>
        </div> -->

        <!-- 直播间二维码 430px-->
        <hyDialog2 :visible.sync="qrcodeShowOne" @dataReset="qrcodeShowOne = false" width="450px" height="345px">
            <template slot="title">
                <div class="color-black font-16 f-w-bold">{{ liveStatus == 3 ? "扫码进入直播间" : '扫码加入群聊' }} </div>
            </template>
            <template slot="content">
                <div class="flex ai-center jc-center" v-if="qrCodePath">
                    <img style="width: 200px;height: 180px;" :src="qrCodePath">
                </div>
                <div v-else></div>
            </template>
            <template slot="footer">
                <div class="flex-col w-100 h-100" v-if="liveAddress">
                    <p class="font14 f-w-500">点击下方直播间链接,进入直播间</p>
                    <div style="margin-top: 5px;">
                        <p class="ellipsis hover_line cursor" @click="openLink()" style="color: #006eff;">{{ liveAddress }}
                        </p>
                    </div>
                </div>
                <div v-else></div>
            </template>
        </hyDialog2>
        <hyDialog2 :visible.sync="qrcodeShowTwo" @dataReset="qrcodeShowTwo = false" width="450px" height="210px">
            <template slot="title">
                <div class="color-black font-16 f-w-bold">点击下方直播间链接,进入直播间</div>
            </template>
            <template slot="content">
                <p class="ellipsis hover_line cursor" @click="openLink()" style="color: #006eff;">{{ liveAddress }}
                </p>
            </template>
            <template slot="footer">
                <div style="height: 0px;"></div>
            </template>
        </hyDialog2>
    </div>
</template>
<script>
import followingCard from './component/followingCard.vue'
import liveCard from './component/liveCard.vue'
import LiveCarousel from './component/liveCarousel.vue'
export default {
    name: '',
    components: {
        followingCard,
        liveCard
    },
    data() {
        return {
            liveStatus: 1,
            qrcodeShowTwo: false,
            qrcodeShowOne: false,
            liveAddress: '',
            playbackPath: '',
            dialogPlayback: false,
            qrCodePath: '',
            qrcodeShow: false,//二维码弹窗
            switchShow: true,
            typeFlag: 1,
            typeList: [
                { uid: 1, name: '直播详情' },
                { uid: 2, name: '直播海报' }
            ],
            liveList: [],
            livaData: {
                isSubscribed: 0,
                classroomSpeaker: {
                    isSubscribed: 0,
                }
            },
            currentIndex: 0,
        }
    },
    computed: {

    },
    created() {
        this.judgeEnter()
    },
    methods: {
        //跳转直播间
        openLink() {
            window.open(this.liveAddress)
        },
        //1关注/ 0取关企业
        followingEnterpriseOption(e) {
            this.$debounce(() => {
                e.type == 1 ? this.followingEnterprise(e.uid) : this.unFollowingEnterprise(e.uid)
            }, 1500)();
        },
        //1关注/ 0取关讲师
        followingPersonOption(e) {
            e.type == 1 ? this.followingPerson(e.uid) : this.unFollowingPerson(e.uid)
        },
        //卡片按钮操作
        changeCard(item) {
            console.log('卡片按钮操作', item)
            this.qrCodePath = ''
            this.liveAddress = ''
            this.playbackPath = ''
            this.liveStatus = item.liveStatus
            const { liveStatus } = item
            //1待开播 判断是否预约过 存放直播间uid 
            const liveOne = () => {
                //如果没有登录才进行缓存Id
                if (this.$store.getters.isLogin) {
                    item.isSubscribed == 0 ? this.followingAppointmentLive(item.uid) : this.$message.success('已预约过直播间')
                } else {
                    let cachedData = JSON.parse(localStorage.getItem('liveuis')) || [];
                    if (cachedData.includes(item.uid)) {
                        this.$message.success('已预约过直播间')
                        return
                    } else {
                        this.followingAppointmentLive(item.uid)
                        cachedData.push(item.uid)
                        window.localStorage.setItem('liveuis', JSON.stringify(cachedData))
                    }
                }
            }
            //2即将开播 如果有直播群二维码就弹出来
            const liveTwo = () => {
                console.log(item.grCodePath);
                if (item.qrCodePath) {
                    this.qrCodePath = item.qrCodePath
                    this.qrcodeShowOne = true
                }
            }
            //3直播中 有直播链接就跳，没有的话就弹出二维码
            const liveThree = () => {
                if (item.liveCodePath) {
                    this.qrCodePath = item.liveCodePath
                    this.liveAddress = item.liveAddress || ''
                    this.qrcodeShowOne = true
                }
                if (item.liveAddress && !item.liveCodePath) {
                    this.liveAddress = item.liveAddress
                    console.log('直播间地址', item.liveAddress, this.liveAddress)
                    this.qrcodeShowTwo = true
                }
            }
            //5 已结束 有回放就弹出回放视频
            const liveFive = () => {
                if (item.playbackPath) {
                    this.playbackPath = item.playbackPath;
                    this.dialogPlayback = true;
                } else {
                    this.$message.warning('暂无回放视频')
                }
            }
            switch (liveStatus) {
                case 1:
                    liveOne()
                    break;
                case 2:
                    liveTwo()
                    break;
                case 3:
                    liveThree()
                    break;
                case 5:
                    liveFive()
                    break;
            }
        },
        handleClose(done) {
            this.playbackPath = ''
            done();
        },
        //判断进入路由id
        judgeEnter() {
            if (this.$route.query?.liveUid) {
                this.getLiveList(this.$route.query?.liveUid)
            } else {
                this.getLiveList()
            }
        },
        //关注企业
        followingEnterprise(enterpriseUid) {
            this.$request.lectureHall.followingEnterpriseData({ enterpriseUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('关注成功')
                    this.livaData.classroomEnterprise.isSubscribed = 1
                    //如果没登录就缓存
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.push(this.livaData.classroomEnterprise.uid)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                    this.liveList.forEach(item => {
                        if (item.liveType == 2 && item.classroomEnterprise?.uid == enterpriseUid) {
                            item.classroomEnterprise.isSubscribed = 1
                        }
                    })
                }
            })
        },
        //取消关注企业
        unFollowingEnterprise(enterpriseUid) {
            this.$request.lectureHall.unFollowingEnterpriseData({ enterpriseUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('已取消关注~')
                    this.livaData.classroomEnterprise.isSubscribed = 0
                    //取消缓存数据  
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.splice(teachData.indexOf(this.livaData.classroomEnterprise.uid), 1)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                    this.liveList.forEach(item => {
                        if (item.liveType == 2 && item.classroomEnterprise?.uid == enterpriseUid) {
                            item.classroomEnterprise.isSubscribed = 0
                        }
                    })
                }
            })
        },
        //取消关注讲师
        unFollowingPerson(speakerUid) {
            this.$request.lectureHall.getLiveUnFollowing({ speakerUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('已取消关注~')
                    this.livaData.classroomSpeaker.isSubscribed = 0
                    //取消缓存数据
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.splice(teachData.indexOf(this.livaData.speakerUid), 1)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                    this.liveList.forEach(item => {
                        if (item.liveType == 1 && item.speakerUid == speakerUid) {
                            item.classroomSpeaker.isSubscribed = 0
                        }
                    })
                }
            })
        },
        //关注讲师
        followingPerson(speakerUid) {
            this.$request.lectureHall.getLiveFollowing({ speakerUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('关注成功')
                    this.livaData.classroomSpeaker.isSubscribed = 1
                    //如果没登录就缓存
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.push(this.livaData.speakerUid)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                    this.liveList.forEach(item => {
                        if (item.liveType == 1 && item.speakerUid == speakerUid) {
                            item.classroomSpeaker.isSubscribed = 1
                        }
                    })
                }
            })
        },
        //预约直播
        followingAppointmentLive(liveUid) {
            this.$request.lectureHall.speakerFollow({ liveUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('预约成功')
                    this.judgeEnter()
                }
            })
        },
        //取消预约直播
        upFollowingAppointmentLive(liveUid) {
            this.$request.lectureHall.cancelSpeakerFollow({ liveUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('取消成功')
                    this.judgeEnter()
                }
            })
        },
        // 过滤html标签
        filterContent(html) {
            if (!html) return '';
            // 匹配 <img>、<video> 等非文字数字元素的正则表达式
            var nonTextElementsRegex = /<(img|video)[^>]*>/gi;
            // 使用 replace 方法将匹配到的非文字数字元素替换为空字符串
            var result = html.replace(nonTextElementsRegex, '');
            // 去除所有 HTML 标签，只保留纯文本
            result = result.replace(/<[^>]+>/g, '');
            return result;
        },
        //获取直播列表
        async getLiveList(liveUid) {
            let result = await this.$request.lectureHall.getLiveSortList({ currentPage: 1, pageSize: 999, liveStatusList: [1, 2, 3] });
            if (result.data.code == this.$code.SUCCESS) {
                const { records } = result.data.data
                if (!this.$store.getters.isLogin) {
                    //判断是否预约过
                    let cachedData = JSON.parse(localStorage.getItem('liveuis')) || [];
                    //判断是否关注过讲师
                    let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                    records.forEach(item => {
                        //待开播 判断是否预约过 存放直播间uid
                        if (item.liveStatus == 1) {
                            item.isSubscribed = cachedData.includes(item.uid) ? 1 : 0
                        }
                        //liveType 1讲师直播 
                        if (item.liveType == 1) {
                            item.classroomSpeaker.isSubscribed = teachData.includes(item.classroomSpeaker.uid) ? 1 : 0
                        }
                        //2企业直播
                        if (item.liveType == 2) {
                            teachData.includes(item.classroomEnterprise.uid) ? item.classroomEnterprise.isSubscribed = 1 : 0
                        }
                    })
                }
                //简介过滤html标签只保留文字 以防内容撑开
                records.forEach(item => {
                    //liveType 1讲师直播 
                    if (item.liveType == 1) {
                        item.classroomSpeaker.content = item.classroomSpeaker.content ? this.filterContent(item.classroomSpeaker.content) : ''
                    }
                    //2企业直播
                    if (item.liveType == 2) {
                        item.classroomEnterprise.intro = item.classroomEnterprise.intro ? this.filterContent(item.classroomEnterprise.intro) : ''
                    }
                })
                this.liveList = records
                if (liveUid) {
                    this.livaData = this.liveList.find(item => item.uid == liveUid)
                    this.currentIndex = this.liveList.findIndex(item => item.uid == liveUid)
                } else {
                    this.livaData = this.liveList[0]
                }
                console.log('liveList', this.liveList)
                console.log('livaData', this.livaData)
            }
        },
        //切换
        changeCarousel(e) {
            this.livaData = this.liveList[e]
        }
    },
}
</script>

<style lang="scss" scoped>
.qrcode-box {
    width: 300px;
    height: 300px;
}

.hover_line {
    text-decoration: none;

    &:hover {
        text-decoration: underline;
    }
}

// .qrcode-body {
//     position: fixed;
//     z-index: 99;
//     top: 0;
//     width: 100vw;
//     height: 100vh;
// }

.lectureRoom {
    width: 100%;
    padding-top: 30px;
    background: #fff;
    padding-bottom: 100px;

    .live-icon1 {
        width: 30px;
        height: 30px;
    }

}
</style>
