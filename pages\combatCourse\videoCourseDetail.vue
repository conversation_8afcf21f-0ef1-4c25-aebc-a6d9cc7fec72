<template>
  <div class="detail-container">
    <div class="head" :style="{ minHeight: '280px', }">
      <div class="head-info" :style="{ minHeight: '280px' }">
        <div class="breadcrumb">
          <hyBreadcrumb v-if="!$route.query.showBre" :breadcrumbList="breadcrumbList"
            :activeList="[breadcrumbList.length - 2]"></hyBreadcrumb>
        </div>

        <el-tabs v-model="detailUid" @tab-click="clickCategoryItem">
          <el-tab-pane v-for="(item, index) in dataList" :key="item.uid + index" :name="item.uid">
            <span slot="label">
              <span v-if="item.studyStatus" class="color-999">
                {{ item.curriculumName.length > 15 ? item.curriculumName.substring(0, 15) + '...' : item.curriculumName }}
                <span class="font-12" style="background: #F4F6F7;padding: 3px 4px;">已学习</span>
              </span>
              <span v-else>
                {{ item.curriculumName.length > 15 ? item.curriculumName.substring(0, 15) + '...' : item.curriculumName }}
              </span>
            </span>
          </el-tab-pane>
        </el-tabs>

        <div class="mt-20 class-info">
          <div class="cover">
            <img :src="pictureUrl" alt="" srcset="">
          </div>
          <div class="right">
            <div class="title">
              <div class="font-28 color-black ellipsis2" style="max-width:650px">{{ detailInfo.curriculumName }}</div>
              <div class="ml-15 font-18 flex ai-center" style="color: #FF564D;white-space: nowrap;"
                v-if="detailInfo.sellWay != 1">
                {{ detailInfo.sellWay == 0 ? '免费' : detailInfo.sellWay == 2 ? '加密' : '指定学员' }}
              </div>
              <div class="price" v-if="detailInfo.sellWay == 1">
                <span :style="{ 'color': detailInfo.payStatus == 1 ? '#BDC7CF' : '#FF564D' }">{{ detailInfo.sellingPrice
                  == 0 ? '免费' : `￥${detailInfo.sellingPrice}` }}</span>
              </div>
              <el-tag v-if="detailInfo.payStatus == 1" type="info">已购买</el-tag>
            </div>
            <div class="desc">
              <span v-html="detailInfo.summary"></span>
            </div>
            <div class="finish-info">
              <div class="complete-item">
                <span>{{ courseLable[detailInfo.curriculumType] }}</span>
              </div>
              <div class="complete-count">
                <img class="icon-count" src="@/assets/combatCourse/videoCourseDetail/buy.svg">
                <span class="complete-desc">订阅量</span>
                <span>{{ detailInfo.subscription || 0 }}</span>
                <span>人</span>
              </div>
              <div class="complete-count">
                <img class="icon-count" src="@/assets/combatCourse/videoCourseDetail/reading.svg">
                <span class="complete-desc">知识体系</span>
                <span>{{ detailInfo.categoryName }}</span>
              </div>
              <div class="complete-count">
                <img class="icon-count" src="@/assets/combatCourse/videoCourseDetail/person.svg">
                <span class="complete-desc">贡献者</span>
                <span class="count">{{ detailInfo.userName }}</span>
              </div>
            </div>
          </div>
          <div class="pay-btn"
            v-if="detailInfo.payStatus == 0 && detailInfo.sellingPrice != 0 && detailInfo.sellWay == 1">
            <el-button @click="imPay" type="primary">
              立即购买
            </el-button>
          </div>
          <div class="pwd-btn" v-if="detailInfo.sellWay == 2">
            <el-button @click="gotoPwd" type="primary" :disabled="isFirstPassPwd">
              {{ isFirstPassPwd ? '已验证' : '输入密码' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="wrap-box">
      <div class="right" id="rightTag">

        <div class="flex">
          <div class="content-btn cursor mr-20" :class="{ 'active-btn': activeTag == item.name }"
            @click="handleClick({ name: item.name })" v-for="(item, index) in Tags" :key="index + 10000">
            {{ item.label }}
          </div>
        </div>

        <component :is="activeTag" :data="detailInfo" :detailUid="detailUid" :isFirstPassPwd.sync="isFirstPassPwd">
        </component>
      </div>

      <div class="ml-40">
        <div>
          <p class="right-title mb-20">该讲师的其他课程推荐</p>
          <template v-if="recommendLessonList.length">
            <div class="flex ai-center mb-20" @click="selectRecommendLesson(item)"
              v-for="(item, index) in recommendLessonList" :key="index + 4000">
              <div class="hover" style="margin-right:12px;">
                <img v-lazy="item.pictureUrl" alt="" srcset="">
              </div>
              <div class="flex-col curri-msg jc-center">
                <p class="title ellipsis">{{ item.curriculumName }}</p>
                <div class="info">
                  <span class="desc">{{ item.userName }}</span>
                  <span class="desc">学习人数{{ item.studyNum || 0 }}</span>
                </div>
                <span v-if="item.sellWay == 1" class="curri-price">
                  {{ item.payStatus == 1 ? '已购买' : item.sellingPrice ? "￥" + item.sellingPrice : '' }}
                </span>
                <span v-else class="curri-price">{{ item.sellWay == 0 ? '免费' : item.sellWay == 2 ? '加密' : '指定学员' }}</span>
              </div>
            </div>
          </template>
          <!-- 空 -->
          <template v-else>
            <div class="empty">
              <el-empty :image="require('@/assets/images/empty/empty.png')" description="暂无推荐课程"></el-empty>
            </div>
          </template>
        </div>

        <div class="mt-40">
          <p class="right-title mb-20 flex ai-center jc-between" @click="toMore">
            <span>课程推荐</span>
            <span class="font-12 color-999 cursor flex ai-center">
              更多
              <i class="el-icon-arrow-right"></i>
            </span>
          </p>
          <template v-if="knowledgeCurrList.length">
            <div class="flex ai-center mb-20" @click="selectRecommendLesson(item)"
              v-for="(item, index) in knowledgeCurrList" :key="index + 10">
              <div class="hover" style="margin-right:12px;">
                <img v-lazy="item.info.url" alt="" srcset="">
              </div>
              <div class="flex-col curri-msg jc-center">
                <p class="title ellipsis">{{ item.info.name }}</p>
                <div class="info">
                  <!-- <span class="desc">{{ item.userName }}</span> -->
                  <span class="desc">学习人数{{ item.info.buyNum || 0 }}</span>
                </div>
                <span class="curri-price" v-if="[0, 1].includes(item.info.sellWay)">
                  {{ item.info.payStatus == 1 ? '已购买' : item.info.sellingPrice ? "￥" +
                    item.info.sellingPrice : "免费" }}
                </span>
                <span class="curri-price" v-if="[2, 3].includes(item.info.sellWay)">{{
                  sellLable[item.info.sellWay] }}</span>
                <!-- <span v-if="item.info.sellingPrice == 1" class="curri-price">
                  {{ item.info.payStatus == 1 ? '已购买' : item.info.sellingPrice ? "￥" + item.info.sellingPrice : '' }}
                </span>
                <span v-else class="curri-price">{{ item.info.sellWay == 0 ? '免费' : item.info.sellWay == 2 ? '加密' : item.info.sellWay == 1 ? '付费' : '指定学员'
                }}</span> -->
              </div>
            </div>
          </template>
          <!-- 空 -->
          <template v-else>
            <div class="empty">
              <el-empty :image="require('@/assets/images/empty/empty.png')" description="暂无推荐课程"></el-empty>
            </div>
          </template>
        </div>
      </div>

    </div>

  </div>
</template>
<script>
import guidedetail from './component/guidedetail.vue';
import secondcomment from './component/secondcomment.vue';
import videocousemain from './component/videocousemain.vue';
export default {
  components: {
    secondcomment,
    guidedetail,
    videocousemain
  },
  data() {
    return {
      sellLable: {
        2: '加密',
        3: '指定学员'
      },
      loading: false,
      courseSearchForm: {
        keyword: '',
        categoryUid: "",
        currentPage: 1,
        pageSize: 15,
        type: 1,
        source: 'VIDEO_CURRICULUM',
        curriculumType: 2,
        total: 0
      },
      dataList: [],
      detailInfo: {}, // 课程详情
      recommendLessonList: [], // 推荐课程
      categoryData: [
        {
          spreedFlag: false,
          name: "测试测试",
          uid: '11132132132312'
        }
      ],
      isFirstPassPwd: false, // 是否是第一次通过加密课程
      detailUid: '', // 当前详情uid
      timer: 'videoTimer',
      activeTag: 'videocousemain',
      Tags: [
        { label: '视频', name: 'videocousemain', component: 'videocousemain' },
        { label: '详情', name: 'guidedetail', component: 'guidedetail' },
        { label: '评论', name: 'secondcomment', component: 'secondcomment' },
      ],
      courseLable: {
        1: '靶机课',
        2: '视频课'
      },
      hasNextPage: false, //是否具有下一页
      knowledgeMsg: {},
      knowledgeCurrList: []
    };
  },
  mounted() {
    this.timer = new Date().getTime()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  watch: {
    '$route.query': {
      handler: function (newVal) {
        if (newVal && newVal.uid && process.client) {
          this.detailUid = newVal.uid
          // 详情信息
          this.initDetailInfo();
          // 推荐课程
          this.initRecommendLesson();
          // 获取课程数据(目录)
          this.getList();

          // 获取课程相关知识点信息
          this.getCareerMsg()
        }
      },
      immediate: true
    }
  },
  computed: {
    pictureUrl({ detailInfo }) {
      if (process.client) {
        if (detailInfo.pictureUrl && process.client) {
          return localStorage?.getItem('picBaseUrl') + detailInfo.pictureUrl
        }
      }
      return '';
    },
    breadcrumbList() {
      return [...this.$store.state.courseBreadcrumb, { label: this.detailInfo?.curriculumName }]
    }
  },
  methods: {
    async clickCategoryItem(data) {
      // console.log(data)
      // 初始化
      this.activeTag = 'videocousemain'
      this.courseSearchForm.currentPage = 1;
      this.detailUid = data.name
      let param = { uid: data.name }

      if (this.$route.query.showBre) param.showBre = true

      this.$router.replace({
        query: param
      })
      // 详情信息
      this.initDetailInfo();
      // 推荐课程
      this.initRecommendLesson();
    },
    //获取课程数据(目录)
    async getList() {
      this.loading = true;
      await this.$request.combatCourse.lessonGetList(this.courseSearchForm).then(res => {
        if (res.data.code == 200) {
          const result = res.data.data
          this.dataList = this.dataList.concat(result.records)
          this.courseSearchForm.total = result.total;
          this.hasNextPage = result.records?.length == result.size && result.total > result.size && result.total > this.dataList.length
          // this.$nextTick(() => {
          //   const ele = document.querySelector('.video-left')
          //   ele && ele.addEventListener('scroll', this.handleScroll)
          // })
        } else {
          this.dataList = []
          this.courseSearchForm.total = 0
        }
      }).catch(err => { })
      this.loading = false;
    },
    selectRecommendLesson(data) {
      let param = { uid: data.info && data.info.uid ? data.info.uid : data.uid }
      if (this.$route.query.showBre) param.showBre = true

      //1  靶机  2 视频
      this.$router.replace({
        path: data.contentType == 1 ? '/combatCourse/detail' : '/combatCourse/videoCourseDetail',
        query: param
      })
    },
    // 立即购买
    imPay() {
      if (!this.$store.getters.isLogin) {
        this.$confirm("您还未登录，请先登录后再购买?", "提示", {
          confirmButtonText: "前往登录",
          cancelButtonText: "再逛逛",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
          })
          .catch(() => {
            // 拒绝前往登录界面
            return;
          });
      } else {
        this.$store.commit('setCourseBreadcrumb', [{ label: '在线靶场' }, { label: '9.9实战课程' }, { label: this.detailInfo.curriculumName }]);
        this.$router.push({
          path: '/certification/pay',
          query: {
            vuid: this.detailInfo.uid
          }
        });
      }
    },
    // 去输入密码
    gotoPwd() {
      this.activeTag = 'videocousemain'
      document.getElementById('rightTag').scrollIntoView()
    },
    async initRecommendLesson() {
      let result = await this.$axios({
        url: this.$api.recommendLesson,
        method: 'get',
        params: {
          uid: this.detailUid
        }
      });
      if (result.data.code === this.$code.SUCCESS) {
        this.recommendLessonList = result.data.data.map(item => {
          return {
            ...item,
            pictureUrl: localStorage.getItem('picBaseUrl') + item.pictureUrl
          }
        });
      }
    },
    // 加载详情信息
    async initDetailInfo() {
      let result = await this.$axios({
        url: this.$api.ninePiecesDetail,
        method: 'get',
        params: {
          uid: this.detailUid
        }
      });
      if (result.data.code === this.$code.SUCCESS) {
        this.detailInfo = result.data.data;
      }
    },
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e && e.target
      if (Math.ceil(scrollHeight) - Math.ceil(scrollTop) <= Math.ceil(clientHeight)) {
        if (this.hasNextPage) {
          this.courseSearchForm.currentPage++;
          this.getList()
        }
      }
    },
    handleClick(v) {
      this.activeTag = v.name
    },

    // 获取课程相关知识点信息
    getCareerMsg() {
      this.knowledgeMsg = {}
      this.$request.combatCourse.getCareerOfCurri({ uid: this.$route.query.uid }).then(res => {
        if (res.data.code == 200) {
          this.knowledgeMsg = res.data.data
          this.knowledgeCurrList = []
          if (this.knowledgeMsg.parentUid) {
            let params = {
              currentPage: 1,
              pageSize: 4,
              exclusionUid: this.$route.query.uid,
              uid: this.knowledgeMsg.parentUid
            }
            this.$request.combatCourse.getContentForCurriculum(params).then(res => {
              if (res.data.code == 200 && res.data.data.records.length > 0) {
                this.knowledgeCurrList = res.data.data.records
              }
            })
          }
        }
      })
    },
    // 前往更多
    toMore() {
      try {
        if (!this.knowledgeMsg.grandpaUid || !this.knowledgeMsg.grandpaName || !this.knowledgeMsg.parentUid) {
          return this.$message.warning('暂无更多推荐课程')
        }
      } catch { return this.$message.warning('暂无更多推荐课程') }

      this.$router.push({
        path: '/certification/knowledgeDetail',
        query: {
          p: this.knowledgeMsg.grandpaUid,
          name: this.knowledgeMsg.grandpaName,
          k: this.knowledgeMsg.parentUid
        }
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.detail-container {
  background: #FFFFFF;



  .wrap-box {
    width: 1180px;
    margin: 0 auto;
    display: flex;
    // margin-top: 20px;
    padding-right: 2px;

    .wrap {
      padding-right: 4px;
      background: #FFFFFF;

      .video-left {
        padding: 16px;
        width: 180px;
        height: 720px;
        overflow-y: auto;

        border-radius: 4px 4px 4px 4px;
        @include customScrollbar(#D4DBE0, transparent, 20px);

        &::-webkit-scrollbar {
          width: 4px !important;
        }

        p {
          margin-left: 16px;
        }

        .select-content {
          .first-lever {
            justify-content: space-between;

            i {
              transform: rotate(0deg);
              transition: all .2s;
            }
          }

          .item {
            white-space: normal;
            word-break: break-all;
            word-wrap: break-word;
            display: flex;
            align-items: center;
            width: 148px;
            padding: 12px;
            height: 68px;
            color: #666666;
            border-radius: 4px 4px 4px 4px;
            font-size: 14px;
            line-height: 22px;

            &:first-child {
              margin-top: 20px;
            }

            &:hover {
              cursor: pointer;
              color: #333333;
              background: #F4F6F7;
            }
          }

          .ready-study {
            color: #BDC7CF !important;
          }

          .active {
            color: #333333;
            background: #F4F6F7;
          }
        }
      }
    }

    .right {
      border-right: 1px solid #E6EAED;
      width: 932px;
      height: 720px;
      background: #FFFFFF;
      // box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.05);
      border-radius: 4px 4px 4px 4px;
      padding: 0px 32px 20px 0;
      display: flex;
      flex-direction: column;


      /deep/ .content-guidance {
        width: 100%;
        height: 680px;
        overflow-y: auto;
        @include customScrollbar(#D4DBE0, transparent, 20px);

        &::-webkit-scrollbar {
          width: 4px !important;
        }

        .el-button {
          width: 120px;
          height: 36px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

    }

  }

  .head {
    border-top: 1px solid #F4F6F7;
    width: 100%;
    background: #FFFFFF;
    // box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.05);
    border-radius: 0px 0px 0px 0px;

    .head-info {
      position: relative;
      min-height: 280px;
      padding-bottom: 20px;
      width: 1180px;
      margin: 0 auto;

      .class-info {
        padding-bottom: 20px;
        border-bottom: 1px solid #E6EAED;
        display: flex;

        .pay-btn {
          position: absolute;
          right: 0;
          top: 96px;
        }

        .pwd-btn {
          position: absolute;
          right: 0;
          top: 96px;
        }

        .cover {
          flex-shrink: 0;
          margin-right: 28px;
          width: 200px;
          height: 150px;
          border-radius: 8px 8px 8px 8px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .right {
          .title {
            max-width: 974px;
            // overflow: hidden;
            // white-space: nowrap;
            // text-overflow: ellipsis;
            // word-break: break-all;
            font-size: 28px;
            font-weight: normal;
            display: flex;
            // align-items: center;
            color: #333333;

            .title-real {
              width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              word-break: break-all;
            }

            .price {
              font-size: 28px;
              font-weight: 500;
              margin: 0 16px;
            }
          }

          .desc {
            word-break: break-all;
            margin: 8px 0 17px 0;
            font-size: 14px;
            font-weight: normal;
            width: 680px;
            line-height: 22px;
            color: #666666;
          }

          .finish-info {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: normal;
            color: #666666;

            .complete-item {
              padding: 5px 16px;
              background: #F5F8FC;
              color: #006EFF;
              border-radius: 4px;
            }

            .complete-count {
              padding: 0 16px 0 16px;
              min-width: 141px;
              height: 32px;
              line-height: 32px;
              background: #F5F8FC;
              border-radius: 4px 4px 4px 4px;
              display: flex;
              align-items: center;
              margin-left: 20px;

              .icon-count {
                width: 16px;
                height: 16px;
                margin-right: 4px;
              }

              .complete-desc {
                margin-right: 12px;
              }

              .count {
                color: #006EFF;
                font-weight: bold;
              }



              &:first-child {
                margin-left: 0;
              }
            }
          }
        }
      }

      .breadcrumb {
        width: 1180px;
        margin: 40px auto 20px auto;

        .el-breadcrumb {
          /deep/ .el-breadcrumb__item {
            .el-breadcrumb__inner {
              font-size: 14px;
              font-weight: normal !important;
              color: #999999 !important;
            }
          }
        }
      }
    }
  }

  .lock-dialog {
    /deep/ .el-dialog {
      height: 500px;
      margin: 0px !important;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
      display: flex;
      flex-direction: column;

      .el-dialog__header {
        padding: 0px 32px;
        height: 76px;
        flex-shrink: 0;
      }

      .el-dialog__body {
        display: flex;
        flex: 1;
        height: 0px;
        padding: 0px 30px;

        .text-name {
          width: 115px;
          text-align: right;
          flex-shrink: 0;
        }
      }

    }

  }
}

.right-title {
  font-size: 18px;
  font-family: Alibaba PuHuiTi 2.0-65 Medium, Alibaba PuHuiTi 20;
  font-weight: 500;
  color: #333333;
  line-height: 28px;
}

.hover {
  width: 120px;
  background: #006EFF;
  height: 90px;
  overflow: hidden;
  border-radius: 4px 4px 4px 4px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.curri-msg {
  width: 148px;
}


.info {
  .title {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  p {
    margin: 12px 0;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
  }

  .desc {
    font-size: 12px;
    font-weight: 400;
    color: #999999;

    &:last-child {
      // margin-left: 4px;
    }
  }
}

.curri-price {
  font-size: 14px;
  font-family: Alibaba PuHuiTi 2.0-65 Medium, Alibaba PuHuiTi 20;
  font-weight: 500;
  color: #FF564D;
  line-height: 22px;
}

.content-btn {
  background: #F4F6F7;
  border-radius: 20px 20px 20px 20px;
  padding: 5px 16px;
  font-size: 14px;
  font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
  font-weight: 400;
  color: #666666;
  line-height: 22px;
}

.active-btn {
  background: #006EFF;
  color: #FFFFFF;
}
</style>