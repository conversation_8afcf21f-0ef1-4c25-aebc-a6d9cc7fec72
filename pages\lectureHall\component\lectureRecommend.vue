<template>
  <div class="mt-50 " :class="type == 2 ? '' : 'bk'">
    <div class="flex ai-center mb-15">
      <div class="font-24 color-black mr-10 f-w-600">为你推荐</div>
      <img src="../../../assets/newVersion/title-2.png" alt="" />
    </div>
    <div v-if="dataList && dataList.length > 0" class="flex flex-wrap">
      <div v-for="(item, index) in dataList" :key="index" class="mt-20 recom-item"
        :class="{ 'mr-20': (index + 1) % 2 != 0 }">
        <img src="../../../assets/newVersion/gold-teacher.png" class="gold-teahcer" />
        <div class="flex ai-center">
          <span class="font-20 color-black f-w-600 mr-8 nowrap tea-name">{{ item.name }}</span>
          <span class="font-12 color-999 ellipsis flex-1">{{ item.intro }}</span>
        </div>

        <div class="mt-8 flex ellipsis">
          <div v-for="(val, i) in  item.topicSet " :key="i" class="point-bg"
            :class="{ 'mr-5': (i + 1) != item.topicSet.length }">
            {{ val }}
          </div>
        </div>

        <div class="mt-12 flex curri-main">
          <img ref="teacherHeader" v-lazy="item.picturePath" class="tea-head mr-12 cursor" @click="toDetail(item)">

          <div class="icon-bg-left" :style="getLeftStyle">
            <i v-if="item.courseList && item.courseList.length > 0" class="el-icon-arrow-left curri-icon"
              @click="toPre(index)"></i>
          </div>
          <div class="icon-bg-right">
            <i v-if="item.courseList && item.courseList.length > 0" class="el-icon-arrow-right curri-icon"
              @click="toNext(index)"></i>
          </div>

          <div :style="curriStyle">
            <div class="flex ai-center mb-12">
              <span class="color-999 font-12">
                <span class="color-main f-w-600"> {{ item.courseTotal }} </span> 门课程
              </span>
              <div class="line"></div>
              <span class="color-999 font-12">
                <span class="color-main f-w-600"> {{ item.students || 0 }} </span> 位学员
              </span>
            </div>
            <div class="curri-list overflow-hidden" ref="curriList">
              <div v-for="( val, i ) in  item.courseList " :key="i" class="curri-item mr-16 cursor"
                @click="replaceCourse(val)">
                <img v-lazy="val.pictureUrl" class="curri-img">

                <div class="mt-6">
                  <div class="font-12 color-black ellipsis-2-2 curri-name">
                    {{ val.name }}
                  </div>
                  <div class="font-12 color-999 mt-4">
                    <span class="mr-10">{{ val.classHour }}课时</span>
                    <span>{{ val.curriculumScore || 0 }}分</span>
                  </div>
                </div>
              </div>

            </div>

            <div v-if="!item.courseList || item.courseList.length < 1" class="color-666 fa-55 flex-center h-100">
              暂无课程
            </div>

          </div>
        </div>
      </div>
    </div>

    <el-empty v-else :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  data () {
    return {
      dataList: [],
      curriStyle: '',
      teacherHeaderWidth: 0
    };
  },
  computed: {
    getLeftStyle () {
      return 'left:' + this.teacherHeaderWidth + 'px'
    }
  },
  async mounted () {
    await this.getData()
    window.addEventListener('resize', this.handleResize)
    setTimeout(() => {
      this.handleResize()
    }, 300)

  },
  beforeDestroy () {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize () {
      const teacherHeader = this.$refs.teacherHeader
      if (teacherHeader && teacherHeader[0] && teacherHeader[0].offsetWidth) {
        // console.log(teacherHeader[0].offsetWidth)
        this.teacherHeaderWidth = teacherHeader[0].offsetWidth
        this.curriStyle = 'width: calc(100% - ' + (teacherHeader[0].offsetWidth + 12) + 'px)'
      } else {
        setTimeout(() => {
          this.handleResize()
        }, 300)
      }
    },
    // 课程列表
    getData () {
      this.$request.lectureHall.goldLecturer({
        "sortType": 0,
        "currentPage": 1,
        "pageSize": 4
      }).then(res => {
        if (res.data.code == 200) {
          res.data.data.records.map(item => {
            if (!item.picturePath) {
              item.picturePath = require('/static/images/default-avatar.png')
            }
          })
          this.dataList = res.data.data.records
        }
      })
    },


    // 向左滚动
    toPre (index) {
      const element = this.$refs.curriList[index]
      element.scrollLeft -= 200
    },
    // 向右滚动
    toNext (index) {
      const element = this.$refs.curriList[index]
      element.scrollLeft += 200
    },

    replaceCourse ({ uid }) {
      if (this.$route.query.uid == uid) return
      // this.$router.push({
      //   path: '/huYuanSchool/guide',
      //   query: { uid }
      // });
    },

    toDetail ({ uid }) {
      this.$router.push({
        path: '/laboratoryNew/personal',
        query: { speakerUid: uid }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.recom-item {
  width: calc((100% - 20px) / 2);
  height: 272px;
  border-radius: 4px 4px 4px 4px;
  padding: 27px;
  padding-top: 32px;
  overflow: hidden;
  background: url('../../../assets/newVersion/recom-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;

  .gold-teahcer {
    position: absolute;
    top: 8px;
    right: 10px;
  }
}

.curri-list {
  overflow: auto;
  scroll-behavior: smooth;
  white-space: nowrap;
}

.point-bg {
  background: linear-gradient(134deg, #FFFFFF 0%, #E5F0FF 100%);
  box-shadow: inset 0px 1px 2px 0px rgba(255, 255, 255, 0.2), 0px 1px 3px 0px rgba(0, 110, 255, 0.15);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #FFFFFF;
  font-size: 12px;
  font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
  font-weight: 400;
  color: #666666;
  padding: 3px 8px;
}

.tea-head {
  height: 100%;
  object-fit: cover;
  aspect-ratio: 0.7517;
}

.line {
  width: 0px;
  height: 12px;
  margin: 0 12px;
  border: 1px solid #E6EAED;
}


.curri-list {
  overflow: auto;
  scroll-behavior: smooth;
  white-space: nowrap;
}

.nowrap {
  white-space: nowrap;
}

.curri-item {
  display: inline-block;
  width: 106px;

  .curri-img {
    width: 106px;
    height: 60px;
    border-radius: 0px 0px 0px 0px;
    object-fit: cover;
  }

  .curri-name {
    white-space: wrap;
  }
}

.mt-4 {
  margin-top: 4px;
}

.curri-main {
  height: 149px;
  overflow: hidden;
  position: relative;

  .curri-icon:hover {
    color: $colorMain;
    cursor: pointer
  }

}

.icon-bg-left {
  width: 30px;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
}

.icon-bg-right {
  background: linear-gradient(90deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  width: 30px;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  right: -10px;
  justify-content: center;
}

.ellipsis-2-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 32px;
}
</style>
