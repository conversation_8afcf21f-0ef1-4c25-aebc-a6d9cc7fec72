<!-- 我购买的课程 -->
<template>
  <div>
    <!-- <p class="title">我的课程</p> -->
    <!-- 搜索框 -->
    <div class="search-box flex jc-between">
      <el-select style="float: left;" @change="search" class="select-input" v-model="curriculumForm.type"
        placeholder="请选择">
        <el-option label="全部" :value="''"></el-option>
        <el-option label="靶机课" :value="1"></el-option>
        <el-option label="视频课" :value="2"></el-option>
      </el-select>
      <hySearchInput placeholder="搜索课程名称" :inputVal.sync="curriculumForm.keyword" @realSearch="search" width="280px"
        height="40px"></hySearchInput>
      <!-- <el-input  class="search-input" placeholder="搜索课程名称" v-model="curriculumForm.keyword">
        <i class="el-icon-search" slot="suffix" @click="search">
        </i>
      </el-input> -->
    </div>
    <!-- 空 -->
    <template v-if="!courseData.length">
      <div class="flex-col ai-center my-50">
        <img v-lazy="require('~/assets/empty/usercenter-cursor-empty.png')" alt="" style="width: 260px;height: 160px;">
        <div class="color-666 font-14 mt-20">
          没有任何课程
        </div>
      </div>
    </template>
    <!-- 内容 -->
    <template v-else>
      <div class="course-box">
        <div class="item cursor" @click="gotoDetail(item)" v-for="item in courseData" :key="item.uid">
          <div class="cover">
            <img v-if="item.pictureUrl" v-lazy="item.pictureUrl" alt="" srcset="">
            <img v-else v-lazy="require('/static/images/emptyImg.jpg')" alt="" srcset="">
          </div>
          <p class="title">{{ item.curriculumName }}</p>
          <div class="study-num">
            <span>学习人数 {{ item.studyNum || 0 }}</span>
            <div class="hr"></div>
            <span>贡献者</span>
            <span style="margin-left: 4px;">{{ item.userName }}</span>
          </div>
          <div class="tag">
            {{ item.curriculumType == 1 ? '靶机课' : '视频课' }}
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <span class="pagination-desc">共 {{ curriculumForm.total }} 条</span>
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="curriculumForm.page" :page-size="curriculumForm.pageSize" layout="prev, pager, next"
          :total="curriculumForm.total">
        </el-pagination>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'HuanyuCollegeNuxtWebMyPayCourse',

  data() {
    return {
      curriculumForm: {
        total: 0,
        page: 1,
        pageSize: 6,
        keyword: '',
        type: ''
      },
      courseData: [],
    };
  },

  mounted() {
    this.initDataList();
  },

  methods: {
    gotoDetail(item) {
      this.$store.commit('setCourseBreadcrumb', [{ label: '个人中心' }, { label: '我的课程' }]);
      if (item.curriculumType == 1) {
        this.$router.push({ path: '/combatCourse/detail', query: { uid: item.uid } })
      } else {
        this.$router.push({ path: '/combatCourse/videoCourseDetail', query: { uid: item.uid } })
      }
    },
    imgUrl(fixUrl) {
      if (localStorage) {
        return localStorage.getItem('picBaseUrl') + fixUrl
      }
      return '';
    },
    handleSizeChange(val) {
      this.curriculumForm.pageSize = val;
      this.initDataList();
    },
    handleCurrentChange(val) {
      this.curriculumForm.page = val;
      this.initDataList();
    },
    async initDataList() {
      let params = {
        curriculumName: this.curriculumForm.keyword,
        curriculumType: this.curriculumForm.type,
        currentPage: this.curriculumForm.page,
        pageSize: this.curriculumForm.pageSize
      }
      let result = await this.$request.myCourse.courseList(params);
      if (result.data.code == this.$code.SUCCESS) {
        this.courseData = result.data.data.records;
        this.curriculumForm.total = result.data.data.total;
      }
      // console.log("initDataList initDataList",result)
    },
    search() {
      this.curriculumForm.page = 1;
      this.initDataList();
    },
    // 前往新增课程
    gotoPage(path) {
      if (!path) return
      this.$router.push({
        path
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.course-box {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  .item {
    width: 280px;
    min-height: 318px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    border: 1px solid #E6EAED;
    padding: 20px;
    margin-top: 20px;

    .title {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap
    }

    .tag {
      color: #006EFF;
      width: 52px;
      height: 20px;
      background: #F4F6F7;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px 2px 2px 2px;
      font-size: 12px;
    }

    .study-num {
      font-size: 12px;
      color: #999999;
      margin: 12px 0;
      display: flex;
      align-items: center;

      .hr {
        margin: 0 8px;
        width: 1px;
        height: 12px;
        background: #E6EAED;
        border-radius: 1px 1px 1px 1px;
      }
    }

    p {
      font-weight: 500;
      color: #333333;
      margin-top: 20px;
    }

    .cover {
      width: 240px;
      height: 180px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  &::after {
    content: '';
    width: 280px;
    display: inline-block;
  }
}

.pagination {
  margin-top: 41px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .pagination-desc {
    font-size: 14px;
    color: #999999;
  }
}

.table-box {}

.search-box {
  height: 36px;

  .select-input {
    width: 132px;
    height: 36px;
  }

  .search-input {
    width: 280px;
    height: 36px;

    /deep/ .el-input__suffix {
      top: 11px;
      right: 10px;
    }
  }
}

.title {
  color: #333333;
  margin-bottom: 20px;
  font-size: 18px;
}
</style>