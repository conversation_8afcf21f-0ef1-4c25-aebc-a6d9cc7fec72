<template>
  <div class="my-train">
    <!-- 头部内容 -->
    <div class="top-content w-1440">
      <div class="content py-40">
        <div class="top-detail flex">
          <div class="detail-image">
            <img
              v-if="certificates.mainPictureUrl"
              :src="certificates.mainPictureUrl"
              style="width: 100%; height: 100%"
              alt=""
              srcset=""
            />
            <img v-else src="@/static/images/emptyImg.jpg" alt="" srcset="" />
          </div>
          <div class="ml-15">
            <div class="font-20 color-333 fa-65 f-w-500 flex">
              {{ certificates.fullName }}
            </div>
            <div class="font-14 fa-55 mt-4 color-333 flex">
              证书简称：
              <span class="color-666">{{ certificates.shortName }}</span>
            </div>
            <div class="font-14 fa-55 mt-4 color-333 flex">
              颁发机构：
              <span class="color-666">{{ certificates.labelName }}</span>
            </div>
            <!--<div class="font-14 fa-55 mt-4 flex">
               <div class="font-14 color-666">
                <span class="color-333">证书简介：</span>
                {{ certificates.recommend }}
              </div> 
            </div>-->
            <div class="color-333 font-14 fa-55 color-333" style="position: relative;">证书简介：
              <div
                v-if="
                  introduce &&
                  introduce.clientHeight < introduce.scrollHeight &&
                  introduce.clientHeight != introduce.scrollHeight
                "
                id="more"
                class="more"
              >
                <div id="introduce_ellipsis" class="introduce_ellipsis">
                  <!-- ... -->
                  <span class="fold" @click="showUp()">详细介绍></span>
                </div>
              </div>
            </div>        
            <div id="introduce" class="introduce font-14 fa-55 color-333">
              <div v-html="certificates.recommend"></div>
              <!-- <div
                v-if="
                  introduce &&
                  introduce.clientHeight < introduce.scrollHeight &&
                  introduce.clientHeight != introduce.scrollHeight
                "
                id="more"
                class="more"
              >
                <div id="introduce_ellipsis" class="introduce_ellipsis">
                  ...
                  <span class="fold" @click="showUp()">详细介绍></span>
                </div>
              </div> -->
            </div>
            <template v-if="signVisible">
              <hyButton
                class="ml-10"
                :extendStyle="{
                  backgroundColor: '#006EFF',
                  border: '1px solid #006EFF',
                  color: '#fff !important',
                  fontSize: '12px !important',
                  marginTop :'10px'
                }"
                size="small"
                width="80px"
                height="30px"
                :plain="true"
                v-if="checkStatus==0||checkStatus==2"
                @click="goSign()"
                >考试报名
              </hyButton>
              <hyButton
                class="ml-10"
                :extendStyle="{
                  backgroundColor: '#006EFF',
                  border: '1px solid #006EFF',
                  color: '#fff !important',
                  fontSize: '12px !important',
                  marginTop :'10px'
                }"
                size="small"
                width="80px"
                height="30px"
                :plain="true"
                v-if="checkStatus==1"
                >审核中
              </hyButton>
              <hyButton
                class="ml-10"
                :extendStyle="{
                  backgroundColor: '#006EFF',
                  border: '1px solid #006EFF',
                  color: '#fff !important',
                  fontSize: '12px !important',
                  marginTop :'10px'
                }"
                size="small"
                width="80px"
                height="30px"
                :plain="true"
                @click="goSign()"
                v-if="checkStatus==3"
                >重新报名
              </hyButton>
            </template>
          </div>
        </div>
      </div>
    </div>
    
    <div class="bottom-content flex">
      <div class="bottom-left">
        <div class="choice-div">
          <div
            v-for="(item, index) in items"
            :key="index"
            class="my-create-cursor-item"
            :class="{ 'active-item': activeIndex === index }"
            @click="handleClick(item.name, index)"
          >
            <img v-lazy="item.icon" alt="" style="width: 20px; height: 20px" />
            <span class="ml-5">{{ item.label }}</span>
          </div>

          <div class="bg-div" :style="{ left: bgLeft }"></div>
        </div>
        <div>
          <div v-if="activeName == 'introduce'">
            <div class="flex jc-center mt-20 color-333 fa-65 font-18 f-w-700">
              {{ certificates.fullName }}
            </div>
            <div
              class="flex jc-center mt-20 font-12 fa-55 f-w-400"
              style="color: #86909c"
            >
              颁发证书机构：{{ certificates.labelName }}
            </div>
            <div
              class="recommend-item mb-30 mt-20"
              v-for="(contentItem, index) in getDescription(
                certificates.description,
                'out'
              )"
              :key="index"
            >
              <div class="recommend-title mb-20">
                {{ contentItem.title }}
              </div>
              <div v-html="contentItem.content" class="content"></div>
            </div>
          </div>
          <certificateOpenClassPlan v-if="activeName == 'plan'" :name="certificates.shortName" :learnStatus="certificates.learnStatus" />
          <courseDirectory v-if="activeName == 'directory'" :trainClassDetail="certificates.learnStatus?certificates.trainModuleContentList:[]" :registerStatus='certificates.registerStatus' />
          <div class="table" v-if="activeName == 'practice'">
            <el-table
              :header-cell-style="{ color: '#666', fontWeight: '400' }"
              :data="certificates.learnStatus? practiceList:[]"
              style="width: 100%"
              :cell-style="{ color: '#666' }"
            >
              <el-table-column
                prop="pname"
                label="练习名称"
                align="left"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <div>
                    <span class="color-black font-14 font-bold">{{
                      scope.row.pname
                    }}</span>
                  </div>
                </template>
              </el-table-column>
              <!--<el-table-column
                prop="difLevel"
                label="练习难度"
                align="left"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <div>
                    <el-rate
                      v-model="scope.row.difLevel"
                      disabled
                      show-score
                      :colors="['#F7BA2A', '#F7BA2A', '#9cc6fb']"
                      allow-half
                      text-color="#999999"
                      score-template="{value}"
                    >
                    </el-rate>
                  </div>
                </template>
              </el-table-column>-->
              <el-table-column prop="address" label="操作">
                <template slot-scope="scope">
                  <hyButton
                    class="ml-10"
                    :extendStyle="{
                      backgroundColor: '#006EFF',
                      border: '1px solid #006EFF',
                      color: '#fff !important',
                      fontSize: '12px !important',
                    }"
                    size="small"
                    width="80px"
                    height="30px"
                    :plain="true"
                    @click="gotoSignUp(scope.row)"
                    >进入练习
                  </hyButton>
                  <hyButton
                    v-if="scope.row.wrongTitleCount != 0 && $store.getters.isLogin"
                    class="ml-10"
                    :extendStyle="{
                      backgroundColor: '#006EFF',
                      border: '1px solid #006EFF',
                      color: '#fff !important',
                      fontSize: '12px !important',
                    }"
                    size="small"
                    width="80px"
                    height="30px"
                    :plain="true"
                    @click="gotoWrong(scope.row)"
                    >错题集
                  </hyButton>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination" v-if="activeName == 'practice'&& certificates.learnStatus">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              background
              :current-page="page"
              :page-sizes="[5, 10, 15, 20, 30]"
              :page-size="pageSize"
              layout="prev, pager, next"
              :total="total"
            >
            </el-pagination>
          </div>
          <div
            v-if="activeName == 'information'"
            class="flex mt-20"
            style="flex-direction: column; width: 100%"
          >
            <div class="flex ai-cenetr jc-between">
              <hyButton
                :extendStyle="{
                  backgroundColor: '#EBF0F7',
                  border: '1px solid #EBF0F7',
                  color: '#006EFF !important',
                  letterSpacing: '1px',
                }"
                size="small"
                width="99px"
                height="32px"
                :plain="true"
                @click="downloadProfile"
                ><i class="el-icon-download iconfont font-14"></i
                ><span>下载</span></hyButton
              >
            </div>
            <!-- 表格数据 -->
            <div class="mt-10" style="max-height:700px;">
              <el-table
                height="100%"
                style="max-height: 700px;"
                :data="certificates.learnStatus?certificates.profileList:[]"
                @selection-change="handleSelectionChange"
                :header-cell-style="{
                  background: 'rgba(2, 86, 255,0.08)',
                  color: '#3D3D3D',
                }"
              >
                <el-table-column type="selection" width="45" :selectable="selectable"/>
                <el-table-column show-overflow-tooltip>
                  <template slot="header">
                    <span class="font-14 color-666">文件名称 </span>
                  </template>
                  <template slot-scope="scope">
                    <span class="color-black font-14 cursor hover-color">
                      {{ scope.row.fileName }}
                    </span>
                  </template>
                </el-table-column>

                <el-table-column show-overflow-tooltip align="center">
                  <template slot="header">
                    <span class="font-14 color-666">文件大小 </span>
                  </template>
                  <template slot-scope="scope">
                    <span v-if="scope.row.fileSize">
                      {{ scope.row.fileSize }}
                    </span>
                    <span v-else>0KB</span>
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip align="center">
                  <template slot="header">
                    <span class="font-14 color-666">文件类型</span>
                  </template>
                  <template slot-scope="scope">
                    <span
                      class="color-black font-14"
                      v-html="formattrtSuffix(scope.row)"
                    >
                    </span>
                  </template>
                </el-table-column>
                <template slot="empty">
                  <div class="flex-col jc-center ai-center">
                    <img
                      v-lazy="
                        require('@/assets/images/userCenter/curriculum/data-empty.png')
                      "
                      alt=""
                      style="width: 120px; height: 75px"
                    />
                    <span class="color-666 font-14">暂无资料</span>
                  </div>
                </template>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="bottom-right flex flex-1 flex-col ml-20 p-20">
        <div class="color-black">推荐课程</div>
        <template v-if="RecommendList.length > 0">
          <div
            class="right-item py-20 flex"
            v-for="item in RecommendList"
            @click="gotoVideoDetail(item)"
            :key="item.uid"
          >
            <div class="right-image">
              <img
                :src="item.coverUrl"
                style="width: 100%; height: 100%; border-radius: 4px"
                alt=""
              />
            </div>
            <div class="flex flex-1 flex-col ml-20" style="width: 0">
              <div class="color-black font-14 ellipsis" :title="item.title">
                {{ item.title }}
              </div>
              <div class="color-999 font-12 my-10">
                {{ String(item.createTime) }}
              </div>
              <div style="color: #ff564d" class="font-14 f-w-700">
                {{ item.sellingPrice ? "￥" + item.sellingPrice : "免费" }}
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="empty">
            <el-empty description="暂无内容"></el-empty>
          </div>
        </template>
      </div> -->
    </div>
    <el-dialog title="考试报名" :visible.sync="dialogFormVisible" width="500px" center>
      <el-form :model="form"  label-width="100px" ref="form" :rules="rules">
        <el-form-item label="认证类型">
          <el-input v-model="form.fullName" disabled></el-input>
        </el-form-item>
        <el-form-item label="报名人" prop="applicantName">
          <el-input v-model="form.applicantName"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="applicantPhone">
          <el-input v-model="form.applicantPhone"></el-input>
        </el-form-item>
        <el-form-item label="表格模板下载">
          <span @click="downloadFile2" style="cursor: pointer; color:#0080ff;">{{ form.examTemplateName }}</span>
        </el-form-item>
        <!-- <el-form-item label="上传考试申请表">
          <el-upload
            :action="actionPath"
            :on-success="templateUploadSuccess"
            :before-upload="beforeUploadtemplate"
            :headers="{
              Authorization: $GetToken(),
            }"
            :on-error="templateUploadError"
            :data="otherData"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            class=""
            multiple
          >
            <el-button size="small" v-if="oprationFlag != 2" type="primary"
              >点击上传</el-button
            >
            <div slot="tip" v-if="oprationFlag != 2" class="el-upload__tip">
              只能上传doc/docx/xls/xlsx文件
            </div>
          </el-upload>
        </el-form-item> -->
        <div>
          <el-form-item
            label="上传考试申请表"
            prop="checkTableUid"
            class="line-height-item"
          >
            <template>
              <div style="height: 40px" class="flex ai-center">
                <input
                  ref="my-upload-person"
                  type="file"
                  style="display: none"
                  @change="presonFileChange"
                />
                <div
                  class="file-div cursor flex ai-center px-10"
                  @click="uploadClick('person')"
                >
                  <img
                    src="~/assets/images/certification/myTraining/upload-icon.png"
                    alt=""
                    style="width: 12px; height: 12px"
                  /><span class="ml-5 font-12 color-main">上传文件</span>
                </div>
              </div>
              <span class="font-12 color-999"
                >支持扩展名：.doc .docx .xls .xlsx</span
              >
              <div
                class="mt-5 color-999 font-12 flex ai-center file-status"
                v-if="form.checkTableName"
              >
                <img
                  src="~/assets/images/certification/myTraining/upload-file-icon.png"
                  style="width: 16px; height: 16px"
                  alt=""
                />
                <span class="mx-10">{{ form.checkTableName }}</span>
                <i
                  class="el-icon-circle-check font-16 success-status"
                  style="color: #5ce571; margin-top: 2px"
                ></i>
                <i
                  class="el-icon-circle-close font-16 color-999 cancel-status cursor"
                  style="margin-top: 2px"
                  @click="cancelTableUid('person')"
                ></i>
              </div>
            </template>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="详细介绍"
        :visible.sync="dialogVisibleDetails"
        width="50%"
        center
        custom-class="content_dialog"
        top="20vh"
        :before-close="handleClose"
      >
        <div v-html="certificates.recommend"></div>
      </el-dialog>
  </div>
</template>

<script>
import certificateOpenClassPlan from "./component/certificateOpenClassPlan.vue";
import courseDirectory from "./component/courseDirectory.vue";
import dayjs from 'dayjs'
export default {
  provide() {
    return {
      myTrainingThis: this,
    };
  },
  components: {
    certificateOpenClassPlan,
    courseDirectory
  },
  data() {
    return {
      dialogVisibleDetails: false,
      introduce:'',
      more:'',
      signVisible:false,
      activeName: "introduce",
      trainClassDetail: {},
      RecommendList: [],
      isSignUpStatus: "",
      certificates: {},
      page: 1,
      bgLeft: ".5%",
      pageSize: 10,
      activeIndex: 0,
      total: 0,
      selectList: [],
      practiceList: [],
      checkStatus:'',
      form:{
        certificateUid:'',
        applicantName:'',
        applicantPhone:'',
        checkTableUid:'',
        checkTableUrl:'',
        checkTableName:''
      },
      rules: {
        // 表单规则
        applicantName: [
          {
            required: true,
            message: "请输入报名人",
            trigger: ["blur", "change"],
          },
        ],
        applicantPhone: [
          {
            required: true,
            message: "请输入报名人手机号",
            trigger: ["blur", "change"],
          },
        ],
        checkTableUid: [
          {
            required: true,
            message: "请上传考试申请表",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogFormVisible:false,
      items: [
        {
          label: "证书介绍",
          name: "introduce",
          icon: require("@/assets/images/certification/introduce.png"),
        },
        {
          label: "开班计划",
          name: "plan",
          icon: require("@/assets/images/certification/plan.png"),
        },
        {
          label: "练习",
          name: "practice",
          icon: require("@/assets/images/certification/practice.png"),
        },
        {
          label: "视频课程",
          name: "directory",
          icon: require("@/assets/images/certification/view.png"),
        },
        {
          label: "下载资料",
          name: "information",
          icon: require("@/assets/images/certification/information.png"),
        },
      ],
    };
  },
  async mounted() {
    this.queryTrainCertificate();
    this.getPracticePage();
    this.getRecommendCursor();
    if (this.$route.query.activeName) {
      this.handleClick(this.$route.query.activeName,1)
    }
    this.$nextTick(() => {
      this.introduce = document.getElementById("introduce");
      this.more = document.getElementById("more");
    });
  },
  computed: {},
  methods: {
    showUp() {
      this.dialogVisibleDetails = true;
    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          let query = {...this.form}
          delete query.examTemplateName
          delete query.examTemplateUrl
          delete query.fullName
          this.$axios({
            url: this.$api.examSignUp,
            method: "post",
            data:query,
          }).then((res) => {
            if (res.data.code == this.$code.SUCCESS) {
              this.dialogFormVisible = false
              this.$message.success('报名成功');
              this.getApplication()
            }
          });
        }
      })
    },
    uploadClick(type) {
      this.uploadType = type;
      this.$refs[`my-upload-${type}`].click();
    },
    cancelTableUid(type, index = 0) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.form.checkTableUid = "";
          this.form.checkTableName = "";
          this.$refs[`my-upload-${type}`].value = "";
        })
        .catch(() => {
          return;
        });
    },
    // 单人模板上传
    presonFileChange(e) {
      if (!e || !e.target.files[0]) return;
      const file = e.target.files[0];
      const suffix = String(file.name).substring(
        String(file.name).lastIndexOf(".") + 1
      );
      if (["doc", "docx", "xls", "xlsx"].includes(suffix)) {
        this.fileUploadFun([file], "person");
      } else {
        this.$message.warning("上传文件格式错误，支持doc,docx,xls,xlsx格式！");
        this.$refs[`my-upload-${this.uploadType}`].value = "";
      }
    },
     // 上传函数
    fileUploadFun(files, type) {
      this.loading = true;
      let formData = new FormData();
      for (let i = 0; i < files.length; i++) {
        formData.append("filedatas", files[i]);
      }
      formData.append("userUid", "wehsbanmsbdwehqwie");
      formData.append("source", "picture");
      formData.append("sortName", "web");
      formData.append("projectName", "blog");
      this.$request.file
        .webPicture(formData)
        .then((res) => {
          if (res.data.code == this.$code.SUCCESS) {
            const data = res.data.data[0];
            this.form.checkTableName = files[0].name;
            this.form.checkTableUid = data.uid;
            this.form.checkTableUrl = data.picUrl;
            // this.$set(this.form,'checkTableName',files[0].name)
            // this.$set(this.form,'checkTableUid',data.uid)
            // this.$set(this.form,'checkTableUrl',data.picUrl)
            // 重置表单验证
            this.resetFormVali();
            this.loading = false;
            console.log(this.form,'as1dasd1a31sdas1d');
          }
        })
        .finally(() => {
          this.loading = false;
          this.$refs[`my-upload-${this.uploadType}`].value = "";
        });
    },
    async resetFormVali() {
      await this.$nextTick();
      this.$refs.form.validateField([
        "checkTableUid",
        "excelUid",
        "moreCheckTableUid",
      ]);
    },
    downloadFile2(){
      this.$downladResetFileName(this.form.examTemplateUrl, this.form.examTemplateName);
    },
    goSign(){
      console.log(this.certificates.registerStatus);
      if (!this.certificates.registerStatus) {
        this.$message.warning('您还未报名培训班级');
        return
      }
      if (this.certificates.examType==0) {
        this.dialogFormVisible = true
        this.getApplication()
      }else{
        window.open(this.certificates.signUpUrl)
      }
    },
    getApplication(){
      this.$axios({
        url: this.$api.getInfoByCertificateUid + '?certificateUid=' + this.certificates.uid,
        method: "get",
      }).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
          console.log(res,'rerererererer');
          this.checkStatus = res.data.data?res.data.data.checkStatus:''
          this.form = {
            certificateUid: this.certificates.uid,
            fullName:this.certificates.fullName,
            applicantName:res.data.data?res.data.data.applicantName:'' ,
            applicantPhone:res.data.data?res.data.data.applicantPhone:'' ,
            checkTableUid:res.data.data?res.data.data.checkTableUid:'' ,
            checkTableUrl:res.data.data?res.data.data.checkTableUrl:'' ,
            checkTableName:res.data.data?res.data.data.checkTableName:'' ,
            examTemplateName: this.certificates.examTemplateName,
            examTemplateUrl: this.certificates.examTemplateUrl,
            uid:res.data.data?res.data.data.uid:''
          }
        }
      });
    },
    selectChange(tab, event) {
      console.log(tab, event);
    },
    selectable() {
      if (this.certificates.registerStatus) {
        return true
      } else {
        return false
      }
    },
    handleClick(name, index) {
      this.activeIndex = index;
      this.activeName = name;
      let baseLet = index * 20;
      this.bgLeft = (baseLet = index ? baseLet - 0.5 : baseLet + 0.5) + "%";
    },
    // 格式化文件类型
    formattrtSuffix(row) {
      return this.getHtml(row.suffix);
    },
    downloadProfile() {
      let profileUid = [];
      this.selectList.forEach((item) => {
        profileUid.push(item.uid);
      });
      this.$request.career.downloadProfile(profileUid).then((result) => {
        this.downloadFile(result.data.data);
      });
    },
    gotoVideoDetail(item) {
      // 免费
      if (!item.sellingPrice) {
        this.$router.push({
          path: "/certification/live",
          query: {
            chapterUid: item.uid,
            ht: 1,
            se: 1,
          },
        });
      }
    },
    // 获取推荐课程
    getRecommendCursor() {
      const data = { limit: 6 };
      this.$axios({
        url: this.$api.getRecommendClass,
        method: "post",
        data,
      }).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
          this.RecommendList = res.data.data;
        }
      });
    },
    // 下载聊天文件
    downloadFile(arr) {
      // 防抖
      this.$debounce(() => {
        // 下载客服文件
        arr.forEach((item) => {
          //下载图片地址和图片名
          // 解决跨域 Canvas 污染问题
          if (["png", "jpg", "gif", "jpeg"].includes(item.suffix)) {
            let image = new Image();
            image.src = item.url;
            // 解决跨域 Canvas 污染问题
            image.setAttribute("crossOrigin", "Anonymous"); // 支持跨域
            image.onload = function () {
              var canvas = document.createElement("canvas");
              canvas.width = image.width;
              canvas.height = image.height;
              var context = canvas.getContext("2d");
              context.drawImage(image, 0, 0, image.width, image.height);
              var url = canvas.toDataURL("image/" + item.suffix); //得到图片的base64编码数据
              var a = document.createElement("a"); // 生成一个a元素
              var event = new MouseEvent("click"); // 创建一个单击事件
              a.download = item.fileName; // 设置图片名称
              a.href = url; // 将生成的URL设置为a.href属性
              a.dispatchEvent(event); // 触发a的单击事件
            };
          }
          if (item.suffix == "mp4") {
            fetch(item.url)
              .then((res) => res.blob())
              .then((blob) => {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.style.display = "none";
                const url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = item.fileName;
                a.click();
                document.body.removeChild(a);
              });
          }
          if (
            ["doc", "docx", "pdf", "ppt", "xls", "xlsx"].includes(item.suffix)
          ) {
            fetch(item.url)
              .then((res) => res.blob())
              .then((blob) => {
                const a = document.createElement("a");
                document.body.appendChild(a);
                a.style.display = "none";
                const url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = item.fileName;
                a.click();
                document.body.removeChild(a);
              });
          }
        });
      })();
    },
    getHtml(suffix) {
      const imgTypes = ["png", "jpg", "gif", "jpeg"];
      const videoTypes = ["mp4"];
      const wordTypes = ["doc", "docx", "pdf", "ppt", "xls", "xlsx"];
      const imgIcon = require("@/assets/images/chat/img.png");
      const wordIcon = require("@/assets/images/chat/word.png");
      const videoIcon = require("@/assets/images/chat/video.png");
      const pdfIcon = require("@/assets/images/chat/pdf.png");
      const undefinedIcon = require("@/assets/images/chat/file-icon.png");
      let fileType = "";
      if (imgTypes.includes(suffix)) fileType = imgIcon;

      if (videoTypes.includes(suffix)) fileType = videoIcon;

      if (suffix == "pdf") fileType = pdfIcon;

      if (wordTypes.includes(suffix)) fileType = wordIcon;

      return `<div style="display: flex;align-items: center;justify-content: center;">
      <img src=${fileType} style="width: 16px;height: 16px;"/><span style="color:#999;font-size:14px;margin-left:4px;">${suffix}</span>
      </div>`;
    },
    handleSelectionChange(val) {
      this.selectList = val.map((item) => item);
    },
    gotoSignUp(item) {
      // 未登录
      if (!this.$store.getters.isLogin) {
        this.$confirm("您还未登录，请先登录后再报名?", "提示", {
          confirmButtonText: "前往登录",
          cancelButtonText: "再逛逛",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
          })
          .catch(() => {
            // 拒绝前往登录界面
            return;
          });
      } else {
        if (this.certificates.registerStatus) {
          this.$store.commit("setCourseBreadcrumb", [{ label: "首页" }]);
          this.$router.push({
            path: "/certification/certificateExam",
            query: {
              uid: item.uid,
              className: this.certificates.shortName,
              classUid: this.$route.query.uid,
            },
          });
        } else {
          this.$message.warning('报名后即可查看相关练习');
        }
      }
    },
    gotoWrong(item) {
      // 未登录
      if (!this.$store.getters.isLogin) {
        this.$confirm("您还未登录，请先登录后再报名?", "提示", {
          confirmButtonText: "前往登录",
          cancelButtonText: "再逛逛",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
          })
          .catch(() => {
            // 拒绝前往登录界面
            return;
          });
      } else {
        if (this.certificates.registerStatus) {
          this.$store.commit("setCourseBreadcrumb", [{ label: "首页" }]);
          this.$router.push({
            path: "/certification/certificateWrong",
            query: {
              uid: item.uid,
              className: this.certificates.fullName,
              classUid: this.$route.query.uid,
            },
          });
        } else {
          this.$message.warning('报名后即可查看相关错题集');
        }
      }
    },
    // 解构详情
    getDescription(data, type) {
      if (!data) return;
      let arr = [];
      arr = JSON.parse(data);
      if (Array.isArray(arr) && arr.length) {
        arr.forEach((item, index) => {
          item.isShow = false;
          item.id = `${type}-${index}`;
        });
      }
      return arr;
    },
    // 证书信息
    queryTrainCertificate() {
      this.$axios({
        method: "get",
        url: this.$api.getTrainCertificateByUid,
        params: { uid: this.$route.query.uid },
      }).then((res) => {
        if (!res.data.data) return;
        this.certificates = res.data.data;
        console.log(this.certificates,'lakhdfasudhansd');
        if (dayjs().isAfter(dayjs(this.certificates.examSignStart))&&dayjs().isBefore(dayjs(this.certificates.examSignEnd))) {
          this.signVisible = true
        }

        this.$nextTick(()=>{
          this.getApplication() 
        })
      });
    },
    handleCurrentChange(val) {
      this.page = val;
      this.getPracticePage();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getPracticePage();
    },
    getPracticePage() {
      let params = {
        currentPage: this.page,
        pageSize: this.pageSize,
        certificateUid: this.$route.query.uid,
      };
      this.$request.career.getPracticePage(params).then((result) => {
        this.total = result.data.data.total;
        this.practiceList = result.data.data.records;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/.el-tabs__active-bar {
  height: 1px !important;
}

/deep/ .el-tabs__nav-wrap::after {
  height: 1px !important;
  background: #e6eaed;
}

/deep/ .el-tabs__header {
  margin: 0px !important;
}

/deep/.el-tabs__item.is-active {
  color: $colorMain;
  font-size: $fontC;
}

/deep/ .el-tabs__item {
  font-size: $fontC;
  color: $color999;
}

/deep/ .el-tabs__item:hover {
  color: $colorMain;
}


.introduce {
  // height: 76px;
  overflow: auto;

  // 滚动条整体样式
  &::-webkit-scrollbar {
    width: 4px !important;
    display: none !important;
    /* 纵向滚动条 宽度 */
    border-radius: 5px;
    /* 整体 圆角 */
  }

  //轨道部分
  &::-webkit-scrollbar-track {
    background: #fff !important;
  }

  // 滑块部分
  &::-webkit-scrollbar-thumb {
    background: #d4dbe0;
    min-height: 167px;
    width: 2px !important;
    border-radius: 5px;
  }

  scrollbar-width: thin !important;
  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
  -ms-overflow-style: none !important;
  /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
}

.introduce {
  position: relative;
  width: 100%;
  /* 多行文本省略号 样式 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 省略号 展开/收起容器 */
.more {
  box-sizing: border-box;
  cursor: pointer;
  position: absolute;
  // width: 108px;
  right: 0;
  bottom: 0;
  padding: 0 5px;
  color: #006eff;
  font-size: 14px;
  // background-color: #fff;
}
/* 省略号 */
.introduce_ellipsis {
  float: left;
}

.my-train {
  .top-content {
    // height: 280px;
    margin: 20px auto 0;
    background: url("@/assets/certification/title_bk.png") no-repeat;
      // background-size: 100% 100%;
      background-size: 102.5% 114%;
      background-position: -18px -10px;

    .content {
      width: 1440px;
      height: inherit;
      margin: 0px auto;
      padding: 16px 20px;
      .top-tag {
        .el-breadcrumb {
          /deep/ .el-breadcrumb__item {
            .el-breadcrumb__inner {
              font-size: 14px;
              font-weight: normal;
              color: #999999 !important;
            }
          }
        }
      }

      .top-detail {
        .detail-image {
          min-width: 264px;
          height: 172px;
          border-radius: 4px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .detail-bg {
          margin-left: 20px;
          padding: 5px 16px;
          border-radius: 4px;
          background: #f5f8fc;
        }

        .detail-bg:first-child {
          margin-left: 0px;
        }
      }
    }
  }

  .bottom-content {
    width: 1440px;
    min-width: 1440px;
    margin: 30px auto;

    .bottom-left {
      padding: 20px 32px;
      width: 1440px;
      min-height: 840px;
      box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.051);
      border-radius: 4px;
      // background: $colorWhite;
      background: url("@/assets/images/certification/body_bk.png") no-repeat;
      background-size: 100% 100%;
      background-size: 103% 105%;
      background-position: -12px -16px;
      flex-shrink: 0;

      .choice-div {
        width: 100%;
        padding: 4px;
        background: #f5f8fc;
        box-shadow: inset 0px 2px 5px 0px rgba(55, 99, 170, 0.05),
          inset 0px -2px 5px 0px rgba(55, 99, 170, 0.05);
        border-radius: 4px;
        display: flex;
        height: 36px;
        position: relative;
        font-size: 14px;
      }

      .my-create-cursor-item {
        display: flex;
        width: 33.33%;
        align-items: center;
        justify-content: center;
        z-index: 1;
        color: #666;
        cursor: pointer;
      }

      .active-item {
        color: $colorMain;
      }

      .bg-div {
        position: absolute;
        width: 20%;
        background: #ffffff;
        box-shadow: 0px 2px 5px 0px rgba(55, 99, 170, 0.1);
        height: 85%;
        top: 50%;
        border-radius: 4px;
        z-index: 0;
        transform: translateY(-50%);
        transition: left 0.3s ease;
      }
    }

    .bottom-right {
      width: 0;
      height: 840px;
      box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.051);
      border-radius: 4px;
      background: $colorWhite;

      .right-item {
        border-bottom: 1px solid #e6eaed;

        .right-image {
          flex-shrink: 0;
          width: 120px;
          height: 90px;
        }
      }

      .right-item:last-child {
        border-bottom: none;
      }
    }
  }

  .training-el-dialog {
    /deep/ .el-dialog {
      height: 660px;
      display: flex;
      flex-direction: column;
      top: 50%;
      transform: translateY(-50%);

      .signup-ing {
        width: 320px;
        height: 105px;
      }

      .el-dialog__header {
        padding: 0px;
      }

      .el-dialog__body {
        display: flex;
        flex: 1;
        padding: 0px 30px;
        color: $color999;

        .demo-SignUpForm {
          width: 100%;
          display: flex;
          flex-direction: column;

          .grid-form {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 20px;

            /deep/ .el-form-item {
              /deep/ .el-select {
                width: 100%;
              }
            }
          }

          .line-height-item {
            .el-form-item__content {
              line-height: 16px !important;
            }
          }

          .file-status {
            .cancel-status {
              display: none;
            }
          }

          .file-status:hover {
            .success-status {
              display: none;
            }

            .cancel-status {
              display: inline-block;
            }
          }

          .file-div {
            height: 28px;
            border-radius: 4px;
            border: 1px solid $colorMain;
          }
        }
      }

      .el-dialog__footer {
        padding: 0px 32px 0px 0px;
        height: 84px;
        display: flex;
      }
    }
  }
}
.recommend-item {
  .recommend-title {
    display: inline-block;
    padding: 2px 12px;
    background: #f4f6f7;
    border-left: 2px solid #006eff;
    font-size: 14px;
    font-family: Alibaba PuHuiTi 2-55 Regular, Alibaba PuHuiTi 20;
    font-weight: 400;
    color: #333333;
  }

  .content {
    font-size: 14px;
    font-family: Alibaba PuHuiTi 2-55 Regular, Alibaba PuHuiTi 20;
    font-weight: 400;
    color: #666666;
    line-height: 22px;

    /deep/ img {
      max-width: 99%;
    }

    /deep/ video {
      max-width: 99%;
    }
  }
}
.mt-4 {
  margin-top: 4px;
}
.pagination {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
::v-deep .el-table,
::v-deep .el-table th,
::v-deep .el-table tr,
::v-deep .el-table td {
  background-color: transparent;
} 
</style>

<style>
.el-form-item__label {
  color: #666 !important;
}

.el-select {
  width: 100%;
}

.defaluBtn {
  background: #f5f8fc;
  width: 120px;
  height: 36px;
  font-size: 14px;
  font-family: "AlibabaPuHuiTi-2-55";
  border-radius: 3px;
  padding: auto;
}
</style>
