<template>
  <div class="mt-50 w-100">
    <div class="w-100 flex ai-center  mb-20">
      <span class="font-24 color-black f-w-600 mr-10">同行也访问了这些实验室</span>
      <img src="../../../assets/newVersion/title-4.png" alt="">
    </div>
    <div class="w-100 flex-col">
      <div class="flex-col w-100 mb-25 cursor" v-reactive="[260, 346]" v-for="item in list" :key="item.uid"
        style="background: #f4f5f7;" @click="enterPersonalGo(item.uid)">
        <div class="w-100" v-reactive="[260, 220]">
          <img class="w-100 h-100" :src="item.logoPath">
        </div>
        <div class="p-15 flex-col flex-1">
          <p class="ellipsis2 font16 f-w-500">{{ item.name }}</p>
          <div class="content-html mt-20 gray-font font13 f-w-400">{{ item.intro }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  components: {

  },
  watch: {
    '$route.query': {
      handler: function (newVal) {
        if (newVal && newVal.enterpriseUid) {
          this.getInterviewEnterprise()
        }
      },
      immediate: true
    },
  },
  data() {
    return {
      list: [],
    };
  },
  mounted() {
    this.getInterviewEnterprise()
  },
  methods: {
    //访问实验室
    enterPersonalGo(enterpriseUid) {
      if (enterpriseUid == this.$route.query.enterpriseUid) return
      this.$router.replace({ path: '/enterpriseLaboratoryHomepage', query: { enterpriseUid: enterpriseUid } })
    },
    // 过滤html标签
    filterContent(html) {
      const doc = new DOMParser().parseFromString(html, "text/html");
      const images = doc.getElementsByTagName("img");
      for (let i = images.length - 1; i >= 0; i--) {
        images[i].parentNode.removeChild(images[i]);
      }
      const videos = doc.getElementsByTagName("video");
      for (let i = videos.length - 1; i >= 0; i--) {
        videos[i].parentNode.removeChild(videos[i]);
      }
      return doc.body.innerHTML;
    },
    //获取好评率学生数量等
    getInterviewEnterprise() {
      this.$request.lectureHall.getInterview().then(res => {
        // console.log('获取同行访问', res)
        if (res.data.code == 200) {
          const { data } = res.data
          data.forEach(item => {
            item.intro = this.filterContent(item.name)
          })
          this.list = data
        }
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.gray-font {
  color: #c0c1c1;
}

.content-html {
  height: 110px;
  overflow-x: auto;

  // 滚动条整体样式
  &::-webkit-scrollbar {
    display: none !important;
    width: 4px !important;
    /* 纵向滚动条 宽度 */
    border-radius: 5px;
    /* 整体 圆角 */
  }

  //轨道部分
  &::-webkit-scrollbar-track {
    background: #fff !important;
  }

  // 滑块部分
  &::-webkit-scrollbar-thumb {
    background: #D4DBE0;
    min-height: 167px;
    width: 2px !important;
    border-radius: 5px;
  }

  scrollbar-width: thin !important;
  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
  -ms-overflow-style: none !important;
  /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
}
</style>
