<template>
  <div class="tag-detail w-100">
    <div class="pt-30 flex">
      <div class="detail-left">
        <div class="color-black font-24">{{ Detail.fullName }}</div>
        <div class="color-666 font-14 mt-15">颁发证书机构：<span>{{ Detail.labelName }}</span>
        </div>
        <div class="recommend">
          <div class="recommend-item" v-for="contentItem in infos">
            <div class="recommend-title my-20">{{ contentItem.title }}</div>
            <div class="recommend-content font-14 color-666" v-html="contentItem.content" style="overflow: hidden;">
            </div>
          </div>
        </div>

        <div class="mt-20 color-main font-14">
          <span @click="watchDetail(Detail)" class="cursor">查看详情<i class="el-icon-arrow-right ml-5"></i></span>
        </div>
      </div>
      <div class="detail-right ml-40 flex flex-1" style="width:0;">
        <div style="width:260px;height:260px;margin:0px auto;">
          <img :src="Detail.mainPictureUrl"
            style="width:100%;height:100%;border-radius: 4px;object-fit: contain;" alt="加载失败">
        </div>
      </div>
    </div>
    <!-- 查看详情弹窗 -->
    <template>
      <el-dialog class="training-el-dialog" :visible.sync="detailDialogVisible" width="1100px" top="0vh">
        <template slot="title">
          <div class="color-black font-bold font-24"> {{ Detail.fullName }}</div>
        </template>
        <template>
          <div class="my-15 font-14 color-666"><span><span class="mr-10">颁发证书机构：</span>
              {{ Detail.labelName }}</span></div>
          <div class="dialog-content">
            <!-- <div class="recommend-item mb-30" v-for="contentItem in getDescription(currentTagObj.description, 'dialog')">
              <div class="recommend-title mb-20">
                {{ contentItem.title }}
              </div>
              <div v-html="contentItem.content">

              </div>
            </div> -->
            <div class="recommend-item" v-for="contentItem in infos">
              <div class="recommend-title my-20">{{ contentItem.title }}</div>
              <div class="recommend-content font-14 color-666" v-html="contentItem.content" style="overflow: hidden;">
              </div>
            </div>
          </div>
        </template>
      </el-dialog>
    </template>
  </div>
</template>

<script>
export default {
  name: 'HuanyuCollegeNuxtWebTrainContentTagDetail',
  props: {
    currentTagObj: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      Detail: '',
      infos: [],
      detailDialogVisible: false
    };
  },
  watch: {
    'currentTagObj': {
      handler: function (val) {
        if (val && Object.keys(val).length) {
          this.Detail = val
          let arr = []
          arr = JSON.parse(val.description)
          this.infos = arr
        }
      },
      deep: true
    }
  },

  mounted() {
  },
  methods: {
    // 查看详情
    watchDetail() {
      this.detailDialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.tag-detail {

  .recommend-item {
    .recommend-title {
      display: inline-block;
      padding: 4px 12px;
      background: #F4F6F7;
      border-left: 2px solid $colorMain;
      color: $colorBlack;
      font-size: $fontB;
    }

    .recommend-content{
     /deep/ img{
        max-width: 99%;
      }

      /deep/ video{
        max-width: 99%;
      }
    }
  }

  .training-el-dialog {
    /deep/ .el-dialog {
      padding: 40px 20px 10px 80px;
      height: 650px;
      display: flex;
      margin: auto;
      flex-direction: column;
      top: 50%;
      transform: translateY(-50%);
    }

    /deep/ .el-dialog__header {
      padding: 0px;
    }

    /deep/ .el-dialog__body {
      padding: 0px;
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 0;

    }

    .dialog-content {
      overflow-y: auto;

      // 滚动条整体样式
      &::-webkit-scrollbar {
        width: 4px !important;
        /* 纵向滚动条 宽度 */
        border-radius: 5px;
        /* 整体 圆角 */
      }

      //轨道部分
      &::-webkit-scrollbar-track {
        background: #fff !important;
      }

      // 滑块部分
      &::-webkit-scrollbar-thumb {
        background: #D4DBE0;
        min-height: 167px;
        width: 2px !important;
        border-radius: 5px;
      }

      scrollbar-width: thin !important;
      /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      -ms-overflow-style: none !important;

      /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */

    }
  }

  .detail-left {
    width: 460px;

    .recommend {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 7;
      overflow: auto;
      line-height: 22px;
      height: 150px;

      // 滚动条整体样式
      &::-webkit-scrollbar {
        width: 4px !important;
        /* 纵向滚动条 宽度 */
        border-radius: 5px;
        /* 整体 圆角 */
      }

      //轨道部分
      &::-webkit-scrollbar-track {
        background: #fff !important;
      }

      // 滑块部分
      &::-webkit-scrollbar-thumb {
        background: #D4DBE0;
        min-height: 167px;
        width: 4px !important;
        border-radius: 5px;
      }

      scrollbar-width: thin !important;
      /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      -ms-overflow-style: none !important;
      /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
    }
  }

  .detail-right {
    height: 300px;
    background-image: (url(../../../../assets/images/certification/myTraining/certificate-back.png));
  }
}
</style>