<!-- 用户预约直播列表页面 -->
<template>
    <div class="user-live fa-55">
        <div class="operate">
            <div class="flex jc-between ai-center">
                <div class="color-14 color-999">
                    <span>公开课</span><span class="mx-5">/</span><span>售前解决方案工程师透彻讲解视频课程</span>
                </div>
                <hyButton
                    :extendStyle="{ backgroundColor: '#EBF0F7', border: '1px solid #EBF0F7', color: '#006EFF !important', letterSpacing: '0px' }"
                    size="small" width="64px" height="32px" :plain="true" @click="goToPage('/userCenter/myCourse')">返回
                </hyButton>
            </div>

            <div class="mt-5 color-black font-20 fa-65 f-w-b">
                用户预约列表
            </div>

            <div class="flex ai-center mt-10">
                <img v-lazy="require('@/assets/images/userCenter/curriculum/user-live-icon.png')" alt=""
                    style="width: 20px;height: 20px;">
                <span class="color-black f-w-b fa-65 mx-10">44</span><span class="color-666 font-12">直播预约用户</span>
            </div>

            <div class="flex jc-between mt-10">
                <div style="width: 48%;display: flex;align-items: center;">
                    <span class="color-black font-14 mr-10" style="flex-shrink: 0;">搜索用户</span>
                    <div class="flex flex-1">
                        <hySearchInput placeholder="搜索用户昵称、手机" :inputVal.sync="name" @realSearch="" width="380px"
                            height="32px">
                        </hySearchInput>
                    </div>
                </div>
                <div style="width: 48%;display: flex; align-items: center;">
                    <span class="color-black font-14 mr-10" style="flex-shrink: 0;">预约时间</span>
                    <div class="flex flex-1">
                        <el-date-picker style="width: 100%;" v-model="time" type="daterange" range-separator="至"
                            start-placeholder="开始日期" class="custom-date-picker" end-placeholder="结束日期"
                            value-format="yyyy-MM-dd HH:mm:ss" @change="handleTimeChange">
                        </el-date-picker>
                    </div>
                </div>
            </div>

            <div class="my-10 flex jc-end">
                <hyButton
                    :extendStyle="{ backgroundColor: '#EBF0F7', border: '1px solid #EBF0F7', color: '#006EFF !important', letterSpacing: '0px' }"
                    size="small" width="106px" height="32px" :plain="true"><i
                        class="iconfont icon-daochu1 color-main mr-5 fant-16"></i>导出Excel
                </hyButton>
            </div>
        </div>


        <!-- 表格 -->
        <div style="display: flex; flex: 1;flex-shrink: 1;height: 0;">
            <el-table height="auto" class="w-100" :data="tableData" @selection-change="handleSelectionChange"
                v-loading="loading" :header-cell-style="{ background: '#EBF0F7' }">
                <el-table-column type="selection" width="44" />

                <el-table-column show-overflow-tooltip width="230" align="left">
                    <template slot="header">
                        <span class="font-14 color-666">预约时间 </span>
                    </template>
                    <template slot-scope="scope">
                        <span class="cursor-name color-black font-14">{{ scope.row.createTime }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column show-overflow-tooltip width="120" align="left">
                    <template slot="header">
                        <span class="font-14 color-666">用户头像 </span>
                    </template>
                    <template slot-scope="scope">
                        <el-image class="circle cursor" style="width: 32px; height: 32px"
                            :src="scope.row.avatar" fit="cover">
                            <div slot="error" style="width: 100%; height: 100%">
                                <img style="width: 100%; height: 100%" v-lazy="$store.state.defaultAvater" />
                            </div>
                        </el-image>
                    </template>
                </el-table-column>

                <el-table-column show-overflow-tooltip align="left">
                    <template slot="header">
                        <span class="font-14 color-666">用户昵称 </span>
                    </template>
                    <template slot-scope="scope">
                        <span class="cursor-name color-black font-14">{{ scope.row.nickName }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column show-overflow-tooltip width="240" align="left">
                    <template slot="header">
                        <span class="font-14 color-666">用户手机 </span>
                    </template>
                    <template slot-scope="scope">
                        <span class="cursor-name color-black font-14">
                            {{ getMobile(scope.row) }}
                        </span>
                        <i v-if="!scope.row.lookFlag" class="font-16 color-999 cursor iconfont icon-biyan"
                            @click="lookOperate(scope.row, true)">

                        </i>

                        <i v-else class="font-16 color-main cursor iconfont icon-guankan"
                            @click="lookOperate(scope.row, false)">

                        </i>
                    </template>
                </el-table-column>
                <template slot="empty">
                    <div class="flex-col jc-center ai-center">
                        <img v-lazy="require('@/assets/images/userCenter/curriculum/data-empty.png')" alt=""
                            style="width: 120px; height: 75px" />
                        <span class="color-666 font-14">暂无用户</span>
                    </div>
                </template>
            </el-table>
        </div>

        <div style="height: 55px; flex-shrink: 0;display: flex;justify-content: end;align-items: center;">
            <div style="height: 40px;">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="paramsForm.pageNo" :page-sizes="[100, 200, 300, 400]" :page-size="paramsForm.pageSize"
                    layout="total, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
    </div>
</template>
<script>

export default {
    name: 'HuanyuCollegeNuxtWebUserLiveList',
    components: {

    },
    data() {
        return {
            name: '',
            time: [],
            selectList: [],
            loading: false,
            tableData: [],
            paramsForm: {
                pageNo: 1,
                pageSize: 10,
                endTime: '',
                startTime: '',
                type: 3,
                uid: '',
            },
            total: 0
        };
    },

    mounted() {
        if (this.$route.query.uid) {
            this.paramsForm.uid = this.$route.query.uid
            this.getData()
        }
    },
    watch: {

    },
    computed: {

    },

    methods: {
        // 获取列表数据
        getData() {
            for (let i = 0; i < 5; i++) {
                this.tableData.push({
                    id: i,
                    nickName: `张三${i}`,
                    createTime: `2023-6-20 20:00:00`,
                    avatar: `https://test.cmdr.com.cn:1443/blog/web/jpg/2023/5/17/1684306541303.jpg`,
                    mobile: `1761076766${i}`,
                    lookFlag: false,

                })
            }

            // const data = JSON.parse(JSON.stringify(this.paramsForm))
            // this.$request.myCourse.getMyCreateLiveFansList(data).then((res) => {
            //     console.log('ressss', res)
            // })
        },
        // 脱敏操作
        getMobile({ mobile, lookFlag }) {
            return lookFlag ? mobile : this.hidePhoneNumber(mobile)
        },
        // 脱敏操作
        lookOperate({ id }, type) {
            this.tableData.forEach((item) => {
                if (item.id == id) {
                    item.lookFlag = type
                }
            })
            this.$forceUpdate();
        },
        // 脱敏 手机号
        hidePhoneNumber(phoneNumber) {
            if (phoneNumber.length !== 11) {
                return phoneNumber; // 如果手机号码长度不是11位，直接返回原手机号码
            }

            const prefix = phoneNumber.slice(0, 3); // 取手机号码前3位
            const suffix = phoneNumber.slice(7); // 取手机号码后4位

            return `${prefix}****${suffix}`; // 拼接隐藏中间数字的手机号码
        },
        handleSelectionChange(val) {
            this.selectList = val.map((item) => item.uid);
        },
        // 时间选择
        handleTimeChange(time) {
            this.paramsForm.startTime = time && time[0] || ''
            this.paramsForm.endTime = time && time[1] || ''
        },
        handleSizeChange(val) {
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
        },
        // 通过传进来的路由路径进行跳转
        goToPage(url) {
            if (!url) return
            this.$router.push({
                path: url
            });
        },
    },
};
</script>
  
<style lang="scss" scoped>
.user-live {
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
    border-radius: 4px;
    padding: 20px;
    max-height: 780px;
    display: flex;
    flex-direction: column;

    .operate {
        flex-shrink: 0;

        div:nth-child(3) {
            background-color: #F5F8FC;
            border-radius: 4px;
            height: 40px;
            padding: 0 15px;
            border: 1px solid #E6EAED;
        }

        div:nth-child(4) {
            padding-bottom: 15px;
            border-bottom: 1px solid #E6EAED;
        }

        .custom-date-picker {
            height: 32px;

            /deep/ i {
                line-height: 24px;
            }

            /deep/ .el-range-separator {
                line-height: 24px;
            }
        }
    }


}
</style>