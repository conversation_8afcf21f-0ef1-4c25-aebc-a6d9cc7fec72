<template>
    <div class="knowlegdeCertificate">
        <div class="content w-100" v-if="filterList.length > 0 && show">
            <div class="bottom">
                <wangEditor :editorConfig="{
                        readOnly: true,
                        placeholder: '暂无内容'
                    }" :wrapperStyle="{
            border: 'none'
        }" :onlyEditor="true" height="auto" :content="content"></wangEditor>
            </div>
            <div class="top flex-wrap" :style="{
                    'justifyContent': filterList.length === 1 ? 'center' : 'flex-start'
                }">
                <div class="item" v-for="(item, index) in filterList" :key="index"
                    @click="openView(item.info ? item.info.url : '')">
                    <div class="item-head">
                        <img :src="item.info ? item.info.url : ''">
                        <div class="item-open">
                            <i class="el-icon-zoom-in font16" style="color: #fff;"></i>
                        </div>
                    </div>
                    <div class="item-bottom flex-center-col">
                        <p class="title"> {{ item.info.name }}</p>
                        <p class="title2 ellipsis2" :title="item.info.fullName"> {{ item.info.fullName }}</p>
                    </div>
                </div>
            </div>

        </div>
        <div v-else>
            <el-empty style="margin-top: 20px;" description="暂无数据"></el-empty>
        </div>
        <el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="imgURL"></el-image-viewer>
    </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
    name: '',
    components: {
        ElImageViewer
    },
    data() {
        return {
            imgURL: [],
            showViewer: false,
            show: false,
            filterList: [],
            content: '',
            listPage: {
                contentType: 6,
            },
        }
    },
    props: {
        uid: {
            type: String,
            default: ''
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        openView(img) {
            this.showViewer = true
            this.imgURL.push(img)
        },
        closeViewer() {
            this.showViewer = false
            this.imgURL = []
        },
        init() {
            this.$nextTick(() => {
                this.$debounce(() => {
                    this.getTrainKnowledgeDetail()
                }, 200)();
            })
        },
        //获取知识点分类详情
        async getTrainKnowledgeDetail() {
            const { uid, listPage } = this
            console.log('证书Uid', { uid, ...listPage })
            try {
                const { data } = await this.$axios.post(this.$api.gerTrainKnowledgeInfo, { uid, ...listPage })
                if (data.code === 200) {
                    let list = [] //证书
                    const { records = [], certificateAcquisitionCondition = "" } = data.data || {}
                    this.content = certificateAcquisitionCondition
                    this.show = true
                    records.forEach(item => {
                        list.push({
                            id: item?.info?.uid,
                            label: item?.info?.issueOrganize,
                            ...item
                        })
                    })
                    this.filterList = list
                    this.active = list?.[0]?.id || ''
                    this.select = list[0] || {}
                    // console.log('证书', list)
                    // console.log('获取证书详情', data)
                }
            } catch (err) {
                console.log('获取证书详情失败', err)
            }
        },
        //切换seticon
        levelChange(v) {
            this.active = v.id
            this.select = v
        },
    },
}
</script>

<style lang="scss" scoped>
.knowlegdeCertificate {
    .content {
        .top {
            margin-top: 32px;
            display: flex;
            flex-direction: row;
            align-items: center;
            // justify-content: flex-start;


            .item {
                width: calc(100% / 3 - 16px);
                margin-right: 16px;
                border: 1px solid #E6EAED;
                border-radius: 6px;
                margin-bottom: 20px;

                .item-head {
                    width: 100%;
                    height: 237px;
                    border-bottom: 1px solid #e6eaed;
                    position: relative;

                    .item-open:hover {
                        z-index: 10; // 设置一个较高的层级
                        opacity: 1;
                        background-color: rgba(0, 0, 0, 0.2);
                    }

                    .item-open {
                        position: absolute;
                        top: 0;
                        right: 0;
                        opacity: 0;
                        width: inherit;
                        height: inherit;
                        background-color: rgba(0, 0, 0, 0.2);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.3s;
                    }

                    img {
                        z-index: 1;
                        border-radius: 6px;
                        width: 100%;
                        height: 100%;
                    }
                }

                .item-bottom {
                    height: 68px;

                    .title1 {}

                    .title2 {
                        margin-top: 5px;
                        text-align: center;
                    }
                }

            }
        }

        .bottom {}
    }
}
</style>