<template>
    <div class="orderer-theory-container min-w-1440 flex jc-center bg-w">
      <div class="w-1440">
        <el-row style="padding-bottom: 32px;" :gutter="24">
            <el-col :span="24">
                <div class="head">
                    <div class="head-icon-box">
                        <span>系列Talk</span>
                        <div class="icon-img ser"></div>
                    </div>
                    <span @click="toPage('/ordererTheory/profile',{uid: systemUidMap.get('系列Talk'),type:1})" class="cursor">更多</span>
                </div>
            </el-col>
            <!-- 内容区域 -->
            <el-col :span="24">
                <div class="item-box">
                    <template v-if="seroData.length">
                        <div class="item radius9" v-for="(item, index) in seroData"  @click="toPage('/ordererTheory/seriesTalk',{uid:item.uid})" :key="index + 9999">
                            <div class="tag-box flex jc-between">
                                <div class="left-num">
                                    <span>共</span>
                                    <span class="num">{{ item.articleCount || 0 }}</span>
                                    <span>期</span>
                                </div>
                                <!-- <div class="tag cursor" @click.stop="subscription(item)">
                                    <i v-if="item.isSubscribe==0" class="iconfont icon-add1"></i>
                                    <span>{{ item.isSubscribe==0?'订阅':'取消订阅' }}</span>
                                </div> -->
                            </div>
                            <p class="title">{{ item.title }}</p>
                            <img v-lazy="item.materialUrl" alt="" srcset="">
                        </div>
                    </template>
                    <el-empty style="margin: 0 auto;" v-else :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
                </div>
            </el-col>
            <el-col :span="24">
                <div class="head">
                    <div class="head-icon-box">
                        <span>人物专访</span>
                        <div class="icon-img personal"></div>
                    </div>
                    <span @click="toPage('/ordererTheory/profile',{uid: systemUidMap.get('人物专访'),type:2})" class="cursor">更多</span>
                </div>
            </el-col>
            <!-- 内容区域 -->
            <el-col :span="24">
                <div class="item-box">
                    <template v-if="profileData.length">
                        <div
                        @click="toPage('/information/articleDetails', { uid: item.uid, articleSource: item.articleSource,type:1 })"
                        class="item mb-20 radius9" v-for="(item, index) in profileData" :key="index">
                            <div class="tag-box">
                                <!-- <div class="left-num">
                                    <span>共</span>
                                    <span class="num">6</span>
                                    <span>期</span>
                                </div> -->
                                <!-- <div class="tag">
                                    <i class="iconfont icon-add1"></i>
                                    <span>订阅</span>
                                </div> -->
                            </div>
                            <p class="title">{{ item.title }}</p>
                            <img v-lazy="item.fileUidUrl" alt="" srcset="">
                        </div>
                    </template>
                    <el-empty style="margin: 0 auto;" v-else :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
                </div>
            </el-col>
        </el-row>
        <!-- 指令者动态 -->
        <!-- <el-row :gutter="24">
            <el-col :span="24">
                <div class="head-two">
                    <p class="h-order-space">指令者动态</p>
                    <div class="icon-img"></div>
                </div>
            </el-col>
            <el-col :span="24">
                <div class="instruction-dynamic">
                    <div class="left">
                        <div class="cover">
                            <img src="https://cdn.wuzhiing.cn/hyfile/%E5%AE%B9%E5%99%A8%402x.png" alt="" srcset="">
                        </div>
                        <span class="title">华为</span>
                        <div class="subscription">
                            <i class="iconfont icon-add1"></i>
                            <span>订阅</span>
                        </div>
                    </div>
                    <div class="right">
                        <div class="list-box">
                            <div class="item" v-for="item in 6" @click="toPage('/ordererTheory/dynamic')">
                                <div class="row-head">
                                    <div class="row-head-left">
                                        <span class="right-title">华为孟晚舟:生成式AI正快速融入金融业务</span>
                                        <div class="right-icon">
                                            <i style="margin:0 2px 0 12px;" class="iconfont icon-fenxiang1"></i>
                                            <span class="share">分享</span>
                                        </div>
                                    </div>
                                    <div class="row-head-right">
                                        <i class="iconfont icon-shijian"></i>
                                        <span>12.5k</span>
                                        <div class="hr"></div>
                                        <i class="iconfont icon-a-lianjie2"></i>
                                        <span>智东西</span>
                                        <div class="hr"></div>
                                        <i class="iconfont icon-icon_dianzan"></i>
                                        <span>6</span>
                                    </div>
                                </div>
                                <p class="desc">
                                    智东西6月7日消息，在今日举行的华为全球智慧金融峰会2023上，华为副董事长、轮值董事长、首席财务官孟晚府发表主题演讲。她谈道，今天，技术的奇点正在临近，生成式A1、云、物联网等技术，正在快速融入金融的业务。作业数字化、数字平台化、平台智能化、智能实战化是华为数字化转型中的“四化方向”。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row> -->
      </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            systemUidMap: new Map([
                ['系列Talk',''],
                ['人物专访',''],
                ['指令者动态','']
            ]),
            seroData: [
                {
                    title: '谢菲尔德大学NLP组系列Talk',
                    cover:'https://cdn.wuzhiing.cn/hyfile/ser-006.png'
                },
                {
                    title: '顶会|ICLR 2022 0ral系列Talk',
                    cover: 'https://cdn.wuzhiing.cn/hyfile/ser-001.png'
                },
                {
                    title: '顶会cVpR2022Takt区+云际会回顾视频 ',
                    cover: 'https://cdn.wuzhiing.cn/hyfile/ser-002.png'
                }
            ],
            profileData: [
                {
                    title: '谢菲尔德大学NLP组系列Talk',
                    cover: 'https://cdn.wuzhiing.cn/hyfile/ser-003.png'
                },
                {
                    title: '顶会cVpR2022Takt区+云际会回顾视频 ',
                    cover: 'https://cdn.wuzhiing.cn/hyfile/ser-004.png'
                },
                {
                    title: '顶会|ICLR 2022 0ral系列Talk',
                    cover: 'https://cdn.wuzhiing.cn/hyfile/ser-005.png'
                }
            ],
            dynamicData:[]
        }
    },
    created() {
        this.getInformationTerritory();
    },
    methods: {
        subscription(item) {
            let params = {
                uid: item.uid,//系列Talkuid
                // isSubscribe:item.isSubscribe==0?1:0,//0未订阅  1已订阅
            }
            this.$debounce(async () => {
                this.$request.information.subscribeSeries(params).then((res) => {
                    if (res.data.code == this.$code.SUCCESS) {
                        // 更新订阅状态
                        item.isSubscribe = item.isSubscribe == 0 ? 1 : 0;
                    }
                });
            })();
        },
        toPage(url, query = {}) {
            this.$router.push({
                path: url,
                query
            })
        },
        async getInformationTerritory() {
            this.$request.information
                .getInformationTerritory({ columnName: "指令者说" })
                .then((res) => {
                    if (res.data.code == this.$code.SUCCESS) {
                        let datas = res.data.data??[];
                        datas.forEach(item => {
                            if (this.systemUidMap.has(item.name)) {
                                this.systemUidMap.set(item.name, item.uid)
                            }
                        })
                        console.log(this.systemUidMap)
                        // 请求指令者说下的体系数据
                        this.initOrdererTheoryData();
                    }
                });
        },
        // 系列Talk
        async getSeriesTalk(uid) {
            if (!uid) return this.seroData = [];
            let result = await this.$request.instruct.selectTalkList({
                size: 3,
                current: 1,
                publicityHidden: 1
            });
            if (result.data.code == this.$code.SUCCESS) {
                this.seroData = result.data.data.records ?? [];

                console.log("seroData",this.seroData)
            }
        },
        // 人物专访
        async getProfile(uid) {
            if (!uid) return this.profileData = [];
            let result = await this.$request.instruct.selectBlogBySeriesId({
                uid
            });
            if (result.data.code == this.$code.SUCCESS) {
                const list = result.data.data ?? [];
                this.profileData = list.filter((item, index) => index < 3)
            }
        },
        // 指令者动态
        getDynamic(uid) {
            if (!uid) return this.dynamicData = [];

        },
        initOrdererTheoryData() {
            for (let [key, value] of this.systemUidMap) {
               switch (key) {
                   case '系列Talk':
                       this.getSeriesTalk(value);
                       break;
                   case '人物专访':
                       this.getProfile(value);
                       break;
                   case '指令者动态':
                       this.getDynamic(value);
                       break;
                   default:
                       break;
               }
            }
        }
    }
}
</script>
<style lang='scss' scoped>
.orderer-theory-container {
    .head-two{
        display: flex;
        align-items: center;
        font-size: 24px;
        .icon-img{
            width: 20px;
            height: 20px;
            background: url('~/static/images/titleDes.png') no-repeat no-repeat center center;
            background-size: cover;
            margin-left: 8px;
        }
    }
    .h-order-space{
        font-size: 24px;
        font-family: Alibaba PuHuiTi 2.0-65 Medium, Alibaba PuHuiTi 20;
        font-weight: 500;
        color: #333333;
        margin: 32px 0;
        font-weight: bold;
    }
    .instruction-dynamic {
        width: 100%;
        padding: 32px 40px;
        background: #FFFFFF;
        display: flex;

        .left {
            padding-top: 60px;
            width: 366px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;

            .title {
                font-size: 18px;
                font-family: Alibaba PuHuiTi 2.0-65 Medium, Alibaba PuHuiTi 20;
                font-weight: 500;
                color: #333333;
                margin: 24px 0 12px 0;
            }

            .cover {
                width: 160px;
                height: 160px;
                background: rebeccapurple;
                img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .subscription {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 64px;
                height: 26px;
                border-radius: 4px 4px 4px 4px;
                opacity: 1;
                border: 1px solid #006EFF;
                color: #006EFF;
                font-size: 12px;
                font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
                font-weight: 400;

                i {
                    margin-right: 3px;
                    font-size: 12px;
                }
            }
        }

        .right {
            border-left: 1px solid #E6EAED;
            padding-left: 56px;
            flex: 1;
            .item{
                border-bottom: 1px solid #E6EAED;
                padding-bottom: 12px;
                cursor: pointer;
                margin-bottom: 12px;
                &:last-of-type{
                    border-bottom: none;
                }
                .desc{
                    display: none;
                }
                .right-icon{
                    display: none;
                }
                .right-title{
                    font-size: 14px;
                    font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
                    font-weight: 400;
                    color: #333333;
                    position: relative;
                    &::before{
                        content: '';
                        position: absolute;
                        width: 6px;
                        height: 6px;
                        background: #006EFF;
                        border-radius: 1px 1px 1px 1px;
                        left: -16px;
                        top: 8px;
                    }
                }
                &:first-of-type{
                    .right-title{
                        font-size: 16px;
                        font-weight: bold;
                    }
                    .desc{
                        display: flex;
                    }
                    .right-icon{
                        display: block;
                    }
                }
                &:hover{
                    // .right-icon{
                    //     display: block;
                    // }
                    // .desc{
                    //     display: flex;
                    // }
                    .right-title{
                        color: #006EFF;
                        // font-size: 16px;
                        // font-weight: bold;
                    }
                }
            }
            .desc {
                margin-top: 12px;
                font-size: 14px;
                font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
                font-weight: 400;
                color: #666666;
            }

            .row-head {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .row-head-left {
                    display: flex;
                    width: max-content;
                    flex-direction: row;
                    align-items: center;
                    .title{

                    }
                    i {
                        color: #999999;
                    }

                    .share {
                        font-size: 12px;
                        font-weight: 400;
                        color: #999999;
                    }
                }

                .row-head-right {
                    display: flex;
                    align-items: center;
                    font-size: 12px;
                    font-weight: 400;
                    color: #999999;
                    i{
                        margin-right: 4px;
                    }
                    .hr{
                        width: 1px;
                        height: 12px;
                        background: #E6EAED;
                        border-radius: 1px 1px 1px 1px;
                        margin: 0 8px;
                    }
                }
            }
        }
    }

    .head {
        width: 100%;
        margin: 48px 0 24px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24px;
        font-family: Alibaba PuHuiTi 2.0-75 SemiBold, Alibaba PuHuiTi 20;
        font-weight: 600;
        color: #333333;
        .head-icon-box{
            display: flex;
            align-items: center;

            .icon-img{
                width: 20px;
                height: 20px;
                background: url('~/static/images/titleDes.png') no-repeat no-repeat center center;
                background-size: cover;
                margin-left: 8px;
            }
            .ser{
                background: url('~/static/images/titleDes2.png') no-repeat no-repeat center center;
                background-size: cover;
            }
            .personal{
                background: url('~/static/images/titleDes3.png') no-repeat no-repeat center center;
                background-size: cover;
            }
        }
        .cursor{
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #999999;
        }
    }

    .item-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        &::after{
            content: '';
            width: calc((100% - 80px) / 3);
            height: 0;
        }
        img{
            position: absolute;
            top: 0;
            width: 100%;
            height: 100%;
        }
        .tag-box{
            z-index: 99;
            position: absolute;
            display: flex;
            align-items: center;
            padding: 20px;
            top: 0;
            left: 0;
            right: 0;
            .left-num{
                font-size: 14px;
                font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
                font-weight: 400;
                color: rgba(255,255,255,0.6);
                .num{
                    font-size: 14px;
                    font-weight: 700;
                    color: #FFFFFF;
                }
            }
        }
        .item {
            border-radius: 2px;
            position: relative;
            width: calc((100% - 80px) / 3);
            padding-top: calc(((100% - 80px) / 3) * 0.56);
            position: relative;
            overflow: hidden;

            .title {
                z-index: 99;
                position: absolute;
                bottom: 20px;
                left: 20px;
                font-size: 16px;
                font-family: Alibaba PuHuiTi 2.0-65 Medium, Alibaba PuHuiTi 20;
                font-weight: 500;
                color: #FFFFFF;
            }

            .tag {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 64px;
                height: 26px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px 4px 4px 4px;
                font-size: 12px;
                font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
                font-weight: 400;
                color: #FFFFFF;
                i {
                    font-size: 12px;
                    margin-right: 3px;
                }
            }
        }
    }
}
.radius9{
  border-radius: 9px !important;
}
</style>