const fs = require('fs');
const path = require('path');
const webpackObfuscator = require('webpack-obfuscator');
const CompressionPlugin = require('compression-webpack-plugin');
import config from './utils/config.js';
import env from './utils/env.js';

function pluginsEnvPanel () {
  let whileArr = ['test', 'release'];
  let plugins = [
    // gzip压缩
    new CompressionPlugin({
      test: /\.js$|\.html$|\.css/, // 匹配文件名
      threshold: 10240, // 对超过10kb的数据进行压缩
      minRatio: 0.7, // 至少压缩的比例
      algorithm: "gzip", // 采用的压缩算法
      deleteOriginalAssets: false // 是否删除原文件
    }),
    new webpackObfuscator({
      compact: true,
      controlFlowFlattening: false,
      deadCodeInjection: true,
      debugProtection: false,
      debugProtectionInterval: 0,
      disableConsoleOutput: false,
      identifierNamesGenerator: 'hexadecimal',
      log: false,
      renameGlobals: false,
      rotateStringArray: false,
      selfDefending: false,
      stringArray: false,
      stringArrayEncoding: ['none'],
      stringArrayThreshold: 0.5,
      unicodeEscapeSequence: false,

      /**
       * 代码混淆配置
       * 表达式混淆、成员表达式混淆、属性字面量混淆、函数声明混淆、函数表达式混淆、catch 从句混淆、
       * switch 语句混淆、逻辑表达式混淆、赋值表达式混淆、调用表达式混淆、成员表达式混淆、new 表达式混淆、
       * for-in 语句混淆、for-of 语句混淆、while 语句混淆、do-while 语句混淆、if 语句混淆、switch 语句混淆、
       * return 语句混淆、变量声明混淆、函数提升混淆、模板字符串混淆、字面量混淆和控制流混淆
       */
      expression: true,
      propertyLiterals: true,
      functionDeclarations: true,
      functionExpressions: true,
      catchClauses: true,
      logicalExpression: true,
      assignmentExpression: true,
      callExpressions: true,
      memberExpressions: true,
      newExpressions: true,
      forInStatements: true,
      forOfStatements: true,
      whileStatements: true,
      doWhileStatements: true,
      ifStatements: true,
      switchStatements: true,
      returnStatements: true,
      variableDeclarations: true,
      functionHoisting: true,
      templates: true,
      literals: true,
      controlFlow: true,
      globalVars: true,
      variables: true,
    }, [])
  ];
  // if (process.env.NODE_ENV === 'production') {
  //   return plugins;
  // }
  return process.env.MODE==='release'?[]:plugins.slice(0,1);
}
export default {
  html: {
    minify: {
      collapseBooleanAttributes: process.env.NODE_ENV === 'production',
      collapseWhitespace: false,
      decodeEntities: process.env.NODE_ENV === 'production',
      minifyCSS: process.env.NODE_ENV === 'production',
      minifyJS: process.env.NODE_ENV === 'production',
      processConditionalComments: process.env.NODE_ENV === 'production',
      removeAttributeQuotes: false,
      removeComments: false,
      removeEmptyAttributes: process.env.NODE_ENV === 'production',
      removeOptionalTags: false,
      removeRedundantAttributes: process.env.NODE_ENV === 'production',
      removeScriptTypeAttributes: false,
      removeStyleLinkTypeAttributes: false,
      removeTagWhitespace: false,
      sortClassName: false,
      trimCustomFragments: process.env.NODE_ENV === 'production',
      useShortDoctype: process.env.NODE_ENV === 'production'
    }
  },
  parallel: true,
  env: {
    MODE: process.env.MODE
  },
  vue: {
    config: {
      productionTip: false,
      devtools: false
    }
  },
  render: {
    resourceHints: false,
    bundleRenderer: {
      shouldPrefetch: () => false,
      shouldPreload: (fileWithoutQuery, asType) => false,
    },
  },
  ssr: false,
  mode: 'spa',
  target: 'static',
  /*
   ** Headers of the page
   */
  head: {
    title: '指令者',
    meta: [
      { charset: 'utf-8' },
      { name: "keywords", content: "指令者" },
      { name: "description", content: "指令者" },
      { name: "referrer", content: "origin" },
      { 'data-draft-node': 'block', 'data-draft-type': 'table', 'data-size': 'normal', 'data-row-style': 'normal' },
      { name: 'viewport', content: 'width=device-width, initial-scale=0.1' },
      process.env.NODE_ENV === 'production' ? { 'http-equiv': "Content-Security-Policy", 'content': 'upgrade-insecure-requests' } : {}
      // { name:"referrer", content:"no-referrer" }
    ],
    link: [
      { rel: "dns-prefetch", href: `${env[process.env.MODE]}`, crossorigin: "anonymous" },
      { rel: "dns-prefetch", href: "//at.alicdn.com/", crossorigin: "anonymous" },
      // { rel: "dns-prefetch", href: "//cdn.staticfile.org/", crossorigin: "anonymous" },
      // { rel: "dns-prefetch", href: "//lib.baomitu.com/", crossorigin: "anonymous" },
      { rel: "dns-prefetch", href: "//cdnjs.cloudflare.com/", crossorigin: "anonymous" },

      { rel: 'stylesheet', href: '/wangEditor5/wangeditor5_5.1.23_css_style.min.css' },

      { rel: 'stylesheet', href: "//at.alicdn.com/t/c/font_3444523_uy7roqa0v5.css" },
      { rel: "prefetch", as: "document", href: `${env[process.env.MODE]}/articleDetail`, crossorigin: "anonymous" },

      // { rel: "preload", as: "style", href: "//lib.baomitu.com/element-ui/2.15.13/theme-chalk/index.min.css", crossorigin: "anonymous" },
      { rel: 'icon', type: 'image/x-icon', href: '/home-logo.ico' },

      { rel: "preload", as: "style", href: "/css/video-js.min.css", crossorigin: "anonymous" },
      // { rel: "preload", as: "style", href: '//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css', integrity: "sha512-SfTiTlX6kk+qitfevl/7LibUOeJWlt9rbyDn92a1DqWOw9vWG2MFoays0sgObmWazO5BQPiFucnnEAjpAB+/Sw==", crossorigin: "anonymous" }
    ],
    script: [
      // { src: "//lib.baomitu.com/vue/2.6.14/vue.min.js", defer: true },

      // { src: "//lib.baomitu.com/element-ui/2.15.13/index.min.js", defer: true },
      // { src: "//cdn.staticfile.org/element-ui/2.15.13/index.min.js", defer: true },
      // { src: "https://gapi.bmy114.com/static/js/chat-front.js" },
      { src: '/tinymce/tinymce.min.js', defer: true },
      { src: '/tinymce/langs/zh-Hans.js', defer: true },

      { src: "/wangEditor5/wangeditor5_5.1.23_index.min.js", defer: true },

      // { src: "//cdn.staticfile.org/echarts/5.4.2/echarts.min.js", defer: true },
      // { src: "//lib.baomitu.com/echarts/4.9.0-rc.1/echarts.min.js", defer: true },
      // { src: "https://js.fundebug.cn/fundebug.2.8.4.min.js", apikey: "121c67153fa8ab9c468025da32d606d1f346dae0b5ce69a58cb808c83ba249b2", crossorigin: "anonymous" },
    ],
  },
  generate: {
  },
  /*
   ** Customize the progress-bar color
   */
  loading: false,

  /*
   ** Global CSS
   */
  css: [
    'common/element-variables.scss',
    'static/css/quill.bubble.css',
    'static/css/quill.core.css',
    'static/css/quill.snow.css',
    'static/css/quill-better-table.css',
    'common/aliFont.css',
    'common/elementui-icon.extend.scss'
  ],

  /*
   ** Plugins to load before mounting the App
   */
  plugins: [
    '@/plugins/clipboard',
    { src: '@/plugins/element-ui', ssr: false },
    '@/plugins/common-function',
    '@/plugins/global-components',
    '@/plugins/validate-js',
    '@/plugins/vue-video-player',
    '@/plugins/router',
    { src: '@/plugins/directives', ssr: false },
    { src: '@/plugins/consoleVersion', ssr: false },
    { src: '@/plugins/socket', ssr: false },
    { src: '@/plugins/axios', ssr: true },
    { src: '@/plugins/JSEncrypt', ssr: false },
    { src: '@/plugins/wxLogin', ssr: false },
    { src: '@/plugins/main', ssr: true },
    { src: '@/plugins/dataPersistence', ssr: false },
    { src: '@/plugins/seamless-scroll', ssr: false }, // ssr: false
    { src: '@/plugins/responseCode', ssr: true },
    { src: '@/plugins/vnc', ssr: false }
  ],

  /*
   ** Nuxt.js modules
   */
  modules: [
    '@nuxtjs/style-resources',
    '@nuxtjs/axios',
    '@nuxtjs/proxy',
    'cookie-universal-nuxt', ['cookie-universal-nuxt', { parseJSON: true }]
  ],
  axios: {
    proxy: process.env.NODE_ENV === 'development', // 允许跨域
    // progress: false, // 禁止显示loading状态
  },
  proxy: {
    // 使用fastmock情况下注释该项，不使用则反之
    '/proxy': {
      target: env['dev'], // 目标接口域名
      secure: false, // 允许https请求
      changeOrigin: true,
      pathRewrite: {
        '^/proxy': ''
      }
    },
    '/cmd-static': {
      target: env['dev_static'], // 目标接口域名
      secure: false, // 允许https请求
      changeOrigin: true,
      pathRewrite: {
        '^/cmd-static': ''
      }
    }
  },
  styleResources: {
    scss: ['./common/base.scss', './common/mixin.scss', './assets/global.scss','./common/personalLaboratoryTheme.scss']
  },
  server: {
    port: process.env.APP_PORT, // default: 3000
    host: '0.0.0.0', // default: localhostpm
    https: config.enableSSL ? {
      key: fs.readFileSync(path.resolve(__dirname, 'cert.key')),
      cert: fs.readFileSync(path.resolve(__dirname, 'cert.crt'))
    } : false
  },
  /* hardSource: process.env.NODE_ENV === 'development',//中间件缓存(优化构建速度)
   ** Build configuration
   */
  build: {
    // vendor:['element-ui'],//过期
    cache: false,
    hardSource: false,//中间件缓存(优化构建速度)
    transpile: [/^element-ui/],
    // cdn https://cdn.wuzhiing.cn/_nuxt/
    // 默认/_nuxt/
    publicPath: process.env.MODE === 'release' ? 'https://cdn.wuzhiing.cn/_nuxt/' :'/_nuxt/',
    extractCSS: process.env.NODE_ENV === 'production', // 合并代码到一个文件
    /*
    ** You can extend webpack config here
    */
    extend (config, ctx) {
      if (ctx.isClient) {
        config.externals = {
          'ali-oss': 'ali-oss',
          // "vue": "Vue",
          // "echarts": "echarts"
        };
        // 处理所有图片格式
        // const imgRule = {
        //   test: /\.(png|jpe?g|gif|ico|svg)$/,
        //   use: [
        //     {
        //       loader: 'image-webpack-loader',
        //       options: {
        //         // 压缩 jpeg 的配置
        //         mozjpeg: {
        //           progressive: true,
        //           quality: 65
        //         },
        //         // 使用 imagemin**-optipng 压缩 png，enable: false 为关闭
        //         optipng: {
        //           enabled: false,
        //         },
        //         // 使用 imagemin-pngquant 压缩 png
        //         pngquant: {
        //           quality: [0.65, 0.80],
        //           speed: 4
        //         },
        //         // 压缩 gif 的配置
        //         gifsicle: {
        //           interlaced: false,
        //         },
        //         // 开启 webp，会把 jpg 和 png 图片压缩为 webp 格式
        //         webp: {
        //           quality: 75
        //         }
        //       }
        //     }
        //   ]
        // }

        // 添加到现有的规则中
        // config.module.rules.push(imgRule);
      }
    },
    plugins: pluginsEnvPanel(),
    analyze: false,//开启打包分析
    optimization: {
      minimize: process.env.NODE_ENV === 'production',//开发环境禁用
      usedExports: true,// Tree Shaking,usedExports：通过标记某些函数是否被使用，之后通过Terser来进行优化的
      splitChunks: {
        //将代码分离到不同的bundle中，之后我们可以按需加载，或者并行加载这些文件
        chunks: "all",
        minSize: 10000,
        maxSize: 244000,
        minChunks: 3,
      },
    },
    babel: {
      plugins: [
        ["component",
          {
          "libraryName": "element-ui",
          "styleLibraryName": "theme-chalk"
          }
        ]
      ]
    }
  },
  buildModules: [
    '@nuxt/postcss8',
  ],
  router: {
    middleware: ['pageTitle'], // 中间件
    prefetchPayloads: false,
    prefetchLinks: false
  }
}
