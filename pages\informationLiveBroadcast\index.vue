<template>
  <div class="orderer-theory-container min-w-1440">
    <div ref="bgContent" :style="bgBoxStyle" class="bk mb-40"></div>
    <div>
      <div class="flex jc-center">
        <div class="w-1440">
          <div class="flex">
            <div style="width: 20%; min-width: 285px;" class="mr-40">
              <div class="flex jc-between ai-center">
                <div class="all-topics">全部话题</div>
                <div class="all-num">
                  <span class="font-16 color-main fa-65 f-w-500">{{ topicList.length || 0 }}</span>
                  话题
                </div>
              </div>
              <div v-for="(item) in topicListTwo" :key="item.uid">
                <div class="mb-24 mt-30 font-20 f-w-500">{{ item.sortName }}</div>
                <div class="flex jc-between ai-center flex-wrap">
                  <div
                    :class="knowledgeId == blog.uid ? 'topical-content mb-20':'topical-content2 topical-content mb-20'"
                    v-for="(blog) in item.blogSortList"
                    @click="toLecture('/singleTopic', { knowledgeTopic: blog.uid })" :key="blog.uid"
                    @mousemove="blogShowTrue(blog)" @mouseout="blogShowFalse(blog)">
                    <div class="topical-content-title flex ai-center">
                      <img class="mr-10" src="@/static/images/information/topical.png" alt="">
                      <span class="ellipsis">{{ blog.sortName }}</span>
                    </div>
                    <div class="ml-28 content-num">{{ blog.count || 0 }} 内容</div>
                    <!-- <div class="ml-28 interest" @mousemove="blogShowTrue(blog)" v-if="knowledgeId == blog.uid">
                      <img style="margin-right:3px;" src="@/static/images/enterprise/add_bule.png">
                      关注
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
            <div style="width: calc(80% - 40px);">
              <div class="font-22 f-w-500">公开课</div>
              <div class="mt-30 mb-24 flex jc-between ai-center">
                <div class="font-20 f-w-400">独家策划</div>
                <!-- <div class="color-999 font-14 f-w-400 flex ai-center">共<span class="f-w-700"
                    style="color:#006EFF;margin:0 5px">688</span>次独家<div class="ml-10">更多></div>
                </div> -->
              </div>
              <div class="flex flex-wrap" :style="liveBroadcastBoxStyle">
                <div v-for="(item, index) in exclusiveList" :key="index" class="flex-col liveBroadcast-item"
                  :class="{ 'mr-20': (index + 1) % 3 != 0 }">
                  <div class="liveBroadcast-img">
                    <img :src="item.img" style="width:100%;height: 100%;">
                  </div>
                  <div class="mt-10 ellipsis2 font-16" style="min-height:40px">{{ item.name }}</div>
                  <div class="mt-10 flex ai-center jc-between">
                    <div class="flex ai-center">
                      <img style="width:24px;height:24px;border-radius: 50%;"
                        src="@/assets/newVersion/default-header.png">
                      <span class="font-14 color-999 ml-10">{{ item.user }}</span>
                    </div>
                    <!-- <hyButton width="80px" height="28px" type="primary" plain size="mini">独家策划</hyButton> -->
                  </div>
                </div>
              </div>
              <div class="mt-48 mb-24 flex jc-between ai-center">
                <div class="font-20 f-w-400">直播</div>
                <div class="color-999 font-14 f-w-400 flex ai-center" style="cursor: pointer;">
                  <div class="ml-10" @click="toLecture('/lectureHall/lectureRoom')">更多></div>
                </div>
              </div>
              <div class="flex flex-wrap" ref="liveBroadcastContent" :style="liveBroadcastBoxStyle">
                <div v-for="(item, index) in companyList" :key="index" class="flex-col liveBroadcast-item"
                  :class="{ 'mr-20': (index + 1) % 3 != 0 }">
                  <div class="liveBroadcast-img">
                    <img :src="item.logoPath" style="width:100%;height: 100%;">
                  </div>
                  <div class="mt-10 ellipsis2 font-16" style="min-height:40px" v-if="item.liveType == 1">
                    {{ item.classroomSpeaker.name }}</div>
                  <div class="mt-10 ellipsis2 font-16" style="min-height:40px" v-else>
                    {{ item.classroomEnterprise.name }}</div>
                  <div class="mt-10 flex ai-center jc-between">
                    <div class="flex ai-center" v-if="item.liveType == 1">
                      <span class="font-14 color-999">{{ item.classroomSpeaker.intro }}</span>
                    </div>
                    <div class="flex ai-center" v-else>
                      <span class="font-14 color-999">{{ item.classroomEnterprise.categoryName }}</span>
                    </div>
                    <hyButton v-if="item.liveStatus == 1" size="normal" height="28px" width="80px" plain type="primary"
                      @click="toLecture('/lectureHall/lectureRoomDetail', { liveUid: item.uid })">
                      <span style="font-size: 12px">预约观看</span>
                    </hyButton>
                    <hyButton v-if="item.liveStatus == 2" size="normal" height="28px" width="80px" plain type="primary"
                      @click="toLecture('/lectureHall/lectureRoomDetail', { liveUid: item.uid })">
                      <span style="font-size: 12px">即将开播</span>
                    </hyButton>
                    <hyButton v-if="item.liveStatus == 3" size="normal" height="28px" width="80px" plain type="primary"
                      @click="toLecture('/lectureHall/lectureRoomDetail', { liveUid: item.uid })">
                      <span style="font-size: 12px">直播中</span>
                    </hyButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div>
            <lecture-review />
            <div class="mt-50 mb-24 flex jc-between ai-center">
              <div class="font-20 f-w-400">为你推荐</div>
              <div class="color-999 font-14 f-w-400 flex ai-center" @click="toLecture('/lectureHall/lectureRoom')" style="cursor: pointer;">更多></div>
            </div>
            <div class="flex flex-wrap" ref="reviewContent" :style="boxStyle">
              <div v-for="(item, index) in recommendList" :key="index"
                @click="toLecture('/lectureHall/lectureRoomDetail',{liveUid: item.uid})" class="flex-col item-cover liveBroadcast-item"
                :class="(index + 1) % 4 != 0 ?'mr-20':''">
                <div class="liveBroadcast-img">
                  <img :src="item.logoPath" style="width:100%;height: 100%;">
                </div>
                <div class="cover-tips now font12 f-w-400 flex ai-center" style="color: #fff;">
                  <p style="margin-left: 5px;"> {{ item.isSubscribed }}人学习 </p>
                </div>
                <div class="mt-10 ellipsis2 font-16" style="min-height:40px">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import lectureReview from './components/lectureReview.vue';
  export default {
    components: {
      lectureReview
    },
    data() {
      return {
        dataList: [{

        }],
        knowledgeId: '',
        topicList: [],
        topicListTwo: [],
        reviewList: [],
        reviewTotal: 0,
        companyList: [],
        recommendList: [],
        boxStyle: '',
        liveBroadcastBoxStyle: '',
        exclusiveList: [{
          name: "从“数商”谈起一一数据科学和数据价值的创造逻辑",
          user: '谢尔曼博士',
          img: require('@/static/images/information/exclusive1.png'),
        }, {
          name: "从“规定”看核心一一数据资产化政策解析",
          user: '谢尔曼博士',
          img: require('@/static/images/information/exclusive2.png'),
        }, {
          name: "从“标准”看门道一一数据资产管理工作内容详解",
          user: '谢尔曼博士',
          img: require('@/static/images/information/exclusive3.png'),
        }],
        boxStyle: '',
        bgBoxStyle: ''
      }
    },
    async mounted() {
      await this.getData()
      this.getReviewData()
      this.getCompanyListData()
      this.getRecommendData()
      window.addEventListener('resize', this.liveBroadcastBoxsize)
      this.$nextTick(() => {
        this.liveBroadcastBoxsize()
      })
      window.addEventListener('resize', this.reviewBgResize)
      this.$nextTick(() => {
        this.reviewBgResize()
      })
      window.addEventListener('resize', this.bgResize)
      this.$nextTick(() => {
        this.bgResize()
      })
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.liveBroadcastBoxsize)
      window.removeEventListener('resize', this.reviewBgResize)
      window.removeEventListener('resize', this.bgResize)
    },
    methods: {
      liveBroadcastBoxsize() {
        let offsetWidth = this.$refs.liveBroadcastContent.offsetWidth
        this.liveBroadcastBoxStyle = '--review-item-width:' + ((offsetWidth - 41) / 3) + 'px;'
      },
      reviewBgResize() {
        let offsetWidth = this.$refs.reviewContent.offsetWidth
        this.boxStyle = '--review-item-width:' + ((offsetWidth - 61) / 4) + 'px;'
      },
      bgResize() {
        let offsetWidth = this.$refs.bgContent.offsetWidth
        this.bgBoxStyle = '--review-item-width:' + ((offsetWidth - 0) / 1) + 'px;'
      },
      toLecture(path, query = {}) {
        this.$router.push({
          path,
          query
        })
      },
      blogShowTrue(item) {
        this.knowledgeId = item.uid
      },
      blogShowFalse(item) {
        this.knowledgeId = ''
      },
      getData() {
        this.$request.lectureHall.selectBlogSortCountGroup().then(res => {
          if (res.data.code == 200) {
            this.topicList = res.data.data
            this.topicListTwo = res.data.data.filter(item => item.blogSortList && item.blogSortList.length > 0)
            if (this.topicListTwo.length > 2) {
              this.topicListTwo.length = 2
            }
            this.topicListTwo.forEach(item => {
              item.blogSortList.forEach(blog => {
                blog.show = false
              })
            })
            // console.log(this.topicListTwo)
          }
        })
      },
      getCompanyListData() {
        let obj = {
          currentPage: 1,
          pageSize: 3,
          liveStatusList: [1, 2, 3],
          name: '',
          liveType: ''
        }
        this.$request.lectureHall.getLiveList(obj).then(res => {
          if (res.data.code == 200) {
            this.companyList = res.data.data.records
          }
        })
      },
      getRecommendData() {
        let obj = {
          currentPage: 1,
          pageSize: 4,
          liveStatusList: [1, 2, 3, 4, 5],
          name: '',
          liveType: ''
        }
        this.$request.lectureHall.getLiveList(obj).then(res => {
          if (res.data.code == 200) {
            this.recommendList = res.data.data.records
          }
        })
      },
      getReviewData() {
        let obj = {
          currentPage: 1,
          pageSize: 8,
          liveStatusList: [4, 5],
          name: '',
          liveType: ''
        }
        this.$request.lectureHall.getLiveList(obj).then(res => {
          if (res.data.code == 200) {
            this.reviewList = res.data.data.records
            this.reviewTotal = res.data.data.total
          }
        })
      },
    }
  }
</script>
<style lang='scss' scoped>
  .orderer-theory-container {
    background: #fff;
    padding-bottom: 80px;
    /* .bk {
      background: url('@/static/images/information/bk.png') no-repeat;
      background-size: 100% 100%;
      height: 400px;
      width: 100%;
    } */

    .all-topics {
      font-size: 22px;
      font-family: Alibaba PuHuiTi 2.0-65 Medium, Alibaba PuHuiTi 20;
      font-weight: 500;
      color: #333333;
    }

    .all-num {
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #999999;
    }

    .topical-content2 {
      padding: 10px !important;
    }

    .topical-content {
      width: 140px;
      padding: 10px !important;
      background: #F5F8FC;
      border-radius: 4px;
      border: 1px solid #E5E6EB;
      cursor: pointer;

      .topical-content-title {
        font-size: 14px;
        font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
        font-weight: 400;
        color: #333333;

        img {
          height: 18px;
          width: 18px;
        }
      }

      .interest {
        font-size: 12px;
        font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
        font-weight: 400;
        color: #006EFF;
      }

      .content-num {
        font-size: 12px;
        font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
        font-weight: 400;
        color: #999999;
        margin: 3px 0 3px 28px;
      }
    }
  }

  .ml-28 {
    margin-left: 28px;
  }

  .review-item {
    width: calc((100% - 40px) / 3);
  }

  .review-item2 {
    width: calc((100% - 60px) / 4);
  }

  .mt-48 {
    margin-top: 48px;
  }

  .img-div {
    overflow: hidden;
    border-radius: 4px;
  }

  .item-cover {
    position: relative;

    .cover-tips {
      position: absolute;
      top: 12px;
      right: 12px;
      padding: 5px 12px;
      background: rgba(26,27,43,0.3);
      border-radius: 20px;
      font-size: 12px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #FFFFFF;
    }
  }

  .disabled {
    color: #999 !important;
    background-color: #F4F6F7 !important;
    border: 1px solid #F4F6F7 !important;
    height: 28px;
    width: 80px;
    display: flex;
    align-items: center;
    cursor: pointer !important;
  }

  .liveBroadcast-item {
    width: var(--review-item-width);

    .liveBroadcast-img {
      width: 100%;
      height: calc(var(--review-item-width) * (157 / 280)); // 高 = 宽 * 长宽比
      // aspect-ratio: 1.7834;
      object-fit: cover;
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .bk {
    background: url('@/static/images/information/bk.png') no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: calc(var(--review-item-width) * (320 / 1440)); // 高 = 宽 * 长宽比
    object-fit: cover;
  }
</style>
