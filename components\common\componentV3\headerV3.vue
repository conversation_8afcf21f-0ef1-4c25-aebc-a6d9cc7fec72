<template>
  <div class="header-container">
    <!-- 一级导航 -->
    <!-- <div class="first-menu" id="first-menu-layout">
      <div class="flex jc-center">
        <div class="w-1440 flex jc-between">
          <div style="width: (14 / 18) %">
            <ul class="left">
              <li
              :style="{color:firstNavParentUid == item.uid?'#66b1ff':''}" 
              v-for="item in menuList" @click="toPage(item.url)" :key="item.uid">
                {{ item.name }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div> -->
    <!-- 二级导航 -->
    <div class="two-menu">
      <div class="flex jc-center">
        <div class="w-1440 flex jc-between">
          <div style="width: (11 / 18) %">
            <div class="left">
              <div class="logo">
                <img
                  v-lazy="webConfig.logoPhoto"
                  v-if="webConfig.logoPhoto"
                  class="cursor"
                  @click="$router.push('/')"
                  alt=""
                />
              </div>
              <ul class="ai-center">
                <li
                  class="two-item"
                  :class="[
                    item.childWebNavbar
                      ? routeUrl.includes(
                          item.childWebNavbar.filter((item) => {
                            return item.url == routeUrl[0];
                          }).length != 0
                            ? item.childWebNavbar.filter((item) => {
                                return item.url == routeUrl[0];
                              })[0].url
                            : ''
                        )
                        ? 'active'
                        : ''
                      : routeUrl.includes(item.url)
                      ? 'active'
                      : '',
                  ]"
                  v-for="item in menuList"
                  :key="item.uid"
                >
                  <div
                    v-if="item.childWebNavbar"
                    class="child_item"
                    @mouseenter="showDropdown()"
                    @mouseleave="hideDropdown()"
                  >
                    <div class="child_tit">{{ item.name }}</div>
                    <div class="dropdown_menu" v-if="isDropdownVisible">
                      <div
                        v-for="(childItem, index) in item.childWebNavbar"
                        :key="childItem.uid"
                        
                        class="child-item"
                        :class="[
                          routeUrl.includes(childItem.url)
                            ? 'active child-item'
                            : 'child-item',
                          index + 1 == item.childWebNavbar.length
                            ? 'border-none'
                            : '',
                        ]"
                      >
                      <div
                        v-if="childItem.childWebNavbar"
                        class="child_item1"
                        @mouseenter="showDropdown1()"
                        @mouseleave="hideDropdown1()"
                      >
                      <div class="child_tit1">{{ childItem.name}}</div>
                        <div class="dropdown_menu1" v-if="isDropdownVisible1">
                          <div
                            v-for="(value, index) in childItem.childWebNavbar"
                            :key="childItem.uid"
                            
                            class="child-item1"
                            :class="[
                              routeUrl.includes(value.url)
                                ? 'active child-item1'
                                : 'child-item1',
                              index + 1 == childItem.childWebNavbar.length
                                ? 'border-none'
                                : '',
                            ]"
                          >
                            <div @click="toPage(value.url,value.isJumpExternalUrl)">{{ value.name }}</div> 
                          </div>
                        </div>
                      </div>
                        <div v-else @click="toPage(childItem.url,childItem.isJumpExternalUrl)">{{ childItem.name }}</div> 
                      </div>
                    </div>
                  </div>
                  <span class="child_tit" v-else 
                  @click="toPage(item.url,item.isJumpExternalUrl)">
                    {{ item.name }}
                  </span>
                </li>
              </ul>
            </div>
          </div>
          <div style="width: (4 / 18) %">
            <ul class="div_right---div">
              <!-- <li>
                <i class="el-icon-search" @click="toPage('/search')"></i>
              </li> -->
              <el-popover placement="bottom" trigger="hover">
                <div class="flex jc-around">
                  <div class="text-c">
                    <img
                      style="width: 175px"
                      src="@/assets/qrcode/zlinger.png"
                      alt=""
                    />
                    <div>指令者订阅号</div>
                  </div>
                  <div class="text-c">
                    <img
                      style="width: 175px"
                      src="@/assets/qrcode/huanyu.png"
                      alt=""
                    />
                    <div>指令者服务号</div>
                  </div>
                  <div class="text-c">
                    <img
                      style="width: 175px"
                      src="@/assets/qrcode/videoHuanyu.png"
                      alt=""
                    />
                    <div>指令者视频号</div>
                  </div>
                </div>
                <li slot="reference" class="pr-15">公众号</li>
              </el-popover>
              <el-popover
                class="ml-20 mr-20"
                placement="bottom"
                width="200"
                trigger="hover"
              >
                <img style="width: 175px" :src="h5Img" alt="" />
                <li slot="reference">移动端</li>
              </el-popover>
              <li>
                <div
                  v-if="$store.state.isLogin"
                  class="head-popover"
                  @mouseover="showHeader = true"
                  @mouseleave="showHeader = false"
                >
                  <el-image
                    :key="userInfo.photoUrl"
                    class="circle cursor"
                    style="width: 25px; height: 25px"
                    :src="userInfo.photoUrl || $store.state.defaultAvater"
                    fit="cover"
                  >
                    <div slot="error" style="width: 100%; height: 100%">
                      <img
                        style="width: 100%; height: 100%"
                        :src="$store.state.defaultAvater"
                      />
                    </div>
                  </el-image>
                  <transition name="el-fade-in-linear">
                    <div class="popover" v-show="showHeader">
                      <div class="flex ai-center jc-between">
                        <div class="flex ai-center">
                          <el-image
                            :key="userInfo.photoUrl"
                            class="circle cursor mr-10"
                            style="width: 48px; height: 48px"
                            :src="
                              userInfo.photoUrl || $store.state.defaultAvater
                            "
                            fit="cover"
                            @click="toLab"
                          >
                            <div slot="error" style="width: 100%; height: 100%">
                              <img
                                style="width: 100%; height: 100%"
                                :src="$store.state.defaultAvater"
                              />
                            </div>
                          </el-image>
                          <div class="title-c-1 font-16 mb-5 ml-10">
                            {{ userInfo.nickName }}
                          </div>
                        </div>
                        <div>
                          <div class="div_right---div">
                            <hyButton
                              @click="toLab"
                              height="36px"
                              width="120px"
                              type="primary"
                              size="mini"
                              >个人实验室</hyButton
                            >
                          </div>
                        </div>
                      </div>

                      <div style="display: flex; justify-content: space-around">
                        <div
                          class="flex-col ai-center action-box"
                          :key="index"
                          v-for="(i, index) in userOperates"
                        >
                          <div
                            class="inner-box mb-10 cursor mt-12"
                            @click="handleClick(i)"
                          >
                            <i
                              style="color: #006eff"
                              :class="['item-text', i.icon]"
                            ></i>
                          </div>
                          <span class="item-text font-14 title-c-2">{{
                            i.title
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </transition>
                </div>

                <div v-else @click="toPage('/login')">
                  <span>登录</span>
                  <span>/</span>
                  <span>注册</span>
                </div>
              </li>
            </ul>
          </div>
          <!-- <div style="width: (7 / 18)%">
            <div class="right-box">
              <div class="right" v-if="$route.path != '/home'">
                <hyButton @click="toLab" height="36px" width="120px" type="primary" size="mini">个人实验室</hyButton>
              </div>
            </div>
            <div class="right-box">
                  <div class="right">
                      <el-input
                      placeholder="请输入内容"
                      v-model="twoSearch" class="right-search">
                          <i slot="suffix" class="el-icon-search"></i>
                      </el-input>
                  </div>
                  <template v-if="type && type ==1">
                      <span style="margin: 0 20px;">机构/个人入驻</span>
                      <span>分销</span>
                  </template>
              </div>
          </div> -->
        </div>
      </div>
    </div>
    <!-- 三级导航 -->
    <!-- <div class="three-menu" v-if="panelHomeUrl($route.path)">
      <div style="height: 100%;" class="flex jc-center">
        <div style="height: 100%;" class="w-1440 flex">
          <el-menu :router="true" text-color="#666666" mode="horizontal">
            <deepHeaderV3 v-for="item in threeMenu" :item="item" :key="item.uid"></deepHeaderV3>
          </el-menu>
          <headerTopic>
            <template slot="left">
              <i class="el-icon-arrow-left"></i>
            </template>
            <template slot="right">
              <i class="el-icon-arrow-right"></i>
            </template>
          </headerTopic>
        </div>
      </div>
    </div> -->
  </div>
</template>
<script>
import { mapMutations } from "vuex";
import QRCode from "qrcode";

export default {
  props: {
    showDistribution: {
      type: Boolean,
      default: false,
    },
    type: {
      type: Number || String,
      default: "",
    },
  },
  data() {
    return {
      isDropdownVisible:false,
      isDropdownVisible1:false,
      currentBackMenu2: [],
      showHeader: false,
      userInfo: {},
      twoSearch: "",
      webConfig: {},
      menuList: [],
      twoMenuList: [],
      threeMenu: [],
      userOperates: [
        {
          title: "个人中心",
          icon: "iconfont icon-a-gerenxinxi11",
          path: "/userCenter/profile",
        },
        {
          title: "消息中心",
          icon: "iconfont icon-xiaoxi1",
          path: "/news/interactiveNews",
        },
        // {
        //   title: '我的订单',
        //   icon: 'icon-order',
        //   path: '/pay/orderCenter'
        // },
        // ,
        // {
        //   title: '私信',
        //   icon: 'el-icon-chat-dot-round',
        //   path: '/news/privateNews'
        // } 下拉框不用了
        {
          title: "退出登录",
          icon: "iconfont icon-tuichu",
          action: "logout",
          // path: ''
        },
      ],
      routeUrl: [],
      h5Img: "",
      h5Url: "https://m.cmdr.com.cn",
      h5Url: "https://m.cmdr.com.cn",
    };
  },
  created() {
    this.getWebNavbarList();
  },
  mounted() {
    this.getWebConfigInfo();
    this.getSystemConfigNoAuth();
    this.getQRcode();
    this.$refs.dropdownMenu = this.$el.querySelector('.dropdown_menu');
  },
  watch: {
    getUserInfo: {
      handler: function (val) {
        if (val) {
          if (Object.keys(val).length) {
            this.userInfo = val;
          }
        }
      },
      deep: true,
    },
    "$route.query": function (newVal, oldVal) {
      if (this.$route.path.indexOf("lectureHall") != -1) {
        this.routeUrl = ["/lectureHall"];
      } else if (this.$route.path.indexOf("huYuanSchool") != -1) {
        this.routeUrl = ["/huYuanSchool"];
      } else if (this.$route.path.indexOf("laboratoryNew") != -1) {
        this.routeUrl = ["/laboratoryNew/personal?speakerUid={speakerUid}"];
      } else if (this.$route.path.indexOf("footAbout") != -1) {
        this.routeUrl = ["/footAbout"];
      } else if (this.$route.path.indexOf("certification") != -1) {
        this.routeUrl = ["/certification/home/"];
      } else {
        this.routeUrl = [this.$route.path];
      }
      this.changeNav();
    },
    "$route.path": function (newVal, oldVal) {
      
      if (this.$route.path.indexOf("lectureHall") != -1) {
        this.routeUrl = ["/lectureHall"];
      } else if (this.$route.path.indexOf("huYuanSchool") != -1) {
        this.routeUrl = ["/huYuanSchool"];
      } else if (this.$route.path.indexOf("laboratoryNew") != -1) {
        this.routeUrl = ["/laboratoryNew/personal?speakerUid={speakerUid}"];
      } else if (this.$route.path.indexOf("footAbout") != -1) {
        this.routeUrl = ["/footAbout"];
      } else if (this.$route.path.indexOf("certification") != -1) {
        this.routeUrl = ["/certification/home/"];
      } else {
        this.routeUrl = [this.$route.path];
      }

      this.changeNav();
    },
  },
  computed: {
    firstNavParentUid({ twoMenuList }) {
      if (twoMenuList.length) return twoMenuList[0].parentUid;
      return "";
    },
    getNewsCount() {
      return this.$store.state.newsCount;
    },
    getUserInfo() {
      return this.$store.state.userInfo;
    },
  },
  methods: {
    showDropdown1() {
      this.isDropdownVisible1 = true;
    },
    hideDropdown1() {
      // setTimeout(() => {
      //   if (!this.$refs.dropdownMenu.contains(document.activeElement)) {
      //     this.isDropdownVisible = false;
      //   }
      // }, 200); // 延迟200毫秒以允许用户移动到下拉菜单上
    },
    showDropdown() {
      this.isDropdownVisible = true;
    },
    hideDropdown() {
      // setTimeout(() => {
      //   if (!this.$refs.dropdownMenu.contains(document.activeElement)) {
      //     this.isDropdownVisible = false;
      //   }
      // }, 200); // 延迟200毫秒以允许用户移动到下拉菜单上
    },
    ...mapMutations(["setTwoNavMenu"]),
    // 判断二级导航栏高亮
    handlerActive(item) {
      let whiles = [item.url, item.url + "/"];
      if (whiles.includes(this.$route.path)) {
        return true;
      }
      return false;
    },
    panelHomeUrl(url) {
      let reg = /\/home/<USER>
      return reg.test(url);
    },
    toPage(url,type='0') {
      if (url == "/") {
        return;
      }
      if (url.indexOf("{speakerUid}") != -1) {
        this.toLab();
        return;
      }
      if (type == '1') {
        window.open(url)
        return
      }
      this.routeUrl = [url];
      this.$router.push(url);
    },
    // 退出登录
    logout() {
      this.$axios({
        method: "post",
        url: this.$api.logout,
        params: { token: localStorage.getItem("token") },
      }).then((res) => {
        // this.$message.success('退出成功');
        this.$store.commit("changeIsLogin", false);
        this.$store.commit("changeUserInfo", null);
        this.$store.commit("clearAuthenticationInfo");
        this.$store.commit("removeCompany");

        this.$bus(this).$emit("logout");
        let arr = [
          "articleStorage",
          "store",
          "userInfo",
          "token",
          "epManagementCompanyUid",
          "enterpriseInfo",
          "userAuth",
          "epOpenParams",
          "dificult",
          "epTabs",
          "studentInfomation",
        ];
        arr.forEach((i) => {
          localStorage.removeItem(i);
        });
      });
    },
    // 点击用户操作列表项
    handleClick(item) {
      if (item.action === "logout") return this.logout();
      if (!item.path) return;
      if (item.path.indexOf("http") > -1) {
        window.open(item.path);
      } else {
        this.$router.push(item.path);
      }
    },
    changeNav() {
      let twoMenuList = [];
      this.threeMenu = [];
      let flag = false;
      this.menuList.forEach((item, itemIndex) => {
        if (item.childWebNavbar && item.childWebNavbar.length) {
          let urls = [item.url, item.url + "/"];
          let datas = item.childWebNavbar || [];
          // 对应的一级的二级目录
          if (this.$route.path.indexOf("lectureHall") != -1) {
            this.routeUrl = ["/lectureHall"];
          } else if (this.$route.path.indexOf("huYuanSchool") != -1) {
            this.routeUrl = ["/huYuanSchool"];
          } else if (this.$route.path.indexOf("laboratoryNew") != -1) {
            this.routeUrl = ["/laboratoryNew/personal?speakerUid={speakerUid}"];
          } else if (this.$route.path.indexOf("footAbout") != -1) {
            this.routeUrl = ["/footAbout"];
          } else if (this.$route.path.indexOf("certification") != -1) {
            this.routeUrl = ["/certification/home/"];
          } else {
            this.routeUrl = [this.$route.path];
          }
          if (urls.includes(this.$route.path)) {
            if (datas.length) {
              datas.forEach((i) => {
                twoMenuList.push(i);
              });
              // 表示二级已加载过
              flag = true;
            }
          }
        }
      });
      let backShowArrFre = Object.freeze(this.menuList);
      let allL = backShowArrFre.length;
      // 点击二级展示保持二级显示
      if (!flag) {
        backShowLoop: for (let index = 0; index < allL; index++) {
          if (
            backShowArrFre[index]?.childWebNavbar &&
            backShowArrFre[index].childWebNavbar.length
          ) {
            let datas = backShowArrFre[index].childWebNavbar;
            // 筛选当前二级路由且没有二级路由信息的一级路由
            if (twoMenuList.length) return;
            for (let i = 0; i < datas.length; i++) {
              if (datas[i].url === this.$route.path) {
                twoMenuList =
                  JSON.parse(
                    JSON.stringify(backShowArrFre[index].childWebNavbar)
                  ) || [];
                break backShowLoop;
              }
            }
          }
        }
      }
      this.twoMenuList = twoMenuList.length
        ? twoMenuList
        : this.$store.state.twoNavMenu;
      // 存在才覆盖
      twoMenuList.length &&
        this.setTwoNavMenu(JSON.parse(JSON.stringify(twoMenuList)));
      wrapLoop: for (let j = 0; j < allL; j++) {
        if (
          backShowArrFre[j].childWebNavbar &&
          backShowArrFre[j].childWebNavbar.length
        ) {
          let urls = [backShowArrFre[j].url, backShowArrFre[j].url + "/"];
          // 对应的一级的二级目录
          if (urls.includes(this.$route.path)) {
            let forArr = backShowArrFre[j].childWebNavbar || [];
            for (let itemIndex = 0; itemIndex < forArr.length; itemIndex++) {
              if (
                forArr[itemIndex].childWebNavbar &&
                forArr[itemIndex].childWebNavbar.length
              ) {
                // 对应的二级的三级目录
                (forArr[itemIndex].childWebNavbar || []).forEach((j) => {
                  this.threeMenu.push(j);
                });
                break wrapLoop;
              }
            }
          }
        }
      }
    },
    // 获取导航栏列表
    getWebNavbarList() {
      let params = {};
      params.isShow = 1;
      params.navbarType = 1
      this.$axios({
        method: "get",
        url: this.$api.getWebNavbar,
        params,
      }).then((res) => {
        if (res.data.code == this.$code.SUCCESS) {
          this.menuList = res?.data ? res.data.data : [];
          console.log(this.menuList);
          this.changeNav();
        }
      });
    },
    // 获取网站配置
    getWebConfigInfo() {
      this.$axios({
        method: "get",
        url: this.$api.getWebConfig,
      }).then((res) => {
        this.webConfig = res?.data ? res.data.data : {};
        this.$store.commit("setWebConfig", this.webConfig); // 设置网站配置
        this.$store.commit(
          "setLogoPhoto",
          Object.keys(this.webConfig).length && this.webConfig.logoPhoto
        ); // 设置网站logo
      });
    },

    toLecture(item) {
      this.$router.push(item);
    },
    //获取系统配置
    getSystemConfigNoAuth() {
      //获取图片地址前缀配置
      this.$axios
        .get(this.$api.getSystemConfigNoAuth)
        .then((res) => {
          localStorage.setItem("picBaseUrl", res.data.data.localPictureBaseUrl);
          this.$store.commit(
            "changePicBaseUrl",
            res.data.data.localPictureBaseUrl
          ); // 设置网站图片前缀
        })
        .catch((err) => {});
    },

    // 移动端二维码
    getQRcode() {
      if (window.location.hostname === 'test.cmdr.com.cn') {
        this.h5Url = `https://m.test.cmdr.com.cn/#/pages/home/<USER>
      } else {
        this.h5Url = `https://m.cmdr.com.cn/#/pages/home/<USER>
      }
      let opts = {
        errorCorrectionLevel: "L", //容错级别
        type: "image/png", //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 1.5, //二维码留白边距
        width: 175, //宽
        height: 175, //高
        text: this.h5Url, //二维码内容
        color: {
          dark: "#333", //前景色
          light: "#fff", //背景色
        },
      };
      //this.QRlink 生成的二维码地址url
      QRCode.toDataURL(this.h5Url, opts, (err, url) => {
        if (err) throw err;
        //将生成的二维码路径复制给data的QRImgUrl
        this.h5Img = url;
      });
    },

    toLab() {
      if (this.$store.getters.isLogin) {
        if (this.$store.state.userInfo.hasLaboratory) {
          this.$router.replace({
            path: "/laboratoryNew/personal",
            query: { speakerUid: this.$store.state.userInfo.speakerUid },
          });
          return;
        }
        this.$confirm("暂未开通个人实验室，是否开通", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$request.laboratoryNew.openLaboratory().then((res) => {
            if (res.data.code == 200) {
              const userInfo = {
                ...this.$store.state.userInfo,
                hasLaboratory: 1,
                speakerUid: res.data.data,
              };
              this.$store.commit(
                "changeUserInfo",
                JSON.parse(JSON.stringify(userInfo))
              );
              this.$router.replace({
                path: "/laboratoryNew/personal",
                query: { speakerUid: this.$store.state.userInfo.speakerUid },
              });
            }
          });
        });
      } else {
        this.$confirm("您未登录，是否立即前往登录界面?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
            this.$store.commit("setLoginAfterRoutePath", this.$route.path);
          })
          .catch((err) => {
            // 拒绝前往登录界面
          });
      }
    },
  },
};
</script>
<style lang='scss' scoped>
.header-container {
  width: 100%;

  .three-menu {
    width: 100%;
    height: 40px;
    background: #f5f8fc !important;

    /deep/ .el-menu {
      border-bottom: none;
      background: transparent;
    }

    /deep/ .is-active {
      border-bottom: transparent;
    }

    /deep/ .el-menu-item,
    /deep/ .el-submenu__title {
      border-bottom: transparent;
      height: 40px;
      line-height: 40px;

      &:hover {
        background-color: #006eff !important;
        color: white !important;

        i {
          color: white;
        }
      }
    }
  }

  .two-menu {
    background: #fff;
    font-weight: 400;
    color: #333333;
    width: 100%;
    height: 64px;
    .two-item {
    }
    .distribution {
      width: 100%;
      display: flex;
      align-items: center;
    }

    .left,
    .div_right---div {
      height: 64px;
      display: flex;
      align-items: center;

      .logo {
        margin-top: 3px;
        width: max-content;
        height: 49px;

        img {
          //   object-fit: cover;
          height: 100%;
        }
      }

      ul {
        display: flex;
        margin-left: 40px;
        li {
          cursor: pointer;
          position: relative;
          white-space: nowrap;
          margin-right: 35px;
          font-size: 16px;
          font-family: Alibaba PuHuiTi 2-55 Regular, Alibaba PuHuiTi 20;
          font-weight: 400;
          color: #333333;
        }

        li:last-of-type {
          margin-right: 0;
        }
      }
      .active {
        .child_tit{
          font-size: 20px;
          font-weight: 600;
          &::after {
            position: absolute;
            content: "";
            left: 50%;
            transform: translateX(-50%);
            bottom: -5px;
            width: 12px;
            height: 2px;
            background: #006eff;
            border-radius: 1px 1px 1px 1px;
          }
        }
        // .child_tit1{
     
        //   &::after {
        //     position: absolute;
        //     content: "";
        //     left: 50%;
        //     transform: translateX(-50%);
        //     bottom: -5px;
        //     width: 12px;
        //     height: 2px;
        //     background: #006eff;
        //     border-radius: 1px 1px 1px 1px;
        //   }
        // }
      }

      /deep/ .el-input__suffix {
        top: 6px;
      }

      /deep/ .el-input__inner {
        height: 32px;
        width: 240px;
      }
    }

    .right-box {
      justify-content: flex-end;
      display: flex;
      align-items: center;

      span {
        white-space: nowrap;
        cursor: pointer;
        font-size: 16px;
        font-family: Alibaba PuHuiTi 2-55 Regular, Alibaba PuHuiTi 20;
        font-weight: 400;
        color: #333333;
      }

      .div_right---div {
        justify-content: flex-end;

        /deep/ input {
          // background: #ffffff;
          // border: none;
        }
      }
    }
  }

  .first-menu {
    width: 100%;
    height: 32px;
    background: #1a1b2b;
    position: relative;

    .left,
    .div_right---div {
      font-size: 12px;
      height: 32px;
      align-items: center;
      list-style: none;
      color: white;
      display: flex;

      li {
        white-space: nowrap;
        margin-right: 20px;
      }

      li:last-of-type {
        margin-right: 0;
      }
    }

    .div_right---div {
      justify-content: flex-end;
    }
  }
}

ul {
  li {
    cursor: pointer;
  }
}

/deep/ .right-search {
  width: 240px;

  .el-input__inner {
    background: #f4f6f7 !important;
    border: none;
  }
}

.inner-box {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f4f6f7;
  border-radius: 4px 4px 4px 4px;
  transition: all 0.5s;
  position: relative;

  &:hover {
    background: #e5f0ff;
    color: #0e76ff;
  }
}

.head-popover {
  position: relative;

  .popover {
    position: absolute;
    top: 34px;
    background: #fff;
    z-index: 10;
    right: 0;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0px 4px 30px #15151e0d;
    width: 360px;

    > div:first-child {
      height: 75px;
      border-bottom: 1px solid #eee;
    }
  }
}
</style>
<style lang='scss'>
.child_item1 {
  color: #333;
  // min-width: 100px !important;
  // width: 140px !important;
  font-size: 16px;
  z-index: 10000000000000000 !important;
  padding: 0;
  position: relative;
  .dropdown_menu1{
    display: none;
    position: absolute;
    top: 0;
    left: 100%;
    // margin: 6px 0;
    cursor: pointer;
    text-align: justify;
    text-align-last: justify;
    text-justify: distribute-all-lines;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    padding: 5px 12px;
    background-color: #fff;
    line-height: 36px;
  }
  &:hover{
    .dropdown_menu1 {
      display: block;
    }
  }
  .border-none {
    border: none !important;
  }
  .child-item1:hover {
    color: #0e76ff;
  }
  .active {
    color: #0e76ff;
    font-weight: 600;
  }
}
.child_item {
  // min-width: 100px !important;
  // width: 140px !important;
  font-size: 16px;
  z-index: 10000000000000000 !important;
  padding: 0;
  position: relative;
  .dropdown_menu{
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    // margin: 6px 0;
    cursor: pointer;
    text-align: justify;
    text-align-last: justify;
    text-justify: distribute-all-lines;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    padding: 5px 12px;
    background-color: #fff;
    line-height: 36px;
  }
  &:hover{
    .dropdown_menu {
      display: block;
    }
  }
  .border-none {
    border: none !important;
  }
  .child-item:hover {
    color: #0e76ff;
  }
  .active {
    color: #0e76ff;
    font-weight: 600;
  }
}
.div_right---div{
  font-size: 15px;
  height: 32px;
  align-items: center;
  list-style: none;
  display: flex;

  li {
    white-space: nowrap;
    margin-right: 20px;
  }

  li:last-of-type {
    margin-right: 0;
  }
}

.div_right---div {
  justify-content: flex-end;
}
</style>
