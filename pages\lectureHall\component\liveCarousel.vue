<template>
    <div class="liveCarousel w-100 flex-col ai-center jc-center">
        <div class="swiper flex ai-center jc-between mt-35">
            <div class="left mt-30">
                <i class="el-icon-arrow-left font16 cursor" @click="moveCarousel(-1)" :disabled="atEndOfList"
                    v-show="switchShow"></i>
            </div>
            <div class="center">
                <div class="center-title font18 f-w-500">直播</div>
                <div v-if="list && list.length > 0" class="center-body mt-30">
                    <div class="live-card flex ai-center" v-for="(item, index) in list" :key="index"
                        :style="{ transform: 'translateX' + '(' + currentOffset + 'px' + ')', }">
                        <div class="info-poster">
                            <img :src="item.logoPath">
                        </div>
                        <div class="info-introduction flex-col ml-20">
                            <div class="font18 ellipsis font18" style="font-weight: 500;color: #333333;">
                                {{ item.name }}
                            </div>
                            <div class="font14 ellipsis2" style="font-weight: 400;color: #666666;margin-top: 10px;">
                                {{ item.intro }}
                            </div>
                            <div class="flex ai-center font14" style="color: #666666;margin-top: 10px;">
                                <span>直播大咖：</span>
                                <div class="ml-15 flex ai-center">
                                    <img style="width: 24px;height: 24px;border-radius: 50%;"
                                        src="~/assets/images/userCenter/huanyu-logo.png">
                                    <span style="margin-left: 15px;">
                                        {{ item.classroomSpeaker ? item.classroomSpeaker.name || '' : '' }}
                                    </span>
                                </div>
                            </div>
                            <!-- 1待开播 2即将开播 -->
                            <el-button class="button flex-center mt-15 cursor" @click="liveStatusOption(item)"
                                v-if="[1].includes(item.liveStatus)">{{ item.isSubscribed == 0 ?
                                    liveStatus[item.liveStatus] : '预约成功'
                                }}</el-button>
                            <el-button class="button flex-center mt-15 cursor" @click="liveStatusOption(item)"
                                v-if="[2].includes(item.liveStatus)">即将开播</el-button>
                            <!-- 3直播中 4结束 5回放 -->
                            <el-button class="button flex-center mt-15 cursor" @click="liveStatusOption(item)"
                                v-if="[3, 4, 5].includes(item.liveStatus)">{{ liveStatus[item.liveStatus] }}</el-button>
                        </div>
                    </div>
                </div>
                <el-empty v-else :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
            </div>
            <div class="right mt-30">
                <i class="el-icon-arrow-right font16 cursor" @click="moveCarousel(1)" :disabled="atEndOfList"
                    v-show="switchShow"></i>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'liveCarousel',
    components: {

    },
    data() {
        return {
            liveStatus: {
                1: '立即预约',
                2: '即将开播',
                3: '直播中',
                4: '已结束',
                5: '查看回放'
            },
            currentOffset: 0,
            currentIndex: 0,
            paginationFactor: 900,
            windowSize: 1, //显示个数：每页展示个数
        }
    },
    computed: {
        //判断是否是第一张/最后一张
        atEndOfList() {
            return (
                this.currentOffset <=
                this.paginationFactor * -1 * (this.list.length - this.windowSize)
            );
        },
        atHeadOfList() {
            return this.currentOffset === 0;
        }
    },
    props: {
        list: {
            type: Array,
            default() {
                return []
            }
        },
        index: {
            type: Number,
            default: 0,
        },
        switchShow: {
            type: Boolean,
            default: true,
        }
    },
    watch: {
        index: {
            handler(val) {
                this.currentIndex = val
                this.currentOffset = this.currentIndex * this.paginationFactor
            },
            immediate: true
        }
    },
    created() {

    },
    methods: {
        //直播状态行为
        liveStatusOption(item) {
            console.log('点击直播状态', item)
            this.$emit('changeCard', item)
        },
        moveCarousel(direction) {
            if (direction === 1 && !this.atEndOfList) {
                this.currentOffset -= this.paginationFactor;
                this.currentIndex++
            } else if (direction === -1 && !this.atHeadOfList) {
                this.currentOffset += this.paginationFactor;
                this.currentIndex--
            }
            this.$emit('changeIndex', this.currentIndex)
        }
    },
}
</script>

<style lang="scss" scoped>
.liveCarousel {


    .swiper {
        width: 1300px;

        .left {
            margin-right: 40px;
        }

        .right {
            margin-left: 40px;
        }

        .center {
            width: 1200px;

            .center-body {
                overflow: hidden;
                display: flex;
                flex-wrap: nowrap; // 防止卡片换行
                justify-content: flex-start; // 从左对齐卡片
                align-items: center; // 垂直居中卡片

                .live-card {
                    padding: 12px;
                    background-color: #fff;
                    width: 860px;
                    height: 204px;
                    transition: transform 0.3s ease;
                    flex-shrink: 0;

                    &:not(:first-child) {
                        margin-left: 40px;
                    }

                    &.card-transition-enter-active,
                    &.card-transition-leave-active {
                        /* 定义过渡动画的样式 */
                        transition: transform 0.3s ease;
                    }

                    &.card-transition-enter,
                    &.card-transition-leave-to {
                        /* 定义进入和离开过渡的初始和最终状态 */
                        opacity: 0;
                        transform: translateX(-100%);
                    }

                    .info-poster {
                        width: 320px;
                        height: 180px;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .info-introduction {
                        flex: 1;

                        .button {
                            width: 120px;
                            height: 36px;
                            background: #006EFF;
                            border-radius: 4px 4px 4px 4px;
                            font-size: 14px;
                            color: #fff;
                        }
                    }
                }
            }

        }

    }
}
</style>