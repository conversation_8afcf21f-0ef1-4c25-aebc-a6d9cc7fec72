<!-- 创建套餐课程第一步组件 -->
<template>
    <div class="container-one">
        <div class="container">
            <el-form :model="packageForm" :rules="rules" ref="packageForm" label-width="110px" class="demo-packageForm">
                <el-form-item label="套餐课程名称" prop="setMealName" style="width: 80%;" class="packageForm-input">
                    <el-input style="width: 56%;" v-model="packageForm.setMealName" placheolder="请输入套餐课程名称" maxlength="30"
                        show-word-limit></el-input> <span class="color-999 font-12">支持中英文、特殊字符，限制30个字符以内</span>
                </el-form-item>
                <el-form-item label="知识话题" prop="knowledgeUid" style="width: 50%;">
                    <el-cascader style="width: 100%;" v-model="packageForm.knowledgeUid" :options="knowledgeList"
                        @change="handleChange" :props="{
                            value: 'uid',
                            label: 'sortName',
                            children: 'blogSortList'
                        }"></el-cascader>
                </el-form-item>

                <el-form-item label="封面图" prop="coverImgUid" style="width: 50%;">
                    <div class="choice-img-area font-12 color-999">
                        <div class="img-div flex-col jc-center ai-center" @click="openChoice">
                            <template v-if="!packageForm.pictureUrl">
                                <i class="iconfont icon-add1 color-main"></i>
                                <span style="color: rgba(0,0,0,0.4);">选择图片</span>
                            </template>
                            <template v-else>
                                <img v-lazy="packageForm.pictureUrl" alt=""
                                    style="width: 100%;height: 100%;object-fit: fill;">
                            </template>
                        </div>

                    </div>
                </el-form-item>

                <el-form-item label="套餐简介" prop="setMealBrief">
                    <el-input placheolder="请输入课程简介" type="textarea" v-model="packageForm.setMealBrief" resize="none"
                        maxlength="150" show-word-limit :autosize="{ maxRows: 5, minRows: 5 }"></el-input>
                </el-form-item>

                <el-form-item label="套餐详情" prop="setMealDetails">
                    <wangEditor height="500px" :key="timestamp" v-model="packageForm.setMealDetails"></wangEditor>
                </el-form-item>

                <el-form-item prop="mealPriceType">
                    <template slot="label">
                        <span class="color-666">售卖方式</span>
                    </template>
                    <template>
                        <div class="color-666 font-14 flex-col" style="margin-top: 13px;">
                            <el-radio-group v-model="packageForm.mealPriceType">
                                <el-radio :label="0">免费</el-radio>
                                <el-radio :label="1">付费</el-radio>
                                <el-radio :label="2">加密</el-radio>
                                <el-radio :label="3">指定学员</el-radio>
                            </el-radio-group>
                            <template v-if="packageForm.mealPriceType == 0">

                            </template>

                            <template v-if="packageForm.mealPriceType == 1">
                                <div class="mt-20">
                                    <el-form-item prop="packagePrice">
                                        <template slot="label">
                                            <span class="color-666">商品价格</span>
                                        </template>
                                        <template>
                                            <el-input placeholder="请输入课程优惠后价格" v-model.trim="packageForm.packagePrice"
                                                style="width: 200px;">
                                            </el-input><span class="ml-5">元</span>
                                        </template>
                                    </el-form-item>

                                    <el-form-item prop="scribePrice" style="margin-top: 25px;">
                                        <template slot="label">
                                            <span class="color-666">划线价格</span>
                                        </template>
                                        <template>
                                            <el-input placeholder="请输入课程原价格" v-model.trim="packageForm.scribePrice"
                                                :disabled="!packageForm.packagePrice" style="width: 200px;">
                                            </el-input><span class="ml-5">元</span>
                                        </template>
                                    </el-form-item>
                                </div>
                            </template>

                            <template v-if="packageForm.mealPriceType == 2">
                                <div class="mt-20">
                                    <el-form-item prop="encryptionPwd">
                                        <template slot="label">
                                            <span class="color-666">密码设置</span>
                                        </template>
                                        <template>
                                            <el-input placeholder="请设置3-12位数字，字母或汉字"
                                                v-model.trim="packageForm.encryptionPwd" style="width: 270px;">
                                            </el-input>
                                        </template>
                                    </el-form-item>
                                </div>
                            </template>

                            <template v-if="packageForm.mealPriceType == 3">
                                <div class="mt-20">
                                    <span class="color-999 font-14">仅被指定学员可免费学习课程，添加指定学员请前往【学员列表】手动操作</span>
                                </div>
                            </template>

                            <div v-if="packageForm.mealPriceType != 3" class="mt-20 flex ai-center"><span
                                    class="color-666 font-14 mr-20">有效期</span>
                                <el-radio-group v-model="dateradio">
                                    <el-radio :label="0">长期有效</el-radio>
                                    <el-radio :label="1">自定义</el-radio>
                                </el-radio-group>

                                <div class="font-14 color-666 ml-10">
                                    <span>领取后</span><el-input placeholder="请输入" v-model.trim="packageForm.termDay"
                                        style="width: 200px; margin:0 5px;" :disabled="!dateradio">
                                    </el-input><span>天有效</span>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-form-item>

                <el-form-item>
                    <template slot="label">
                        <span class="color-666">上架设置</span>
                    </template>
                    <template>
                        <div class="color-666 font-14 flex-col" style="margin-top: 13px;">
                            <el-radio-group v-model="shelvesradio">
                                <el-radio :label="1">立即上架</el-radio>
                                <!-- <el-radio :label="2">定时上架</el-radio> -->
                                <el-radio :label="3">下架</el-radio>
                            </el-radio-group>
                            <!-- <template v-if="shelvesradio == 1">
                                <div class="flex-col mt-15">
                                    <span style="color: #C9CDD4;">更多设置</span>
                                    <div class="flex">
                                        <el-checkbox v-model="checked">定时下架</el-checkbox>
                                        <div class="ml-10"> <el-date-picker v-model="date1" :key="500" type="datetime"
                                                popper-class="my-package-date"
                                                placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss"
                                                :disabled="!checked">
                                            </el-date-picker></div>
                                    </div>
                                </div>
                            </template> -->
                            <!-- <template v-if="shelvesradio == 2">
                                <div class="flex-col mt-15">
                                    <div>
                                        <el-form-item prop="upTime">
                                            <template slot="label">
                                                <span class="color-666">上架时间</span>
                                            </template>
                                            <template>
                                                <el-date-picker v-model="date2" type="datetime" :key="510"
                                                    placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss"
                                                    >
                                                </el-date-picker>
                                            </template>
                                        </el-form-item>
                                    </div>
                                    <span class="mt-15" style="color: #C9CDD4;">更多设置</span>
                                    <div class="flex">
                                        <el-checkbox v-model="checked1">定时下架</el-checkbox>
                                        <div class="ml-10"> <el-date-picker v-model="date3" type="datetime"
                                                value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" :key="520"
                                                :disabled="!checked1">
                                            </el-date-picker></div>
                                    </div>
                                </div>
                            </template> -->
                            <template v-if="shelvesradio == 3">
                            </template>
                        </div>
                    </template>
                </el-form-item>

                <el-form-item>
                    <template slot="label">
                        <span class="color-666">隐藏设置</span>
                    </template>
                    <template>
                        <div class="color-666 font-14 flex-col">
                            <div class="flex ai-center"><el-checkbox v-model="packageForm.hide">隐藏</el-checkbox> <span
                                    class="color-999 font-14 ml-10">不可通过搜索或列表进行访问，仅通过链接式访问</span></div>
                        </div>
                    </template>
                </el-form-item>
            </el-form>

        </div>

        <div class="pt-20 pl-40">
            <hyButton
                :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px' }"
                size="small" width="120px" height="36px" :plain="true" @click="nextStep('packageForm')">保存并下一步</hyButton>
        </div>
        <choiceImgorVideo :choiceDialog="choiceFlag" @updateFlag="updateFlag" :type="2" @currentObj="currentImg">
        </choiceImgorVideo>
    </div>
</template>


<script>
import pick from 'lodash/pick';
import choiceImgorVideo from './choiceImgorVideo.vue'
export default {
    name: 'HuanyuCollegeNuxtWebCreatePackageStepOne',
    components: {
        choiceImgorVideo
    },
    data() {
        const validateIntro = (rules, value, callback) => {
            if (delHtml(value)) {
                callback()
            } else {
                const imgStrs = value.match(/<img.*?>/g)
                if (imgStrs && imgStrs.length) { // 内容只有图片时
                    callback()
                } else {
                    callback(new Error('请填写套餐详情'))
                }
            }

            // 过滤html代码、空格、回车 空白字符
            function delHtml(str) {
                str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
                str = str.replace(/[\r\n]/g, '')
                str = str.replace(/\s/g, '')
                str = str.replace(/&nbsp;/ig, '')
                return str
            }
        }

        const validateSellingPrice = (rules, value, callback) => {
            if (value) {
                if (value == 0 || value == 0.0 || value == 0.00) {
                    callback(new Error('价格不能为0'))
                    return
                }
                let regNum = /^\d+(.\d{1,2})?$/;
                let regCh = /[\u4e00-\u9fa5]/g;
                if (regCh.test(value)) {
                    callback(new Error('请勿输入中文'))
                    return
                }
                if (!regNum.test(value)) {
                    callback(new Error('请输入正确的格式，最多保留两位小数'))
                    return
                }
                this.$refs['packageForm'].validateField('packagePrice')
                callback()
            }
        }

        const validatePackagePrice = (rules, value, callback) => {
            if (value) {
                if (value == 0 || value == 0.0 || value == 0.00) {
                    callback(new Error('价格不能为0'))
                    return
                }
                let regNum = /^\d+(.\d{1,2})?$/;
                let regCh = /[\u4e00-\u9fa5]/g;
                if (regCh.test(value)) {
                    callback(new Error('请勿输入中文'))
                    return
                }
                if (!regNum.test(value)) {
                    callback(new Error('请输入正确的格式，最多保留两位小数'))
                    return
                }
                if (Number(value) >= Number(this.packageForm.scribePrice)) {
                    callback(new Error('商品价格不能大于或等于划线价格'))
                } else {
                    callback()
                }
            } else {
                this.packageForm.scribePrice = ''
                callback(new Error('请输入商品价格'))
                return
            }
        }
        return {
            choiceFlag: false, // 选择素材弹窗是否展示
            timestamp: '', // 防止富文本框不响应问题
            dateradio: 0, // 有效期选择 
            shelvesradio: 1, // 上下架状态
            checked: false, // 是否定时下架
            checked1: false, // 是否定时下架
            date1: '', // 立即上架时的定时下架时间
            date2: '', // 定时上架的定时上架时间
            date3: '', // 定时上架的定时下架时间
            packageForm: { // 新建套餐表单
                setMealName: '', // 套餐名称
                knowledgeUid: '', // 知识话题UID
                knowledgeName: '', // 知识话题名称
                lecturerUid: '', // 讲师UID
                lecturerName: '', // 讲师名称
                coverImgUid: '', // 封面图片UID
                setMealBrief: '', // 套餐简介
                setMealDetails: '', // 套餐详情
                mealPriceType: 0, // 套餐价格类型：0.免费；1.付费；2.加密；3.指定学员
                isLongTerm: '', // 是否长期有效：0否；1.是；2.自定义
                termDay: '', // 有效天数
                packagePrice: '', // 套餐价格
                scribePrice: '', // 划线价格
                encryptionPwd: '', // 套餐加密密码
                mealType: '', // 套餐类型:1.畅销套餐；2.精选套餐；3.人气套餐；4.尊享套餐；5.高薪套餐
                isRelease: '', // 是否立即发布：0.否；1.是；2.定时发布
                releaseAtTime: '', // 定时发布时间
                hide: false, // 隐藏设置：0.否；1.是
            },
            rules: {
                setMealName: [{ required: true, message: '请输入套餐名称', trigger: ['change', 'blur'] }],
                knowledgeUid: [{ required: true, message: '请选择知识话题', trigger: ['change', 'blur'] }],
                coverImgUid: [{ required: true, message: '请选择封面', trigger: ['change', 'blur'] }],
                setMealBrief: [{ required: true, message: '请填写套餐简介', trigger: ['change', 'blur'] }],
                setMealDetails: [
                    { required: true, validator: validateIntro, trigger: ['change', 'blur'] },
                ],
                mealPriceType: [
                    { required: true, message: '请填写售卖', trigger: ['change', 'blur'] }
                ],
                packagePrice: [
                    { required: true, validator: validatePackagePrice, trigger: ['change', 'blur'] },
                ],
                scribePrice: [
                    { validator: validateSellingPrice, trigger: ['change', 'blur'] },
                ],
                encryptionPwd: [
                    { required: true, message: '请输入密码', trigger: ['change', 'blur'] }
                ],
            },
            knowledgeList: [],//知识话题
        }
    },
    watch: {
        'packageForm.mealPriceType': {
            handler: function (val) {
                let formName = 'packageForm'
                if (val == 0 || val == 3) {
                    this.packageForm.sellingPrice = ''
                    this.packageForm.password = ''
                    this.$refs[formName].clearValidate('sellingPrice')
                    this.$refs[formName].clearValidate('password')
                } else if (val == 1) {
                    this.packageForm.password = ''
                    this.$refs[formName].clearValidate('password')
                } else if (val == 2) {
                    this.packageForm.sellingPrice = ''
                    this.$refs[formName].clearValidate('sellingPrice')
                }
            },
            deep: true
        },
        'shelvesradio': {
            handler: function (val) {
                if (val == 1 || val == 3) {
                    this.date2 = ''
                    this.date3 = ''
                    this.checked1 = false
                } else if (val == 2) {
                    this.checked = false
                }
            },
            deep: true
        },
        // 'courseDetail': {
        //   handler: function (val) {
        //     if (Object.keys(val).length) {
        //       const data = this.$deepCopy(val)
        //       this.packageForm = data
        //       this.shelvesradio = data.shelvesradio
        //       this.firstFrame = data.firstFrame
        //       this.checked = Boolean(data.date1)
        //       this.checked1 = Boolean(data.date3)
        //       this.date1 = data.date1
        //       this.date2 = data.date2
        //       this.date3 = data.date3
        //     }
        //   },
        //   deep: true
        // },
        'checked': {
            handler: function (val) {
                if (!val) {
                    this.date1 = ''
                }
            },
            deep: true
        },
        'checked1': {
            handler: function (val) {
                if (!val) {
                    this.date3 = ''
                }
            },
            deep: true
        }
    },
    mounted() {
        const packageUid = this.$store.state.myCreateCoursePackageCurrentUid || ''
        if (packageUid) {
            this.getCourseDetail(packageUid)
        }
        this.getSelectList()
    },
    methods: {
        // 保存并下一步
        nextStep(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 防抖
                    this.$debounce(() => {
                        const data = JSON.parse(JSON.stringify(this.packageForm))
                        data.lecturerUid = this.$store.state.userInfo && this.$store.state.userInfo.speakerUid || ''
                        data.lecturerName = this.$store.state.userInfo && this.$store.state.userInfo.realName || ''
                        data.hide = data.hide ? 1 : 0
                        // data.isRelease = this.shelvesradio == 1 ? 1 : this.shelvesradio == 2 ? 0 : 2
                        // data.releaseAtTime = this.date1
                        // data.isLongTerm = this.dateradio == 0 ? 1 : 0
                        data.setMealDetails = encodeURIComponent(data.setMealDetails)
                        data.status = this.shelvesradio == 3 ? 1 : 0
                        data.upRelease = this.shelvesradio == 1 ? '0' : this.shelvesradio == 3 ? '0' : '1'
                        data.downRelease = this.checked || this.checked1 ? '1' : '0'
                        data.downTime = this.shelvesradio == 1 ? this.date1 : this.shelvesradio == 2 ? this.date3 : ''
                        data.upTime = this.shelvesradio == 2 ? this.date2 : ''
                        data.termDay = this.packageForm.mealPriceType == 3 ? 0 : data.termDay
                        data.isLongTerm = this.packageForm.mealPriceType == 3 ? 1 : this.dateradio == 0 ? 1 : 0
                        data.status = this.shelvesradio == 1 ? '1' : this.shelvesradio == 3 ? '0' : ''
                        const submitFun = this.$store.state.myCreateCoursePackageCurrentUid ? 'updatePackageCourseByUid' : 'myCreateAddSetMeal'
                        this.$request.myCourse[submitFun](data).then((res) => {
                            if (res.data.code == this.$code.SUCCESS || res.data.code == 0) {
                                this.$Elemessage('success', '保存成功')
                                this.$store.commit('setMyCreateCoursePackageCurrentUid', res.data.data)
                                this.$store.commit('setMyCreateCoursePackageStep', 'createPackageTwo')
                            }
                        }).catch((err) => {
                            this.$alert(err.data.message, "提示", {
                                confirmButtonText: "确定",
                                showClose: false,
                            });
                        })
                    }, 1000)();
                }
            })
        },
        // 打开素材中心弹窗
        openChoice() {
            this.choiceFlag = true
        },
        updateFlag(value) {
            this.choiceFlag = value
        },
        // 选中的当前图片
        currentImg(img) {
            if (img) {
                const { fileUrl, fileUid } = img
                this.packageForm.pictureUrl = fileUrl
                this.packageForm.coverImgUid = fileUid
                this.$refs['packageForm'].clearValidate('coverImgUid')
            }
        },
        getBaseUrl() {
            return (localStorage.getItem('picBaseUrl'))
        },
        //获取知识话题
        async getSelectList() {
            const res2 = await this.$axios.get(this.$api.getSelectBlogTopic);
            if (res2.data.code == 200) {
                //过滤掉没有二级分类的
                this.knowledgeList = res2.data.data.filter(option => option.blogSortList && option.blogSortList.length > 0);
            }
        },
        //知识体系二级选择
        handleChange(e) {
            // console.log('选择知识体系', e)
            const parent = this.knowledgeList.find((item) => item.uid == e[0])
            const child = parent && parent.blogSortList.find((item) => item.uid == e[1])
            this.packageForm.knowledgeUid = e[e.length - 1]
            this.packageForm.knowledgeName = `${parent && parent.sortName}/${child && child.sortName}`
        },
        // 获取课程详情
        getCourseDetail(setMealUid) {
            const params = { setMealUid }
            this.$request.myCourse.getPackageDetailByUid(params).then((res) => {
                if (res.data.code == this.$code.SUCCESS || res.data.code == 0) {
                    let formData = pick(res.data.data, ["uid", "setMealDetails", "hide", "setMealName", "knowledgeUid", "coverImgUid", "mealPriceType", "scribePrice", "packagePrice", "encryptionPwd", "termDay", "pictureUrl", "setMealBrief"])
                    const { status, validTime, manualState,upRelease,downRelease,downTime,upTime,hide,termDay,isLongTerm } = res.data.data
                    if (formData.hide == 0) {
                        this.$set(formData , 'hide',false)
                    } else if (formData.hide == 1) {
                        this.$set(formData , 'hide',true)
                    }
                    this.packageForm = JSON.parse(JSON.stringify(formData))
                     this.shelvesradio = status == 2 ? 3 : 1
                    if (upRelease == 1) {
                        this.shelvesradio = 2
                        this.date2 = upTime
                    }
                    if(downRelease == 1) {
                        if(this.shelvesradio == 1) {
                            this.checked = true
                            this.date1 = downTime
                        } else if(this.shelvesradio == 2) {
                            // this.checked1 = trueisLongTerm
                            this.date3 = downTime
                        }
                    }
                    this.dateradio = isLongTerm == 1 ? 0 : 1
                    this.timestamp = new Date().getTime()
                }
            })
        },
        // packageTimeFocus(type) {
        //     const target = `date${type}`
        //     this.$nextTick(() => {
        //         const ele = document.querySelector('.el-picker-panel__footer')
        //         if (ele) {
        //             const btnTxt = ele.querySelector('.el-button--text')
        //             if (btnTxt) {
        //                 console.log('target', target)
        //                 btnTxt.innerHTML = '清空'
        //                 btnTxt.addEventListener('click', () => {
        //                     this[target] = ''
        //                 })
        //             }
        //         }
        //     })
        // },
    }
}

</script>

<style lang="scss" scoped>
.container-one {
    .container {

        .demo-packageForm {
            /deep/ .el-form-item__label {
                color: $color666;
            }

            /deep/ .el-radio__label {
                color: $color666;
            }

            .packageForm-input {
                /deep/ .el-input__inner {
                    padding: 0 50px 0 15px;
                }
            }
        }
    }

    .choice-img-area {
        .img-div {
            width: 150px;
            height: 150px;
            border: 1px dashed #D4DBE0;
            box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
            border-radius: 4px;
            line-height: 18px;
            cursor: pointer;
        }

        .img-div:hover {
            border-color: $colorMain;
        }
    }
}
</style>