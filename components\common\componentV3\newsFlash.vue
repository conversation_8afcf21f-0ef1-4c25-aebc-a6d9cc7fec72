<template>
  <div class="news-flash-container">
    <div class="title-head">
      <div class="left-info">
        <!-- <div class="icon">
                </div> -->
        <span>我的实验室</span>
      </div>
      <div
        class="font-14 color-999 f-w-400 flex ai-center"
        style="cursor: pointer"
        @click="changeData"
      >
        <i class="iconfont icon-a-shuaxin1"></i>
        <span style="margin-left: 4px">换一组</span>
      </div>
    </div>
    <div class="content-box">
      <template v-if="liveList.length">
        <div
          class="item"
          @click="toToPage(item)"
          v-for="(item, index) in liveList"
          :key="index"
        >
        <div style="height: 62px; margin-right: 20px; min-width: 90px; max-width:90px;">
            <img style="height: 100%;width: 100%;" :src="item.logoUrl" alt="">
          </div>
        <div>
          <p class="title">{{ item.name }}</p>
          <div class="info">
            <p>{{ item.type | typeLabelFilter }}</p>
            <div class="info-hr"></div>
            <span> 来自 {{ item.creator == '【社区】超级管理员' ? '小宇' : item.creator }} </span>
          </div>
        </div>
        </div>
      </template>
      <el-empty
        v-else
        :image="require('@/assets/images/certification/empty.png')"
        description="暂无数据"
      ></el-empty>
    </div>
  </div>
</template>
<script>
export default {
  inject: ["homeThis"],
  filters: {
    typeLabelFilter(type) {
      switch (type) {
        case 1:
          return "讲堂";
        case 2:
          return "课程";
        case 3:
          return "报告";
        case 4:
          return "套餐";
      }
    },
  },
  props: {
    liveList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      contentList: [
        {
          type: 1,
          title: "T3出行CEO宣布:将推出自动驾驶“多核模式”加快未来出行过渡",
          auth: "马老师",
        },
        {
          type: 1,
          title: "类脑芯片植入，马斯克到底想干什么?",
          auth: "梁老师",
        },
        {
          type: 2,
          title: "2030年中国运营的自动驾驶车辆将达三千万辆",
          auth: "自动驾驶",
        },
        {
          type: 2,
          title: "云原生K8s管理员认证课-零基础免费学",
          auth: "AI职场赋能计划",
        },
      ],
    };
  },
  created() {},
  methods: {
    // 换一组
    changeData() {
      this.homeThis.getLiveList();
    },
    toToPage(data) {
      switch (data.type) {
        case 1:
          this.$router.push({
            path: "/lectureHall/lectureRoomDetail",
            query: {
              liveUid: data.uid,
            },
          });
          return "讲堂";
        case 2:
          this.$router.push({
            path: "/huYuanSchool/computerDetail",
            query: {
              uid: data.uid,
            },
          });
          return "课程";
        case 3:
          this.$router.push({
            path: "/information/report",
            query: {
              uid: data.uid,
            },
          });
          return "报告";
        case 4:
          // this.$router.push({
          //     path: '/information/report',
          //     query: {
          //         uid: data.uid
          //     }
          // })
          return "套餐";
      }
    },
  },
};
</script>
<style lang='scss' scoped>
.news-flash-container {
  padding: 0 30px 0 12px;
  margin-top: 26px;
  width: 100%;
  border-left: 1px solid #e6eaed;
  .content-box {
    height: 250px;
    overflow: hidden;
    overflow-y: auto;
    // 滚动条整体样式
    &::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
      /* 纵向滚动条 宽度 */
      border-radius: 5px;
      /* 整体 圆角 */
    }

    //轨道部分
    &::-webkit-scrollbar-track {
      background: #fff !important;
    }

    // 滑块部分
    &::-webkit-scrollbar-thumb {
      background: #d4dbe0;
      min-height: 167px;
      width: 2px !important;
      border-radius: 5px;
    }

    scrollbar-width: thin !important;
    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    -ms-overflow-style: none !important;
    /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
    .item {
      cursor: pointer;
      margin-top: 20px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      &:hover {
        color: #006eff;
      }
      .title {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden; //溢出内容隐藏
        text-overflow: ellipsis; //文本溢出部分用省略号表示
        display: -webkit-box; //特别显示模式
        -webkit-line-clamp: 2; //行数
        line-clamp: 2;
        -webkit-box-orient: vertical; //盒子中内容竖直排列
      }
      .info {
        margin-top: 9px;
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        color: #999999;
        .info-hr {
          width: 1px;
          height: 12px;
          background: #e6eaed;
          border-radius: 1px 1px 1px 1px;
          margin: 0 8px;
        }
        p {
          height: 20px;
          border-radius: 1px 1px 1px 1px;
          line-height: 20px;
          font-size: 12px;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
          font-weight: 400;
          color: #8d79ff;
        }
      }
    }
  }
  .title-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* border-bottom: 1px solid #E5E5E5; */
    padding-bottom: 10px;
    .left-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 18px;
      font-weight: 600;
      font-family: Alibaba PuHuiTi 2-65 Medium, Alibaba PuHuiTi 20;
      color: #333333;
      .icon {
        width: 32px;
        height: 32px;
        margin-right: 6px;
        background: url("https://cdn.wuzhiing.cn/hyfile/personal.png") no-repeat
          no-repeat center center;
        background-size: cover;
      }
    }
    p {
      font-size: 18px;
      font-weight: 600;
      font-family: Alibaba PuHuiTi 2-65 Medium, Alibaba PuHuiTi 20;
      color: #333333;
    }
    i {
      // color: #006EFF;
    }
  }
}
</style>