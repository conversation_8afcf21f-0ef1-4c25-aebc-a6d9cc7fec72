<template>
  <div class="pushCourseSection">
    <div class="title font-bold">认证类课程</div>
    <div v-if="navList.length" class="title-wrap flex-row ai=center mb-5">
      <div class="flex-row ai-center font-14">
        <span
          class="nav-item cursor mr-20"
          :class="navIndex == item.uid ? 'active' : ''"
          v-for="(item, index) in navList"
          :key="index"
          @click="navClick(item,index)"
          >{{ item.shortName }}</span>
      </div>
      <div class="cursor" style="margin-left: auto; color: #999;fontSize:12px;" @click="toMore">
        <span>更多</span>
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <div class="head-class" v-if="navList.length">
      <client-only>
        <vue-seamless-scroll
        :key="seamlessScroll"
        :data="navList[navCertIndex].classList"
        :class-option="classOption"
        class="warp"
        >
        <div class="class-box">
          <div class="left-class"
           :style="{
            marginRight:(index+1)%2!=0?'28px':'0px'
           }"
           v-for="(item,index) in navList[navCertIndex].classList" :key="item.uid">
              <div class="cover">
                <img v-lazy="item.fileUrl" v-if="item.fileUrl" srcset="">
                <img v-else v-lazy="require('@/static/images/emptyImg.jpg')"  alt="" srcset="">
              </div>
              <div class="right-txt">
                <div class="top">
                  <div class="row-title">
                    <div class="title">
                      <span>{{ item.name }}</span>
                    </div>
                    <div class="status-box">
                      <span>报名中</span>
                    </div>
                  </div>
                  <!-- <div class="sign-time">
                          <span>报名时间</span>
                          <span>2022.09.10-2022.10.11</span>
                        </div> -->
                  <div class="training-time">
                    <span>培训时间</span>
                    <span>{{ $formatDate(new Date(item.trainingStartTime), 'YYYY-mm-dd') }}-{{ $formatDate(new Date(item.trainingEndTime),
                      'YYYY-mm-dd') }}</span>
                  </div>
                  <div class="training-address">
                    <span>培训地点</span>
                    <span>{{ item.trainingPlass }}</span>
                  </div>
                </div>
                <div class="bottom">
                  <div class="price-box">
                    <div class="price">
                      <template v-if="item.sellWay==0">
                        <span>免费</span>
                      </template>
                      <template v-else>
                        <span>￥</span>
                        <span>{{ item.trainingPrice }}</span>
                      </template>
                    </div>
                    <el-button type="primary" @click="onlineSign(item)">在线报名</el-button>
                  </div>
                </div>
              </div>
            </div>
            <div class="left-class" v-if="navList[navCertIndex].classList && navList[navCertIndex].classList.length % 2!=0">
              <div class="cover">
                <img src="/images/courseReportEmpty.png" alt="" srcset="">
              </div>
              <div class="right-txt">
                <div class="top">
                  <div class="row-title">
                    <div class="title">
                      <span>更多认证培训类课程敬请期待</span>
                    </div>
                  </div>
                  <!-- <div class="sign-time">
                          <span>报名时间</span>
                          <span>2022.09.10-2022.10.11</span>
                        </div> -->
                  <div class="training-time">
                    <span>培训时间</span>
                    <span>-</span>
                  </div>
                  <div class="training-address">
                    <span>培训地点</span>
                    <span>-</span>
                  </div>
                </div>
                <div class="bottom">
                  <div class="price-box">
                    <div class="price">
                      <span>￥</span>
                      <span>-</span>
                    </div>
                    <el-button class="waiting-btn" disabled type="info" @click="onlineSign(item)">敬请期待</el-button>
                  </div>
                </div>
              </div>
            </div>
        </div>
          <template slot="left-switch">
            <div class="left-box">
              <div class="left-switch">
                <i class="el-icon-arrow-left"></i>
              </div>
            </div>
          </template>
          <template slot="right-switch">
            <div class="right-box">
              <div class="right-switch">
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </template>
        </vue-seamless-scroll>
      </client-only>
    </div>
    <div v-if="showXiaoe" class="course-wrap">
      <div
        v-if="selectList&&selectList.length"
        class="course-list flex-row flex-wrap font-14"
      >
        <div
          :class="[
            'course-item',
            'cursor',
            (index + 1) % 3 == 0 ? 'item-3' : '',
          ]"
          :style="{marginLeft:index/4===(Math.floor(index/4))%Math.round(selectList.length/4)?'0':'26px'}"
          v-for="(item, index) in selectList"
          :key="index"
          @click="toDetail(item)"
        >
          <img v-lazy="item.img_url" class="course-img radius6" :src="item.img_url" />
          <div class="course-bottom">
            <!-- 免费图标 -->
            <div v-if="item.price == 0" class="free-icon"></div>
            <div class="cb-title ellipsis">
              {{ item.title }}
            </div>
            <!-- <div class="mt-10 font-12" style="color: #999;">
              <span class="mr-10">{{ item.second }}</span>
              <span>{{ item.user }}</span>
            </div> -->
              <div class="introduction">
                <span class="introduction-item">{{
                  item.finished_state == 0 ? "未完结" : "已完结"
                }}</span>
                <span class="step">|</span>
                <span class="introduction-item">{{ item.tag_name }}</span>
              </div>
            <!-- <div class="introduction-price" style="color: #999;"> -->
              <!-- <span class="mr-10" style="color: #ce4c28; font-size: 14px;">{{
                item.sourceType
              }}</span> -->
                <!-- <span style="color: #FF564D;font-size:14px;">¥</span>
                <span
                  class="introduction-item"
                  style="color: #FF564D; font-size: 22px"
                >
                  {{ `${item.price / 100}.00` }}
                </span> -->
              <!-- <span>{{ item.view_count }}次浏览</span> -->
            <!-- </div> -->
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无数据" :image-size="100"></el-empty>
    </div>
  </div>
</template>
<script>
export default {
  name: "pushCourse",
  props: {
    showXiaoe: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      seamlessScroll:'',
      classOption: {
        switchOffset:0,
        navigation: true,
        switchSingleStep: 1200,
        direction: 3,
      },
      navCertIndex:0,
      navIndex: "",
      navList: [
        { name: "工业互联网", id: "1" },
        { name: "能源电力", id: "2" },
      ],
      selectList: [],
      limit: {
        index: 1,
        limit: 5,
      },
      certForm: {
        limit: 6
      },
      params: {
        currentPage: 1,
        pageSize: 4,
        id: '10503104',
      },
    };
  },
  mounted() { 
    // this.getCourseTypes();
    this.getCourseList();
    this.initIndexCerList();
  },
  computed: {
    // 课程类别
    picBaseUrl() {
      return this.$store.state.picBaseUrl;
    },
  },
  methods: {
    onlineSign(item) {
      // this.$message.warning(this.$store.state.warnMap['wn-2']);
      // return;
      if (!this.$store.getters.isLogin) {
                this.$confirm("您还未登录，请先登录后再报名?", "提示", {
                    confirmButtonText: "前往登录",
                    cancelButtonText: "再逛逛",
                    type: "warning",
                })
                    .then(() => {
                        this.$router.push("/login");
                    })
                    .catch(() => {
                        // 拒绝前往登录界面
                        return;
                    });
            } else {
              this.$throttle(() => {
              this.$store.commit('setCourseBreadcrumb', [{ label: '首页' }]);
              this.$router.push('/certification/myTraining?uid=' + item.uid)
            })();
            }
    },
    // 获取课程类别
    // getCourseTypes() {
    //   this.$axios({
    //     method: "post",
    //     url: this.$api.getHomeCourseList,
    //     data:{
    //       currentPage: 1,
    //       pageSize: 10
    //     }
    //   }).then((res) => {
    //     let data= res?.data?.data?.data;
    //     if(data){
    //       this.navList = data.list;
    //       this.navIndex = this.navList[0].id;
    //       this.params.id = this.navList[0].id;
    //     }
    //   });
    // },
    // 获取证书及培训班列表
    initIndexCerList() { 
      this.$axios({
        method: "post",
        url: this.$api.getIndexCerList,
        data: this.certForm,
      }).then((res) => {
        if (res?.data.code === this.$code.SUCCESS) { 
          this.navList = res.data.data;
          if (this.navList.length) { 
            this.navIndex = this.navList[0].uid;
          }
        }
        // console.log("cert", res);
      });
    },
    // 获取课程列表
    getCourseList() {
      this.$axios({
        method: "post",
        url: this.$api.queryCourseList,
        data: this.params,
      }).then((res) => {
        if (res?.data.data) {
          this.selectList = res.data.data?.data?.resources?.data;
        }
      });
    },
    navClick(item,index) {
      this.navIndex = item.uid;
      this.navCertIndex = index;
      this.seamlessScroll = new Date().getTime();
    },
    toMore() {
      // window.open("https://hyxy.powerun.cn/all/10256599?navIndex=2");
      this.$router.push({ path: "/certification/home", query: { isClass:true }})
    },
    toDetail(item) {
      window.open(
        `https://hyxy.powerun.cn/detail/${item.resource_id}/${item.type}`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.pushCourseSection {
  width: 1200px;
  margin: 0 auto;
  margin-top: 64px;
  min-height: max-content;
  .title{
    font-size: 18px;
    font-weight: bold;
    color: #333333;
  }
  .title-wrap {
    position: relative;
    padding-bottom: 4px;
    margin-top: 20px;
    .nav-item {
      color: #999;
    }
    .nav-item.active {
      color: $colorMain;
    }
  }
    .head-class {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 auto;
      margin-top: 20px;
      .warp {
        width: 1200px;
        overflow: hidden;
        .class-box{
          display: flex;
        }
        .left-box,.right-box{
          height: 194px;
          width: 110px;
          display: none;
          align-items: center;
          justify-content: center;
        }
        .left-switch,.right-switch{
          font-size: 12px;
          display: flex;
          align-items: center;
          color: #006EFF;
          justify-content: center;
          width: 32px;
          height: 32px;
          background: #FFFFFF;
          box-shadow: 0px 0px 5px 0px rgba(26,27,43,0.05);
          border-radius: 20px 20px 20px 20px;
        }
      }
      &:hover{
        .left-box,.right-box{
          display: flex;
        }
      }
      .left-class {
        flex-shrink: 0;
        display: flex;
        border-radius: 6px;
        padding: 16px;
        width: 586px;
        height: 194px;
        background: #FFFFFF;
        &:first-of-type{
          margin-left: 0; 
        }
        .cover{
          width: 232px;
          height: 162px;
          overflow: hidden;
          border-radius: 6px;
          img{
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .right-txt{
          flex: 1;
          margin-left: 20px;
          flex-direction: column;
          justify-content: space-between;
          .sign-time{
            font-size: 12px;
            color: #999999;
            margin-top: 12px;
            span:last-child{
              margin-left: 16px;
            }
          }
          .training-time{
            font-size: 12px;
            color: #999999;
            margin-top: 12px;
            span:last-child {
              margin-left: 16px;
              margin-top: 12px;
            }
          }
          .training-address{
            font-size: 12px;
            color: #999999;
            margin-top: 12px;
            span:last-child {
              margin-left: 16px;
            }
          }
          .price-box{
            margin-top: 48px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 36px;
            .waiting-btn{
              background: #F4F6F7;
              border-color: #F4F6F7;
              color: #BDC7CF;
            }
            .el-select{
              width: 80px;
              height: 36px;
            }
            .price{
              span:first-child{
                font-size: 14px;
                color: #FF564D;
              }
              span:last-child{
                font-size: 22px;
                color: #FF564D;
              }
            }
          }
          .row-title{
            display: flex;
            flex: 1;
            align-items: center;
            .title{
              margin-bottom: 0;
              max-width: 220px;
              text-overflow: ellipsis;
              word-break: break-all;
              overflow: hidden;
              white-space: nowrap;
            }
            .status-box{
              margin-left: 8px;
              width: 56px;
              height: 22px;
              background: #e5f0ff;
              border-radius: 4px 4px 4px 4px;
              font-size: 12px;
              color: #006EFF;
              text-align: center;
              line-height: 22px;
            }
          }
        }
      }
    }
  .course-wrap {
    .course-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      .course-item {
        margin-left: 20px;
        border-radius: 6px 6px 6px 6px;
        overflow: hidden;
        margin-top: 20px;
        background: #FFFFFF;
        width: 280px;
        height: 326px;
        .course-img {
          width: 100%;
          height: 210px;
          transition: opacity 0.5s;
        }
        .course-bottom{
          position: relative;
          padding: 16px;
          .free-icon{
            width: 40px;
            height: 28px;
            background: url('@/assets/images/home/<USER>') no-repeat;
            background-size:contain;
            position: absolute;
            top: 0;
            left: -2px;
            top: -40px;
          }
        }
      }
      .course-item:hover{
        .course-bottom{
          .cb-title{
            color: #006EFF;
          }
        }

        .course-img{
          opacity: 0.7;
        }
      }
      .course-item.item-3 {
        margin-right: 0;
      }
    }
  }

  .introduction {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 12px;
}
.introduction-price{
  display: flex;
  margin-top: 8px;
  margin-bottom: 12px;
  span:first-of-type{
    padding-bottom: 2px;
    align-self: flex-end;
  }
  span:last-of-type{
    margin-left: 4px;
  }
}

  .introduction-item {
  font-size: 12px;
  color: #999999;
}
.step{
  margin: 0 8px;
  display: inline-block;
  height: 9px;
  line-height: 9px;
  width: auto;
  color: #D8D8D8;
  overflow: hidden;
}
}
</style>
