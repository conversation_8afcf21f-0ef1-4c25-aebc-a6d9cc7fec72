<template>
    <div class="courseVideoDialog">
        <el-dialog title="选择课程" :visible.sync="show" width="1000px" height="700px" @close="closeDialog">
            <template>
                <div class="content w-100">
                    <div class="content-top w-100 flex ai-start jc-end">
                        <el-input v-model="searchForm.curriculumName" placeholder="名称"
                            style="width: 232px;margin-right: 20px;" @change="searchData"></el-input>
                        <el-button class="search" type="primary" style="background: #006EFF;color: #fff;"
                            @click="searchData">查询</el-button>
                    </div>
                    <div class="content-table w-100">
                        <el-table :header-cell-style="{
                            width: '100100%',
                            backgroundColor: '#F4F6F7',
                            color: '#666666',
                            fontSize: '14px',
                            fontWeight: '400',
                        }" ref="elTable" :data="tableData" style="width: 100%"
                            @selection-change="handleSelectionChange" @row-click="rowclick">
                            <el-table-column type="selection" width="60">
                            </el-table-column>
                            <el-table-column type="index" :index="indexMethod" label="序号" width="100" align="center">
                            </el-table-column>
                            <el-table-column label="名称" width="650" align="left">
                                <template slot-scope="scope">
                                    <div class="flex ai-center jc-start">
                                        <el-image style="width: 120px; height: 80px"
                                            :src="scope.row.pictureUrl">
                                        </el-image>
                                        <div class="flex-col" style="margin-left: 15px;">
                                            <p class="font14" style="font-weight: 400;color: #333333;">{{
                                                scope.row.curriculumName }}</p>
                                            <p class="font-12" style="margin-top: 5px;color: #FF564D;">{{ scope.row.sellWay
                                                == 0
                                                ? '免费' :
                                                scope.row.sellWay == 1 ?
                                                    `￥${scope.row.sellingPrice}` : scope.row.sellWay == 2 ? '加密' : '指定学员' }}</p>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="name" label="上架状态" align="center">
                                <template slot-scope="scope">
                                    <div class="w-100 flex-center">
                                        <span class="course-status font-14" :class="`course-status-${scope.row.status}`">{{
                                            scope.row.status == 0 ?
                                            '已下架' : scope.row.status == 1 ? '已上架' : '待上架' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </template>
            <template slot="footer">
                <div class=" flex ai-center jc-between mt-10">
                    <span class="font-14 color-999">{{ `每页 ${searchForm.size} 条, 共 ${total} 条数据
                                            已选${getAllSelectLength()}/50` }}</span>
                    <el-pagination background layout="prev, pager, next" :total="total" :current-page="searchForm.current"
                        :page-size="searchForm.size" @current-change="handleCurrentChange">
                    </el-pagination>
                </div>
                <div class="dialog-bottom flex jc-end mt-20">
                    <el-button type="primary" class="cancel flex-center" @click="closeDialog">取消</el-button>
                    <el-button type="success" class="success flex-center" @click="confirmDialog">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import TemplateSlot from '../../../../components/privates/hyMeeting/templateSlot.vue'
export default {
    name: '',
    components: {
        TemplateSlot

    },
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tableData: [],
            totalSelect: {},
            selectList: [],
            loading: false,
            total: 0,
            searchForm: {
                curriculumName: '',
                size: 4,
                current: 1,
                curriculumType: '',
            }
        }
    },
    computed: {

    },
    mounted() {

    },
    methods: {

        //重置请求
        resetList() {
            this.searchForm.current = 1
            this.searchForm.title = ''
            this.selectList = []
            this.totalSelect = {}
            this.getMyCourseList()
        },
        //保存弹窗
        confirmDialog() {
            let list = []
            for (const key in this.totalSelect) {
                list = [...list, ...this.totalSelect[key]]
            }
            if (list == 0) {
                this.$message({
                    message: '请选择课程',
                    type: 'warning'
                })
                return
            }
            const ids = []
            list.forEach(item => {
                ids.push({
                    bcUid: item,
                    type: 1
                })
            })
            // console.log('确定', ids)
            this.$emit('confirm', ids)
        },
        indexMethod(i) {
            return i + 1 + this.searchForm.size * (this.searchForm.current - 1)
        },
        //关闭 弹窗
        closeDialog(e) {
            console.log('关闭弹窗', e)
            this.$emit('close')
        },
        searchData() {
            this.searchForm.current = 1
            this.selectList = []
            this.totalSelect = {}
            this.getMyCourseList()
        },
        rowclick(row) {
            // console.log('rowclick', e)
            this.$refs.elTable.toggleRowSelection(row);
        },
        handleSelectionChange(val) {
            console.log('handleSelectionChange', val)
            this.selectList = []
            if (this.totalSelect[this.searchForm.current]) this.totalSelect[this.searchForm.current] = []
            val.forEach((item) => {
                if (this.getAllSelectLength() >= 50) {
                    this.$nextTick(() => {
                        this.$refs.elTable.toggleRowSelection(item, false)
                    })
                } else {
                    this.selectList.push(item.uid)

                    if (!this.totalSelect[this.searchForm.current]) {
                        this.$set(this.totalSelect, this.searchForm.current, [])
                    }
                    // 存入total，避免翻页数据丢失
                    this.totalSelect[this.searchForm.current].push(item.uid)
                }
            })
        },
        // 已选择的
        getAllSelectLength() {
            let length = 0
            for (const key in this.totalSelect) {
                length = length + this.totalSelect[key].length
            }
            return length
        },
        //获取我的课程
        async getMyCourseList() {
            this.loading = true
            try {
                const { searchForm } = this
                let result = await this.$axios.post(this.$api.getKnowledgCourseList, searchForm)
                if (result.data.code == this.$code.SUCCESS) {
                    this.tableData = result.data.data.records
                    this.total = result.data.data.total
                    console.log('获取我的课程', this.tableData)
                    // 翻页还原勾选
                    this.selectList = []
                    this.tableData.forEach(item => {
                        if (this.totalSelect[this.searchForm.current] && this.totalSelect[this.searchForm.current].includes(item.uid)) {
                            this.$nextTick(() => {
                                this.selectList.push(item.uid)
                                this.$refs.elTable.toggleRowSelection(item, true)
                            })
                        }
                    })
                }
            } catch (err) {
                this.loading = false
            }
        },
        // 改变页大小
        handleSizeChange(val) {
            this.searchForm.size = val
            this.getMyCourseList()
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.searchForm.current = val
            this.getMyCourseList()
        },
    },
}
</script>

<style lang="scss" scoped>
.course-status {
    display: inline-block;
    border-radius: 2px;
    padding: 2px 6px;
}

.course-status-0 {
    color: #666666;
    background: #E6EAED;
}

.course-status-1 {
    color: #3AAC7B;
    background: rgba(58, 172, 123, 0.1);
}

.course-status-2 {
    color: #F77900;
    background: rgba(247, 121, 0, 0.102);
}

.upclass {
    width: 58px;
    height: 22px;
    background: rgba(58, 172, 123, 0.1);
    border-radius: 2px;
    font-weight: 400;
    color: #3AAC7B;
}

.closeclass {
    font-weight: 400;
    width: 58px;
    height: 22px;
    background: #E6EAED;
    border-radius: 2px;
    color: #666666;
}

.courseVideoDialog {
    .content {
        .content-table {
            margin-top: 20px;
        }
    }

    .dialog-bottom {
        .success {
            width: 120px;
            height: 36px;
            background: #006EFF;
            border-radius: 4px;
            color: #FFFFFF;
            font-weight: 400;
            font-size: 14px;
            border: none;
        }

        .cancel {
            width: 120px;
            height: 36px;
            background: #F4F6F7;
            border-radius: 4px;
            color: #666666;
            font-weight: 400;
            font-size: 14px;
            border: none;
        }
    }

}
</style>