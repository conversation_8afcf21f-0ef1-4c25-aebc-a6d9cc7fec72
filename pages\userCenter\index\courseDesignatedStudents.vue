<!-- 课程指定学员操作页面 -->
<template>
    <div class="designated-student fa-55">
        <div class="flex jc-between ai-center mb-20">
            <div class="font-14">
                <span class="color-999 cursor hover-color" @click="goToPage('/userCenter/myCourse')">我的课程</span>
                <span class="mx-10 color-999">/</span>
                <span class="color-black">课程设置</span>
            </div>
            <div>
                <hyButton class="ml-15" :key="106"
                    :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px', lineHeight: '22px !important' }"
                    size="small" width="80px" height="28px" :plain="true" @click="goToPage('/userCenter/myCourse')">返回
                </hyButton>
            </div>
        </div>
        <div class="top-datail flex mb-30">
            <div class="detail-img">
                <el-image style="width: 100%; height: 100%;border-radius: 4px;"
                    :src="courseDetail.pictureUrl" fit="cover">
                    <div slot="error" style="width: 100%; height: 100%">
                        <img style="width: 100%; height: 100%" v-lazy="$store.state.defaultEnterpriseAvater" />
                    </div>
                </el-image>
            </div>
            <div class="ml-10 flex-col flex-1 jc-center" style="width: 0px;">
                <div class="font-14"><span class="color-black mr-10">{{ courseDetail.name }}</span>
                    <span v-if="courseDetail.upRelease == 1" class="status" :class="`status-2`">{{ '待上架' }}</span>
                    <span v-if="courseDetail.upRelease == 0" class="status" :class="`status-${courseDetail.status}`">{{
                        courseDetail.status == 1 ? '已上架' : '已下架' }}</span>
                </div>
                <div class="mt-5 font-12"><span style="color: #FF564D;">{{ courseDetail.sellWay != 1 ?
                    coursetypeList[courseDetail.sellWay]
                    : courseDetail.sellingPrice }}</span></div>
            </div>
        </div>

        <div class="students-list flex-col mt-25">
            <div class="top-control flex jc-between" style="height: 38px;flex-shrink: 0;">
                <div>
                    <el-select v-model="studuForm.status" placeholder="请选择" style="width: 132px;" @change="selectChange">
                        <el-option v-for="item in typeList" :key="item.uid" :label="item.name" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="flex">
                    <hySearchInput placeholder="学员昵称和手机号码查询" :inputVal.sync="studuForm.keyword" @realSearch="realSearch"
                        width="320px" height="36px"></hySearchInput>
                    <hyButton class="ml-20"
                        :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px', lineHeight: '30px !important' }"
                        size="small" width="116px" height="36px" :plain="true" @click="addStudy"><i
                            class="icon-add1 iconfont" style="margin-right: 3px;"></i>添加学员</hyButton>

                    <hyButton class="ml-20"
                        :extendStyle="{ backgroundColor: '#006EFF', border: '1px solid #006EFF', color: '#fff !important', letterSpacing: '1px', lineHeight: '30px !important' }"
                        size="small" width="116px" height="36px" :plain="true" @click="handleDel"
                        :disabled="!selectList.length"><i class="icon-shanchu iconfont" style="margin-right: 3px;"></i>批量删除
                    </hyButton>
                </div>
            </div>
            <!-- 表格 -->
            <div class="main-table flex flex-1" style="height: 0;">
                <el-table class="mt-20 w-100" :data="tableData" height='auto' v-loading="loading"
                    @selection-change="handleSelectionChange" :header-cell-style="{ background: '#F3F4F7' }">
                    <el-table-column type="selection" width="44" />
                    <el-table-column>
                        <template slot="header">
                            <span class="font-14 color-666">学员</span>
                        </template>
                        <template slot-scope="scope">
                            <div class="study-info flex ai-center">
                                <div class="study-cover">
                                    <el-image style="width: 100%; height: 100%;border-radius: 50%;"
                                        :src="scope.row.avatarPath" fit="cover">
                                        <div slot="error" style="width: 100%; height: 100%;border-radius: 50%;">
                                            <img style="width: 100%; height: 100%"
                                                v-lazy="$store.state.defaultEnterpriseAvater" />
                                        </div>
                                    </el-image>
                                </div>
                                <span class="ml-10">{{ scope.row.nickName }}</span>
                            </div>

                        </template>
                    </el-table-column>
                    <el-table-column width="130">
                        <template slot="header">
                            <span class="font-14 color-666">绑定号码</span>
                        </template>
                        <template slot-scope="scope">
                            <span class="color-black font-14"> {{ scope.row.mobile }} </span>
                        </template>
                    </el-table-column>
                    <el-table-column width="90">
                        <template slot="header">
                            <span class="font-14 color-666">状态</span>
                        </template>
                        <template slot-scope="scope">
                            <span class="status-type" :class="`status-type-${scope.row.status}`">
                                {{ scope.row.status == 1 ? '有效' : '已过期' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column width="90">
                        <template slot="header">
                            <span class="font-14 color-666">加入类型</span>
                        </template>
                        <template slot-scope="scope">
                            <span class="color-black font-14">{{ scope.row.joinType == 0 ? '单个添加' : '批量添加' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column width="180">
                        <template slot="header">
                            <span class="font-14 color-666">加入时间</span>
                        </template>
                        <template slot-scope="scope">
                            <div class="font-14 color-black">{{ scope.row.createTime }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column width="180">
                        <template slot="header">
                            <span class="font-14 color-666">到期时间</span>
                        </template>
                        <template slot-scope="scope">
                            <div class="font-14 color-black">{{ scope.row.expireTime ? scope.row.expireTime : '长期有效' }}
                            </div>
                        </template>
                    </el-table-column>

                    <template slot="empty">
                        <div class="flex-col jc-center ai-center">
                            <img v-lazy="require('@/assets/images/userCenter/curriculum/data-empty.png')" alt=""
                                style="width: 120px; height: 75px" />
                            <span class="color-666 font-14">暂无学员</span>
                        </div>
                    </template>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="flex ai-center jc-between mt-15" style="height: 64px;flex-shrink: 0;">
                <div>
                    <span class="font-14 color-999">{{ `每页 ${studuForm.pageSize} 条, 共 ${total} 条数据` }}</span>
                </div>
                <div>
                    <el-pagination class="msg-pagination-container" background layout="prev, pager, next" :total="total"
                        :background="true" :current-page="studuForm.currentPage" :page-size="studuForm.pageSize"
                        @current-change="handleCurrentChange">
                    </el-pagination>
                </div>
            </div>

            <!-- 添加学员dialog -->
            <hyDialog2 :loading="addloading" :visible.sync="addDialog" @confirm="submitAdd('addForm')" height="312px"
                width="650px">
                <template slot="title">
                    <span class="font-18 color-black">添加学员</span>
                </template>
                <template slot="content">
                    <div>
                        <el-form :model="addForm" status-icon :rules="rules" ref="addForm" label-width="80px"
                            class="demo-ruleForm">
                            <el-form-item prop="mobile">
                                <template slot="label">
                                    <span class="color-666 font-14">添加学员</span>
                                </template>
                                <template>
                                    <el-input v-model="addForm.mobile" autocomplete="off" style="width: 450px;"></el-input>
                                </template>
                            </el-form-item>
                            <el-form-item prop="time" style="margin-top: 15px;">
                                <template slot="label">
                                    <span class="color-666 font-14">有效期</span>
                                </template>
                                <template>
                                    <div class="flex ai-center">
                                        <el-radio-group v-model="addForm.expireType">
                                            <el-radio :label="1">长期有效</el-radio>
                                            <el-radio :label="0">自定义时间
                                            </el-radio>
                                        </el-radio-group>
                                        <div class="ml-15">
                                            <el-date-picker v-model="addForm.expireTime" type="datetime"
                                                placeholder="选择到期日期时间" :disabled="addForm.expireType == 1"
                                                value-format="yyyy-MM-dd HH:mm:ss" @change="handleChange">
                                            </el-date-picker>
                                            <!-- <el-date-picker  type="daterange" style="width: 250px;"
                      range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                      :disabled="addForm.expireType == 1" @change="handleChange" >
                    </el-date-picker> -->
                                        </div>
                                    </div>
                                </template>
                            </el-form-item>
                        </el-form>
                    </div>
                </template>
            </hyDialog2>
        </div>
    </div>
</template>
<script>

export default {
    name: 'HuanyuCollegeNuxtWebCourseDesignatedStudents',
    components: {

    },
    data() {
        var checkTime = (relus, value, callback) => {
            if (this.addForm.expireType == 0) {
                if ((this.addForm.expireTime)) {
                    callback()
                } else {
                    callback(new Error('请选择有效期'))
                }
            } else {
                callback()
            }
        }
        // 验证手机号
        const validateMobile = (rule, value, callback) => {
            let sqlReg = new RegExp(/select|update|delete|exec|count|'|"|=|;|>|<|%/);
            let mobileReg = new RegExp(/^0?1[3|4|5|8|7|9|6][0-9]\d{8}$/);
            if (value === "") {
                callback(new Error("请您输入正确的手机号"));
            } else {
                if (sqlReg.test(value)) {
                    callback(new Error("请不要输入特殊字符"));
                } else {
                    if (mobileReg.test(value)) {
                        callback();
                    } else {
                        callback(new Error("请您输入正确的手机号"));
                    }
                }
            }
        };
        return {
            coursetypeList: ['免费', '付费', '加密', '指定学员'],
            typeList: [
                { uid: 1, name: '全部状态', value: '' },
                { uid: 2, name: '有效', value: '1' },
                { uid: 3, name: '已过期', value: '2' },
            ],
            tableData: [], // 表格数据
            loading: false, // 列表数据加载状态
            addDialog: false, // 添加学员弹窗
            addloading: false, // 添加学员加载中
            addForm: {
                expireType: 1, // 0-设置有效时间 1-长期有效
                mobile: '',
                expireTime: '',
                curriculumUid: '',
                time: ''
            },
            total: 0,
            studuForm: { // 添加学员表单
                keyword: '',
                status: '',
                curriculumUid: '',
                pageSize: 10,
                isBind: 1,
                currentPage: 1
            },
            rules: {
                mobile: [
                    {
                        validator: validateMobile,
                        trigger: ["blur", "change"],
                    },
                ],
                time: [
                    { required: true, validator: checkTime, trigger: ['change', 'blur'] }
                ]
            },
            selectList: [], // 表格前面复选框的所选择的值
            courseDetail: '', // 课程详情对象
        };
    },

    mounted() {
        this.getData()
        const courseUid = this.$route.query.uid || ''
        if (courseUid) {
            this.getCourseDetail(courseUid)
        }
    },
    watch: {

    },
    computed: {

    },

    methods: {
        realSearch() {
            this.studuForm.currentPage = 1
            this.getData()
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.studuForm.currentPage = val
            this.getData()
        },
        // 添加学员操作
        submitAdd(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const data = JSON.parse(JSON.stringify(this.addForm))
                    delete data.time
                    if (data.expireType == 1) {
                        data.expireTime = ''
                    } else if (data.expireType == 0) {
                        data.expireTime = data.expireTime
                    }
                    this.addloading = true
                    let str = ''
                    if (this.$route.query.type == 'package') {
                        str = 'bindingOneStudent'
                    } else {
                        str = 'DesignateStudentAddStudent'
                    }
                    this.$request.myCourse[str](data).then((res) => {
                        if (res.data.code == 200) {
                            this.addDialog = false
                            this.getData()
                        }
                    }).catch((err) => {
                        this.$message.warning(err.data.message)
                    }).finally(() => {
                        this.addloading = false
                    })
                }
            })
        },
        // 获取表格数据
        getData() {
            this.loading = true
            const data = JSON.parse(JSON.stringify(this.studuForm))
            data.curriculumUid = this.$route.query.uid || ''
            let str = ''
                    if (this.$route.query.type == 'package') {
                        str = 'selectStudentList'
                    } else {
                        str = 'getDesignateStudentList'
                    }
            this.$request.myCourse[str](data).then((res) => {
                if (res.data.code == 200) {
                    const result = res.data.data
                    this.tableData = result.records
                    this.total = result.total
                }
            }).finally(() => {
                this.loading = false
            })
        },
        // 下拉框值发生变化时
        selectChange() {
            this.studuForm.currentPage = 1
            this.getData()
        },
        handleChange(val) {
            this.addForm.expireTime = val || ''
        },
        // 打开添加学员弹窗
        addStudy() {
            this.addForm = {
                expireType: 1, //
                mobile: '',
                expireTime: '',
                curriculumUid: this.$route.query.uid || '',
                time: ''
            },
                this.addDialog = true
            this.$nextTick(() => {
                this.$refs['addForm'].resetFields()
            })
        },
        // 批量删除
        handleDel() {
            this.$confirm('此操作将删除该课程下选择的学员, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const UIDList = this.selectList.map((item) => item.uid)
                let str = ''
                    if (this.$route.query.type == 'package') {
                        str = 'deleteStudentBatch'
                    } else {
                        str = 'DesignateStudentDelStudent'
                    }
                this.$request.myCourse[str](UIDList).then((res) => {
                    if (res.data.code == 200) {
                        this.getData()
                    }
                })
            }).catch(() => {

            });
        },
        // 获取课程详情
        getCourseDetail(uid) {
            let params
            let str = ''
                    if (this.$route.query.type == 'package') {
                        str = 'getPackageDetailByUid'
                        params = { setMealUid: uid }
                    } else {
                        params = { uid }
                        str = 'getVideoCourseDetailByUid'
                    }
            
            this.$request.myCourse[str](params).then((res) => {
                console.log(res);
                if (res.data.code == 200 || res.data.code == 0) {
                    this.courseDetail = res.data.data
                    console.log('this.courseDetail', this.courseDetail)
                }
            })
        },
        // 调整页面
        goToPage(url) {
            if (!url) return
            this.$router.push({
                path: url
            });
        },
        // 表格前面多选框选择
        handleSelectionChange(val) {
            this.selectList = val.map((item) => item);
        },
    },
};
</script>
  
<style lang="scss" scoped>
.designated-student {
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
    border-radius: 4px;
    padding: 20px;

    .top-datail {
        .detail-img {
            width: 120px;
            height: 80px;
            flex-shrink: 0;
        }

        .status {
            display: inline-block;
            padding: 6px 8px;
            border-radius: 2px;
        }

        .status-1 {
            background: rgba(58, 172, 123, 0.1);
            color: #3AAC7B;
        }

        .status-0 {
            background: #E6EAED;
            color: $color666;
        }

        .status-2 {
            color: #F77900;
            background: rgba(247, 121, 0, 0.102);
        }
    }

    .students-list {
        height: 700px;

        .main-table {
            .study-info {
                .study-cover {
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                }
            }

            .status-type {
                padding: 6px 8px;
                border-radius: 2px;
                font-size: 14px;
            }

            .status-type-1 {
                background: rgba(58, 172, 123, 0.1);
                color: #3AAC7B;
            }

            .status-type-2 {
                background: #E6EAED;
                color: $color666;
            }

        }

        /deep/ .el-select {
            height: 36px;

            .el-input {
                height: inherit;

                .el-input__inner {
                    height: inherit;
                }
            }
        }
    }
}
</style>
<style>
.msg-pagination-container.is-background .el-pager li {
    /*对页数的样式进行修改*/
    background-color: #FFF;
    color: #999;
}
</style>