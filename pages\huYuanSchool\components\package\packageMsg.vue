<template>
  <div class="package-msg flex">
    <div class="step-list mr-20">
      <div
        v-for="(item, index) in stepList"
        :key="index"
        class="flex jc-between ai-center mb-16"
      >
        <div
          class="step-item cursor"
          :class="{ 'active-step': activeStep == item.uid }"
          @click="clickStep(item)"
        >
          <div
            class="font-14 fa-55 f-w-400"
            :class="activeStep == item.uid ? 'color-white' : 'color-333'"
          >
            第{{ index + 1 }}阶段
          </div>
          <div
            class="mt-8 fa-55 f-w-400 font-12 ellipsis"
            :class="activeStep == item.uid ? 'color-white' : 'color-999'"
          >
            {{ item.directoryName }}
          </div>
        </div>
        <div v-if="activeStep == item.uid" class="step-right"></div>
      </div>
      <div class="flex jc-between ai-center">
        <div
          class="step-item cursor"
          :class="{ 'active-step': activeStep == 'download' }"
          @click="clickStep({ uid: 'download' })"
        >
          <div
            class="font-14 fa-55 f-w-400"
            :class="activeStep == 'download' ? 'color-white' : 'color-333'"
          >
            资料下载
          </div>
          <div
            class="mt-8 fa-55 f-w-400 font-12 ellipsis"
            :class="activeStep == 'download' ? 'color-white' : 'color-999'"
          >
            {{ parentThis.detailInfo.setMealName }}
          </div>
        </div>
        <div v-if="activeStep == 'download'" class="step-right"></div>
      </div>
    </div>
    <div v-if="activeStep != 'download'" class="detail-main">
      <div class="color-333 font-24 fa-75 f-w-600 ellipsis mb-10">
        {{ stepMsg.directoryName }}
      </div>
      <div class="flex curri-list">
        <div
          v-for="(itemList, index) in stepMsg.curriculumList"
          :key="index"
          class="curri-item"
          :class="{ 'mr-20': index != 2 }"
        >
          <el-popover
            placement="right"
            trigger="hover"
            width="300"
            popper-class="popper-main"
          >
            <div class="curri-popover pt-8">
              <div class="flex ai-center popover-head px-12">
                <img
                  :src="
                    require('../../../../assets/newVersion/courseDetail/book.png')
                  "
                  style="width: 20px; height: 20px"
                />
                <span class="f-w-400 fa-55 font-16 color-333 ml-5"
                  >课程大纲</span
                >
              </div>
              <el-collapse
                v-if="itemList.outlines && itemList.outlines.length > 0"
                accordion
              >
                <el-collapse-item
                  v-for="(item, index) in itemList.outlines"
                  :key="index"
                >
                  <template slot="title">
                    <div class="flex ai-center jc-between" style="width: 90%">
                      <el-tooltip
                        v-if="item.name.length > 25"
                        class="item"
                        effect="dark"
                        :content="`${item.name}`"
                        placement="top"
                      >
                        <span
                          class="color-333 font-16 f-w-500 fa-65 ellipsis"
                          >{{ item.name }}</span
                        >
                      </el-tooltip>
                      <span
                        v-else
                        class="color-333 font-16 f-w-500 fa-65 ellipsis"
                        >{{ item.name }}</span
                      >
                      <div
                        class="flex ai-center font-12 color-999 fa-55 f-w-400"
                      >
                        <div class="flex ai-center">
                          <img
                            style="margin-right: 4px"
                            src="@/assets/newVersion/course/time.png"
                            alt=""
                          />
                          {{ getDuration(item.videoList) }}
                        </div>
                        <div class="flex ai-center">
                          <img
                            style="margin-right: 4px"
                            src="@/assets/newVersion/course/chapter.png"
                            alt=""
                          />
                          {{ item.videoTotal || 0 }}节
                        </div>
                      </div>
                    </div>
                  </template>
                  <div v-if="item.videoList && item.videoList.length > 0">
                    <div
                      class="flex ai-center jc-between video-item color-999"
                      v-for="(details, i) in item.videoList"
                      @click="toDetail(itemList.courseUid, details.uid, item.uid)"
                      :key="i"
                    >
                      <div class="cursor flex ai-center">
                        <img
                          class="play-1"
                          src="../../../../assets/newVersion/courseDetail/playing.png"
                        />
                        <img
                          class="play-2"
                          src="../../../../assets/newVersion/courseDetail/waiting-play.png"
                        />
                        <span class="font-14 f-w-400 fa-55 ml-5 ellipsis">{{
                          details.name
                        }}</span>
                      </div>

                      <span class="font-14 f-w-400 fa-55">
                        {{ getTime(details.duration) }}
                      </span>
                    </div>
                  </div>
                  <div v-else class="flex ai-center jc-center color-999 font-14 " style="width: 100%; height: 80px;">
                    暂无内容
                  </div>
                </el-collapse-item>
              </el-collapse>
              <div v-else class="flex ai-center jc-center color-999 font-14 " style="width: 100%; height: 80px;">
                暂无内容
              </div>
              <div class="popover-footer px-20 py-12 flex ai-center jc-between">
                <hyButton type="primary" width="80px" height="28px">
                  <template>
                    <span
                      style="font-size: 10px !important; letter-spacing: 0px"
                      >免费试看</span
                    >
                  </template>
                </hyButton>

                <i class="el-icon-star-off"></i>
              </div>
            </div>
            <img
              slot="reference"
              v-lazy="itemList.pictureUrl"
              v-aspect="[266, 151]"
            />
          </el-popover>
          <div>
            <div class="px-10 pt-8 pb-12">
              <div
                class="font-16 color-333 fa-65 f-w-500 mb-8 curri-name ellipsis2"
              >
                {{ itemList.courseName }}
              </div>
              <div class="flex ai-center mb-10">
                <span class="fa-55 color-666 font-12 f-w-400"
                  >{{
                    formattrtDuration(itemList)
                  }}课时</span
                >
                <div class="line"></div>
                <span class="fa-55 color-666 font-12 f-w-400">{{
                  getDuration(itemList.duration)
                }}</span>
              </div>

              <div
                class="ellipsis3 font-12 fa-55 f-w-400 desc-color"
                v-html="itemList.intro"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="detail-main">
      <div class="flex" style="flex-direction: column; width: 100%">
        <div class="flex ai-cenetr jc-between" style="width: 310px">
          <hyButton
            :extendStyle="{
              backgroundColor: '#EBF0F7',
              border: '1px solid #EBF0F7',
              color: '#006EFF !important',
              letterSpacing: '1px',
            }"
            size="small"
            width="99px"
            height="32px"
            :plain="true"
            @click="downloadFile"
            ><i class="el-icon-download iconfont font-14"></i
            ><span>下载</span></hyButton
          >
        </div>
        <!-- 表格数据 -->
        <div class="mt-10" style="height: 340px">
          <el-table
            height="100%"
            :data="tableData"
            @selection-change="handleSelectionChange"
            :header-cell-style="{ background: '#EBF0F7' }"
          >
            <el-table-column type="selection" width="45" />

            <el-table-column show-overflow-tooltip>
              <template slot="header">
                <span class="font-14 color-666">文件名称 </span>
              </template>
              <template slot-scope="scope">
                <span class="color-black font-14 cursor hover-color">
                  {{ scope.row.fileName }}
                </span>
              </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip width="90" align="center">
              <template slot="header">
                <span class="font-14 color-666">文件大小 </span>
              </template>
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.fileSize && scope.row.fileSize / 1024 / 1024 > 1
                  "
                >
                  {{ (scope.row.fileSize / 1024 / 1024).toFixed(2) }}MB
                </span>
                <span v-else
                  >{{ (scope.row.fileSize / 1024).toFixed(2) }}KB</span
                >
              </template>
              <!-- <template slot-scope="scope">
                                <span class="color-black font-14">{{ scope.row.fileSize }}</span>
                            </template> -->
            </el-table-column>
            <!-- :formatter="formattrtSuffix" -->
            <el-table-column show-overflow-tooltip width="110" align="center">
              <template slot="header">
                <span class="font-14 color-666">文件类型</span>
              </template>
              <template slot-scope="scope">
                <span
                  class="color-black font-14"
                  v-html="formattrtSuffix(scope.row)"
                >
                </span>
              </template>
            </el-table-column>

            <el-table-column show-overflow-tooltip width="200" align="center">
              <template slot="header">
                <span class="font-14 color-666">添加时间 </span>
              </template>
              <template slot-scope="scope">
                <span class="color-black font-14">{{
                  scope.row.createTime
                }}</span>
              </template>
            </el-table-column>

            <!-- <el-table-column show-overflow-tooltip width="90" align="center">
              <template slot="header">
                <span class="font-14 color-666">是否为免费 </span>
              </template>
              <template slot-scope="scope">
                <div
                  class="flex ai-center jc-center"
                  v-if="scope.row.hasFree == 1"
                >
                  <div class="free-tag"></div>
                  <span class="ml-5">免费</span>
                </div>
                <span class="color-333 font-14" v-else> -- </span>
              </template>
            </el-table-column> -->

            <template slot="empty">
              <div class="flex-col jc-center ai-center">
                <img
                  v-lazy="
                    require('@/assets/images/userCenter/curriculum/data-empty.png')
                  "
                  alt=""
                  style="width: 120px; height: 75px"
                />
                <span class="color-666 font-14">暂无资料</span>
              </div>
            </template>
          </el-table>
        </div>

        <!-- 分页 -->
        <div
          style="height: 56px"
          class="flex ai-center jc-end"
          v-if="tableData"
        >
          <div style="height: 40px">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="dataPageForm.currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="dataPageForm.pageSize"
              layout="total, prev, pager, next, jumper"
              :total="dataTotal"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  inject: ["parentThis"],
  data() {
    return {
      stepList: [
        { uid: 0, name: "[面向高薪] K8s企业级落地实战 ......" },
        { uid: 1, name: "云原生Kubermetes全栈架构师: 其于世界" },
        { uid: 2, name: "云原生K8s管理员认证课-零基础 考题更新" },
        { uid: 3, name: "CKS原题-K8s CIS基准测试与安全扫摧" },
      ],
      activeStep: 0,
      stepMsg: {},
      materialList: [],
      coursePageForm: {
        // 视频分页参数
        currentPage: 1,
        pageSize: 3,
        directoryUid: "",
        courseName: "",
      },
       dataPageForm: {
        currentPage: 1,
        pageSize: 10,
        fileName: '',
        setMealUid: '',
        fileType: ''
      },
      dataTotal: 0
    };
  },
  computed: {},
  watch: {
    "$route.query": {
      handler: function (newVal) {
        if (newVal && newVal.uid) {
          // 详情信息
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getAllChapter();
  },
  methods: {
    clickStep(val) {
      this.activeStep = val.uid;
    },
    handleSelectionChange(val) {
      this.selectList = val.map((item) => item);
    },
    downloadFile() {
      // 防抖
      this.$debounce(() => {
        // 下载客服文件
        this.selectList.forEach((item) => {
          //下载图片地址和图片名
          // 解决跨域 Canvas 污染问题
          if (["png", "jpg", "gif", "jpeg"].includes(item.suffix)) {
            let image = new Image();
            image.src = localStorage.getItem("picBaseUrl") + item.url;
            // 解决跨域 Canvas 污染问题
            image.setAttribute("crossOrigin", "Anonymous"); // 支持跨域
            image.onload = function () {
              var canvas = document.createElement("canvas");
              canvas.width = image.width;
              canvas.height = image.height;
              var context = canvas.getContext("2d");
              context.drawImage(image, 0, 0, image.width, image.height);
              var url = canvas.toDataURL("image/"+item.suffix); //得到图片的base64编码数据
              var a = document.createElement("a"); // 生成一个a元素
              var event = new MouseEvent("click"); // 创建一个单击事件
              a.download = item.fileName; // 设置图片名称
              a.href = url; // 将生成的URL设置为a.href属性
              a.dispatchEvent(event); // 触发a的单击事件
            };
          }
          if (item.suffix == 'mp4') {
            fetch(localStorage.getItem("picBaseUrl") + item.url).then(res => res.blob()).then(blob => {
              const a = document.createElement('a');
              document.body.appendChild(a)
              a.style.display = 'none'
              const url = window.URL.createObjectURL(blob);
              a.href = url;
              a.download = item.fileName;
              a.click();
              document.body.removeChild(a)
            })
          }
          if (["doc", "docx", "pdf", "ppt", "xls", "xlsx"].includes(item.suffix)) {
            fetch(localStorage.getItem("picBaseUrl") + item.url).then(res => res.blob()).then(blob => {
              const a = document.createElement('a');
              document.body.appendChild(a)
              a.style.display = 'none'
              const url = window.URL.createObjectURL(blob);
              a.href = url;
              a.download = item.fileName;
              a.click();
              document.body.removeChild(a)
            })
          }
        });
      })();
    },
    getData() {
            if (!this.$route.query.uid || '') {
                this.$message({
                    message: '套餐不存在！',
                    type: 'warning',
                    duration: 1000
                });
                return
            }
            this.loading = true
            const params = JSON.parse(JSON.stringify(this.dataPageForm))
            params.setMealUid = this.$route.query.uid || '' || ''
            this.$request.myCourse.getMyCreatePackageAllInformation(params).then((res) => {
                if (res.data.code == this.$code.SUCCESS) {
                    const result = res.data.data
                    this.tableData = result.records
                    this.dataTotal = result.total
                }
            }).finally(() => {
                this.loading = false
            })
        },
    toDetail (courseUid,uid, parentUid) {
      if ((this.parentThis.passwordType) || (this.parentThis.detailInfo.namedUser == 1) || (this.parentThis.isBuy == 1) || (this.parentThis.detailInfo.lecturerUid == this.$store.state.userInfo.speakerUid)) {
        this.$router.push({
          path: '/huYuanSchool/packageCourseDetail',
          query: {
            uid: courseUid,
            videoUid: uid,
            pUid: parentUid
          }
        })
      } else {
        this.$message.error('暂无权限');
      }
    },
    getDuration (list) {
      if (!list || list.length < 1) return 0
      let length = 0
      list.forEach(item => length += item.duration)
      let time = this.getTime(length, 1)
      let str = ''
      if (time[1] > 0) str += time[1] + '小时'
      if (time[2] > 0) str += time[2] + '分钟'
      if (time[3] > 0) str += time[3] + '秒'
      if (str == '') str = '0分钟'
      return str
    },
    // 获取所有阶段
    getAllChapter() {
      const params = {
        setMealUid: this.$route.query.uid || "",
      };
      this.$request.myCourse
        .getPackageCourseAllChapterByUid(params)
        .then((res) => {
          if (res.data.code == 200) {
            // const result = res.data.data
            this.stepList = res.data.data;
            if (this.stepList.length) {
              this.clickStep(this.stepList[0]);
              this.choiceChapter(this.stepList[0]);
            }
          }
        });
    },
    // 选择阶段
    choiceChapter(item) {
      this.currentChapterUid = item.uid;
      this.coursePageForm.directoryUid = item.uid;
      const params = JSON.parse(JSON.stringify(this.coursePageForm));
      params.setMealUid = this.$route.query.uid || "";
      this.$request.myCourse
        .myCreatePackageChapterAllCourse(params)
        .then((res) => {
          if (res.data.code == 200) {
            const result = res.data.data;
            this.stepMsg = {};
            this.stepMsg.directoryName = item.directoryName;
            this.stepMsg.curriculumList = result.records;
            this.chapteCourseTotal = result.total;
            this.stepMsg.curriculumList.forEach((item) => {
              this.$request.curriculum
                .outlineList(item.courseUid)
                .then((res) => {
                  if (res.data.code == 200) {
                    item.outlines = res.data.data;
                    this.$forceUpdate();
                  }
                });
            });
          }
        });
    },
    // 格式化视频时长
        formattrtDuration(row, type) {
          console.log(row.courseVideoTimeLong);
            const resultTime = row && this.$getHMSTime(type ? row.videoDuration : row.courseVideoTimeLong)
            if (resultTime) {
                return `${resultTime[0]}:${resultTime[1]}:${resultTime[2]}`
            } else {
                return 0
            }
        },
    getTime(val, type = 0) {
      let value = val;
      let time = [];
      let day = parseInt(value / 86400);
      let hour = parseInt((value % 86400) / 3600);
      let min = parseInt(((value % 86400) % 3600) / 60);
      let sec = parseInt(((value % 86400) % 3600) % 60);
      time[0] = day > 0 ? day : 0;
      time[1] = hour > 0 ? hour : 0;
      time[2] = min > 0 ? parseFloat(min) : 0;
      time[3] = sec > 0 ? parseFloat(sec) : 0;
      let durationHour =
        time[1] > 0 ? (time[1] > 9 ? time[1] : "0" + time[1]) + ":" : "";
      let durationMin =
        time[2] > 0 ? (time[2] > 9 ? time[2] : "0" + time[2]) + ":" : "00:";
      let durationSec =
        time[3] > 0 ? (time[3] > 9 ? time[3] : "0" + time[3]) : "00";
      if (type == 1) return time;
      return durationHour + durationMin + durationSec;
    },
  },
};
</script>

<style lang="scss" scoped>
.step-list {
  width: 280px;

  .step-item {
    padding: 12px 18px;
    width: 260px;
    height: 74px;
    background: url("../../../../assets/newVersion/courseDetail/default-package.png");
    background-size: 100% !important;
    background-repeat: no-repeat;
    box-shadow: 0px 2px 2px 0px rgba(55, 99, 170, 0.1);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #ffffff;
  }

  .active-step {
    font-weight: 600;
    background: url("../../../../assets/newVersion/courseDetail/active-package.png");
  }

  .step-right {
    width: 4px;
    height: 74px;
    background: #e5f0ff;
    border-radius: 2px 2px 2px 2px;
  }
}

.detail-main {
  background: linear-gradient(315deg, #f1f7ff 0%, #ffffff 50%, #f2f7fc 100%);
  box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
  border-radius: 4px 4px 4px 4px;
  border: 2px solid #ffffff;
  padding: 20px;
  width: calc(100% - 300px);
  height: max-content;
  // cursor: pointer;
}

.desc-color {
  color: #9e9e9e;
}

.curri-list {
  .curri-item {
    width: calc((100% - 40px) / 3);
    background: linear-gradient(135deg, #f2f7fc 0%, #f9fbfc 100%);
    box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
    border-radius: 4px 4px 4px 4px;
    border: 2px solid #ffffff;
    cursor: pointer;

    img {
      width: 100%;
    }
  }

  .curri-item:hover {
    .curri-name {
      color: $colorMain;
    }
  }
}

.line {
  width: 1px;
  height: 12px;
  background: #e6eaed;
  border-radius: 1px 1px 1px 1px;
  opacity: 1;
  margin: 0 8px;
}

.curri-popover {
  .popover-head {
    padding-bottom: 8px;
    width: 100%;
    border-bottom: 1px solid #e6eaed;
  }

  .popover-main {
    height: 250px;
    overflow: auto;

    // 滚动条整体样式
    &::-webkit-scrollbar {
      width: 3px !important;
      /* 纵向滚动条 宽度 */
      border-radius: 5px;
      /* 整体 圆角 */
    }

    //轨道部分
    &::-webkit-scrollbar-track {
      background: #fff !important;
    }

    // 滑块部分
    &::-webkit-scrollbar-thumb {
      background: #d4dbe0;
      max-height: 50px;
      width: 2px !important;
      border-radius: 5px;
    }

    scrollbar-width: thin !important;
    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    -ms-overflow-style: none !important;
    /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
  }

  .video-item {
    padding: 8px 0px;
    padding-right: 5px;
    border-radius: 4px 4px 4px 4px;

    div:nth-child(1) {
      width: calc(100% - 60px);
    }

    .play-1 {
      display: none;
    }
  }

  .video-item:hover {
    color: #006eff !important;

    .play-1 {
      display: inline-block;
    }

    .play-2 {
      display: none;
    }
  }

  .popover-footer {
    width: 100%;
    height: 52px;
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
    border-radius: 0px 0px 4px 4px;
    opacity: 1;

    i {
      color: $colorMain;
      font-size: 24px;
      cursor: pointer;
    }
  }
}

.material {
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 2px 5px 0px rgba(55, 99, 170, 0.1);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  padding: 8px 20px;

  div:nth-child(1) {
    width: calc(100% - 100px);
  }

  div:nth-child(2) {
    width: 100px;
    text-align: center;
  }
}

.material-item:hover {
  div:nth-child(1) {
    color: $colorMain !important;
  }
}
</style>

<style>
.popper-main {
  padding: 0 !important;
  /* background: pink; */
}
</style>
