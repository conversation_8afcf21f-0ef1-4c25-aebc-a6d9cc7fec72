<template>
  <div class="training-content flex w-1440">
    <div class="left mt-50">
      <div class="head">
        <div class="flex ai-center">
          <div
            @click="regionFn({ uid: '全部' })"
            :class="
              regionName == '全部'
                ? 'mr-10 tab-btn selected-btn cursor'
                : 'tab-btn mr-10 cursor'
            "
          >
            全部
          </div>
          <div
            @click="regionFn(item)"
            :class="
              regionName == item.uid
                ? 'mr-10 tab-btn selected-btn cursor'
                : 'tab-btn mr-10 cursor'
            "
            v-for="(item, index) in regionList"
            :key="index"
          >
            {{ item.name }}
          </div>
        </div>
        <el-autocomplete
          class="inline-input"
          v-model="keyword"
          size="mini"
          :fetch-suggestions="querySearch"
          placeholder="请输入证书名称"
          suffix-icon="el-icon-search"
          @select="handleSelect"
          @change="handleSelect"
          clearable
        ></el-autocomplete>
      </div>
      <div class="body flex-col" v-loading="loading">
        <div class="body-head-tag flex" ref="purchaseItem">
          <div style="width: 90%"></div>
          <div
            @click="changeContent"
            class="refresh flex-center pt-5 flex-1 font-14 cursor"
          >
            <i class="mr-5 iconfont icon-a-shuaxin1"></i>
            <span>换一组</span>
          </div>
        </div>
        <div
          class="Tag-detail flex ai-center flex-wrap"
          :style="boxStyle"
          v-if="Tags.length != 0"
        >
          <div
            v-for="(item, index) in Tags"
            :key="index"
            class="item"
            @click="gotoCertificateDetail(item.uid)"
            :class="{ 'mr-20': (index + 1) % 5 != 0 }"
          >
            <div class="hover">
              <img :src="item.mainPictureUrl" alt="" />
            </div>
            <div class="title ellipsis">
              {{ item.fullName }}
            </div>
            <div class="short-name">
              {{ item.shortName }}
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="require('@/assets/images/certification/empty.png')"
          description="暂无数据"
        ></el-empty>
      </div>
    </div>
    <div class="flex flex-col flex-1 mt-50 ml-20 right_div">
      <div class="color-back font-18 mb-20">学员动态</div>
      <div class="right-body">
        <template v-if="dynamicList.length > 0">
          <client-only>
            <vue-seamless-scroll :data="dynamicList" class="dynamic-warp">
              <div
                class="dynamic-item pb-20"
                v-for="(item, index) in dynamicList"
                :key="index"
              >
                <div class="color-999 font-12 flex ai-center">
                  <img
                    style="width: 16px; height: 16px; margin-right: 2px"
                    src="@/assets/images/certification/application.png"
                    alt=""
                  />{{ item.registerTime }}
                </div>
                <div
                  class="color-666 fa-55 f-w-400 mt-8 font-14 ellipsis ml-20"
                  style="width: 200px"
                >
                  {{ `${item.userName}报名了${item.className}` }}
                </div>
              </div>
            </vue-seamless-scroll>
          </client-only>
        </template>
        <template v-else>
          <div class="flex-center flex-col" style="width: 100%; height: 100%">
            <img
              src="~/assets/images/certification/myTraining/message-empty-icon.png"
              alt=""
              style="width: 40px; height: 40px"
            />
            <span class="color-999 font-14">暂无数据</span>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import trainContentTagDetail from "./trainContentTagDetail.vue";
export default {
  components: {
    trainContentTagDetail,
  },
  data() {
    return {
      loading: false,
      totalPage: 0,
      page: 1,
      pageSize: 10,
      total: 0,
      Tags: [],
      keyword: "",
      currentTagObj: {},
      dynamicList: [],
      activeName: "",
      regionList: [],
      regionName: "全部",
      boxStyle: "",
      certificateNameList: [],
      restaurants: [],
    };
  },
  async mounted() {
    this.queryStudentTrend();
    // await this.initTrainCertificatePageData();
    this.getTrainCertificateAreaList();
    this.getTrainCertificateNameList(null);
    window.addEventListener("resize", this.liveBroadcastBgResize);
    this.$nextTick(() => {
      this.liveBroadcastBgResize();
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.liveBroadcastBgResize);
  },
  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        );
      };
    },
    handleSelect() {
      this.page = 1;
      if (this.regionName == "全部") {
        this.initTrainCertificatePageData(null);
      } else {
        this.initTrainCertificatePageData(this.regionName);
      }
    },
    liveBroadcastBgResize() {
      let offsetWidth = this.$refs.purchaseItem.offsetWidth;
      this.boxStyle = "--review-item-width:" + (offsetWidth - 81) / 5 + "px;";
    },
    getTrainCertificateNameList(areaUid) {
      this.$request.authentication.getTrainCertificateNameList({areaUid:areaUid}).then((res) => {
        this.certificateNameList = res.data.data;
        this.restaurants = []
        this.certificateNameList.forEach((item) => {
          this.restaurants.push({ value: item });
        });
      });
    },
    regionFn(item) {
      this.page = 1;
      this.regionName = item.uid;
      this.totalPage = 0;
      if (item.uid == "全部") {
        this.initTrainCertificatePageData(null);
        this.getTrainCertificateNameList(null)
      } else {
        this.initTrainCertificatePageData(item.uid);
        this.getTrainCertificateNameList(item.uid);
      }
    },
    getTrainCertificateAreaList() {
      this.$request.authentication.getTrainCertificateAreaList().then((res) => {
        this.regionList = res.data.data;
        this.initTrainCertificatePageData();
      });
    },
    queryStudentTrend() {
      this.$axios({
        url: this.$api.trainStudentTrend,
        method: "get",
      }).then((res) => {
        if (res.data.code === this.$code.SUCCESS) {
          this.dynamicList = res.data.data;
        }
      });
    },
    // 换一换
    changeContent() {
      this.$debounce(() => {
        if (this.totalPage >= this.total) {
          this.page = 1;
          this.totalPage = 0;
        } else {
          this.page++;
        }
        if (this.regionName == '全部') {
          this.initTrainCertificatePageData(null);
        }else{
          this.initTrainCertificatePageData(this.regionName);
        }
      })();
    },
    async initTrainCertificatePageData(areaUid) {
      this.loading = true;
      let params = {
        currentPage: this.page,
        pageSize: this.pageSize,
        areaUid: areaUid || null,
        keyword: this.keyword,
      };
      let result = await this.$axios({
        url: this.$api.getTrainCertificatePage,
        method: "post",
        data: params,
      });
      if (result.data.code === this.$code.SUCCESS) {
        this.total = result.data.data.total;
        this.Tags = result.data.data.records;
        this.totalPage += this.Tags.length;
        if (this.Tags.length) {
          this.activeName = this.Tags[0].uid;
          this.currentTagObj = this.Tags[0];
        }
      }
      this.loading = false;
    },
    handleClick(val) {
      this.currentTagObj = this.Tags[val.index];
    },
    // 解构详情
    getDescription(data, type) {
      if (!data) return;
      let arr = [];
      arr = JSON.parse(data);
      if (Array.isArray(arr) && arr.length) {
        arr.forEach((item, index) => {
          item.isShow = false;
          item.id = `${type}-${index}`;
        });
      }
      return arr;
    },
    // 跳转到全部证书页面
    gotoAllCertificates() {
      this.$router.push({
        path: "/certification/allCertificates",
      });
    },
    gotoCertificateDetail(uid) {
      this.$router.push({
        path: "/certification/certificateDetail",
        query: {
          uid: uid,
        },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/.el-tabs__active-bar {
  height: 1px !important;
}

/deep/ .el-tabs__nav-wrap::after {
  height: 1px !important;
  background: #e6eaed;
}

/deep/ .el-tabs__header {
  margin: 0px !important;
}

/deep/.el-tabs__item.is-active {
  color: $colorMain;
}

/deep/ .el-tabs__item {
  color: $color999;
}

/deep/ .el-tabs__item:hover {
  color: $colorMain;
}

.training-content {
  margin: 0 auto;

  .recommend-item {
    .recommend-title {
      display: inline-block;
      padding: 4px 12px;
      background: #f4f6f7;
      border-left: 2px solid $colorMain;
      color: $colorBlack;
      font-size: $fontB;
    }
    .recommend-content {
      overflow: hidden;
    }
  }

  .left {
    width: 1080px;
    flex-shrink: 0;
    background: url("@/assets/images/certification/bk-div.png") no-repeat;
    background-size: 103% 107%;
    background-position: -13px -11px;
    .head {
      display: flex;
      justify-content: space-between;
      // background: #ffffff;
      padding: 23px 16px 0;
      border-radius: 4px 4px 0 0;
      .right-opration {
        color: #999;
      }

      .right-opration:hover {
        color: $colorMain;
      }
    }

    .body {
      position: relative;
      padding: 0 15px 25px;
      border-radius: 0 0 4px 4px;
      // height: 410px;
      // background: white;

      .body-head-tag {
        position: relative;

        .refresh {
          // position: absolute;
          color: $color999;
          height: 40px;
          // right: 0px;
          // top: 10px;
        }

        .refresh:hover {
          color: $colorMain;
        }
      }

      .content {
        padding: 10px 10px 60px 10px;

        .watch-detail {
          position: absolute;
          left: 20px;
          bottom: 20px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .content-main {
          float: left;
        }

        .title {
          width: max-content;
          margin-top: 20px;
          font-weight: bold;
        }

        .desc {
          width: max-content;
          margin-top: 10px;
          font-size: 12px;
          color: darkgrey;
        }

        .editor-content {
          word-break: break-all;
          margin-top: 87px;
        }

        .float-right {
          margin-top: 20px;
          float: right;
          width: 255px;
          height: 146px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }

  .right_div {
    background: url("@/assets/images/certification/bk.png") no-repeat;
    background-size: 109% 107%;
    background-position: -14px -11px;
    padding: 20px;
    .right-body {
      border-radius: 8px;
      height: 370px;
      // background: white;

      .dynamic-warp {
        height: 100%;
        width: 200px;
        overflow: hidden;
      }

      .dynamic-item {
        // cursor: pointer;
        width: 100%;
        // border-bottom: 1px solid #e6eaed;

        p:first-child:hover {
          color: rgba(0, 122, 254, 1);
        }

        p:last-child {
          margin-top: 2px;
          font-size: 12px;
        }
      }
    }
  }
}
.item {
  border-radius: 4px;
  opacity: 1;
  width: calc(var(--review-item-width));
  margin-top: 8px;
  cursor: pointer;
  .title {
    font-size: 12px;
    font-family: Alibaba PuHuiTi 2-45 Light, Alibaba PuHuiTi 20;
    font-weight: 300;
    color: #3d3d3d;
  }
  .short-name {
    font-size: 12px;
    font-family: Alibaba PuHuiTi 2-45 Light, Alibaba PuHuiTi 20;
    font-weight: 300;
    color: #3d3d3d;
    display: flex;
    align-items: center;
    justify-content: end;
  }
  .hover {
    width: var(--review-item-width);
    height: calc(var(--review-item-width) * (156 / 278));
    margin-bottom: 4px;
    border-radius: 4px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.item:hover {
  .title {
    color: rgba(0, 122, 254, 1);
  }
}
::v-deep {
  .el-tabs {
    background: #fff;
  }
}
.selected-btn {
  background: #006eff !important;
  color: #ffffff !important;
}
.tab-btn {
  border-radius: 20px;
  background: #f4f6f7;
  padding: 6px 18px;
  font-size: 14px;
  font-family: Alibaba PuHuiTi 2-55 Regular, Alibaba PuHuiTi 20;
  font-weight: 400;
  color: #666666;
}
</style>