<template>
  <div class="article-list">
    <div class="article-item cursor flex ai-center" v-for="(item,index) in dataList" :key="item.uid+index" @click="toArticle(item)">
      <img :src="item.pictureUrl" class="mr-20" style="width: 160px;height: 120px">
      <div class="flex-col">
        <div class="flex ai-center">
          <div class="mr-20 art-title ellipsis">{{ item.title }}</div>
          <div :class="`art-type art-type${item.isOriginal}`">{{ item.isOriginal == 0 ? '转载' : '原创' }}</div>
        </div>

        <div class="art-desc ellipsis2">
          {{ item.summary }}
        </div>

        <div class="art-desc flex ai-center">
          <span class="art-line pr-8">{{ item.releaseTime }}</span>
          <i class="el-icon-view ml-5"></i>
          <span class="art-line pl-8 pr-8"> {{ item.clickCount }}</span>
          <i class="el-icon-star-off ml-5"></i>
          <span class="art-line pl-8 pr-8"> {{ item.collectCount }}</span>
          <img src="../../../assets/2023/hy-like.png" class="ml-5">
          <span> {{ item.starCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      imgBasePath: ''
    }
  },
  mounted() {
    this.imgBasePath = localStorage.getItem('picBaseUrl')
  },
  methods: {
    toArticle(item) {
      this.$openUrl({ path: '/articleDetail', query: { uid: item.uid } })
    }
  }
}
</script>

<style lang="scss" scoped>
.article-item {
  height: 120px;
  margin-bottom: 32px;
}

.article-item:hover {

  // box-shadow: 0px 0px 12px #e8e8e8;

}

.art-title {
  max-width: 666px;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 24px;
}

.art-type {
  padding: 3px 8px;
  width: 40px;
  height: 24px;
  border-radius: 2px 2px 2px 2px;
  font-size: 12px;
  font-family: Alibaba PuHuiTi 2.0-55 Regular, Alibaba PuHuiTi 20;
  font-weight: 400;
  line-height: 18px;
}

.art-type0 {
  background: #F4F6F7;
  color: #666666;
}

.art-type1 {
  background: rgba(0, 110, 255, 0.1);
  color: #006EFF;
}

.art-desc {
  margin: 8px 0;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  line-height: 18px;
}

.art-line {
  border-right: 1px solid #E6EAED;
}

.pl-8 {
  padding-left: 8px;
}

.pr-8 {
  padding-right: 8px;
}
</style>