<template>
    <div class="information_articleDetails min-w-1440">
      <div class="flex jc-center">
        <div class="w-1440 flex">
            <!-- 左侧 -->
            <div style="width: calc(100% * (14 / 18));">
                <div class="information_articleDetails_box">
                    <infomationAuthorInfo :key="random" :articleInfo="articleInfo" :newsArticleInfo="newsArticleInfo" :personInfo="personInfo"
                        :SimilarArticleList="SimilarArticleList" :hotArticleList="hotArticleList" :commentList="commentList"
                        :total="commentTotal" @getInfo="getPersonInfo">
                    </infomationAuthorInfo>
                </div>
            </div>
            <!-- 侧边栏 -->
            <div style="width: calc(100% * (4 / 18));">
                <div style="padding-left: 39px;">
                    <div class="publicity_one">
                        <img :src="rtPicUrl"
                            alt="" srcset="">
                    </div>
                    <!-- 相似话题 -->
                    <similarTopic :topicList="topicList"></similarTopic>
                    <!-- 大家如是说 -->
                    <peopleSay></peopleSay>
                    <!-- <div class="publicity_two">
                        <img src="https://cdn.wuzhiing.cn/hyfile/%E9%87%91%E8%9E%8D%E4%BF%9D%E9%99%A9%E5%B3%B0%E4%BC%9A%E4%BC%9A%E8%AE%AE%E9%80%9A%E7%9F%A5%E5%85%AC%E5%91%8A%E5%AE%A3%E4%BC%A0%E6%B8%85%E9%80%8F%E6%84%9F%E5%B9%BF%E5%91%8Abanner%20%281%29%402x.png"
                            alt="" srcset="">
                    </div> -->
                </div>
            </div>
        </div>
      </div>
    </div>
</template>
<script>
import infomationAuthorInfo from './component/infomationAuthorInfo'
import similarTopic from './component/similarTopic'
import peopleSay from './component/peopleSay'
export default {
    name: "infomation-authorInfo",
    components: {
        infomationAuthorInfo,
        peopleSay,
        similarTopic
    },
    data() {
        return {
            random: Date.now(),
            rtPicUrl:require('/static/images/article_img.png'),
            newsArticleInfo:{},
            uid: "",
            topicList:[],
            articleInfo: {}, //文章详情信息
            personInfo: {}, //作者的粉丝数，获赞数，排行
            SimilarArticleList: [], //同类文章列表
            hotArticleList: [], //热门文章列表
            commentList: [], //评论列表
            commentTotal: 0, //评论总数
            //获取评论列表参数
            commentParams: {
                blogUid: "",
                source: "BLOG_INFO",
                currentPage: 1,
                pageSize: 6,
            },
        };
    },
    created() {
        this.getStaticImg();
    },
    //监听路由query，获取文章uid
    watch: {
        "$route.query": {
            handler: async function (item) {
                let { uid = "", articleSource = "",type } = this.$route.query;
                console.log(articleSource, uid)
                // if (item.uid) {
                this.uid = uid || '5f50a7669e2d4e07af16425062e44fdf';
                this.commentParams.blogUid = uid || '5f50a7669e2d4e07af16425062e44fdf';
                // this.articleInfo = await this.getArticleInfo(this.uid);
                await this.watchArticleDetail(this.uid, articleSource);

                // this.getPersonInfo(); //获取排行，点赞信息
                // this.getSimilarArticle(); //获取同类文章
                this.getHotArticle(); //获取热门文章
                this.getCommentList(); // 获取评论列表
                this.getRelationTopic();//获取文章关联话题
                // }
            },
            immediate: true,
        },
    },

    mounted() {
        this.$bus(this).$on('changeComment', (val) => {
            this.commentParams.currentPage = val;
            this.getCommentList();
        })
    },

    methods: {
        // 获取文章关联话题
        async getRelationTopic() {
            let result = await this.$request.information.relationTopic({
                uid: this.uid,
                current: 1,
                pageSize: 5
            })
            if (result.data.code == 200) {
                this.topicList = result.data.data.records;
            }
        },
        async getStaticImg() {
            // let result = await this.$request.information.getStaticImg();
            // if (result.data.code == 200) {
            //     this.rtPicUrl = result.data.data;
            // }
            let result = await this.$request.home.staticLists(["article_details_img"]);
            if (result.data.code == this.$code.SUCCESS) {
                this.bannerList = result.data.data.HOME_BANNER;
                if (result.data.data['article_details_img'] && result.data.data['article_details_img'][0]) {
                    this.rtPicUrl = result.data.data['article_details_img'][0].pictureUrl
                }
            }
        },
        // 获取文章详情(新)
        async watchArticleDetail(uid, articleSource) {
            let params = {
                type:articleSource,
                uid
            }
            let result = await this.$request.information.watchArticleDetail(params);
            if (result.data.code == this.$code.SUCCESS) {
                this.newsArticleInfo = result.data.data;
                this.random=Date.now();
            }
        },
        //获取文章详情
        getArticleInfo(uid) {
            return new Promise((resolve, reject) => {
                this.$axios({
                    method: "get",
                    url: this.$api.contentGetBlogByUid,
                    params: {
                        uid,
                    },
                }).then((res) => {
                    let result = res.data.data
                    result.content = this.$changeRichtextImgStyle(result.content)
                    result.knowledgeList = result.trainSkillTreeList.sort((a, b) => a.level - b.level)
                    console.log('knowledgeList', result.knowledgeList)
                    resolve(result);
                }).catch(err => {
                    reject(err);
                });
            });
        },
        //热门文章
        getHotArticle() {
            this.$axios({
                method: "get",
                url: this.$api.queryHotArticle,
            }).then((res) => {
                this.hotArticleList = res.data.data;
            });
        },
        //获取同类文章
        getSimilarArticle() {
            this.$axios({
                method: "get",
                url: this.$api.querySimilarArticle,
                params: {
                    blogSortUid: this.articleInfo.blogSortUid,
                    uid: this.articleInfo.uid,
                },
            }).then((res) => {
                this.SimilarArticleList = res.data.data;
            });
        },
        //获取个人排行，点赞
        getPersonInfo() {
            this.$axios({
                method: 'get',
                url: this.$api.userQueryUserMessageData,
                params: {
                    userUid: this.newsArticleInfo.uid
                }
            }).then((res) => {
                this.personInfo = res.data.data;
            })
        },
        // 获取评论列表
        getCommentList() {
            this.$axios({
                method: 'post',
                url: this.$api.commentGetList,
                data: this.commentParams
            }).then((res) => {
                this.commentList = res.data.data.records;
                this.commentTotal = res.data.data.total;
            })

        }
    }
}
</script>
<style lang="scss" scoped>
.information_articleDetails {
    padding-top: 64px;
    background: white;
    width: 100%;

    .information_articleDetails_box {
        width: 100%;
    }

    .publicity_one {
        margin-bottom: 48px;
        width: 100%;
        aspect-ratio: 281/156;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .publicity_two {
        margin-top: 48px;
        width: 100%;
        aspect-ratio: 281/156;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
}
</style>
