<template>
  <div class="course-list flex ai-center flex-wrap" id="courseList">
    <div class="course-item flex-col cursor " v-for="(item, index) in dataList" :key="index + 100" @click="toCourse(item)"
      :style="(index + 1) % 4 !== 0 ? 'margin-right:' + getMargin : '0'">
      <img :src="item.pictureUrl" style="width: 192px;height: 140px">
      <div class="course-type">{{ item.type == 2 ? '视频课' : '靶机课' }}</div>
      <div class="flex-col mt-10">
        <div class="art-title ellipsis">
          {{ item.name }}
        </div>

        <div class="art-desc flex ai-center">
          <span class="art-line pr-8">{{ item.releaseTime ? item.releaseTime.substring(0, 10) : '-' }}</span>
          <i class="el-icon-user ml-5"></i>
          <span class="art-line pl-8 pr-8"> {{ item.subscription }}</span>
          <i class="el-icon-chat-line-round ml-5"></i>
          <span class="pl-8"> {{ item.commentNum }}</span>
        </div>

        <div class="color-error font-16">
          <!-- <span v-if="item.sellWay != 0">￥{{ item.sellWay }}</span>
          <span v-else>免费</span> -->


          <span v-if="item.sellWay == 1">
            {{ item.payStatus == 1 ? '已购买' : item.sellingPrice ? "￥" + item.sellingPrice : '' }}
          </span>
          <span v-else class="curri-price">{{ item.sellWay == 0 ? '免费' : item.sellWay == 2 ? '加密' : '指定学员' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      imgBasePath: ''
    }
  },
  computed: {
    getMargin () {
      const el = document.getElementById('courseList')
      const totalWidth = el ? el.offsetWidth : 916
      const margin = (totalWidth - (192 * 4)) / 3
      return margin + 'px' || '25px'
    }
  },
  mounted () {
    this.imgBasePath = localStorage.getItem('picBaseUrl')
  },
  methods: {
    toCourse (item) {
      let path = item.type == 2 ? '/combatCourse/videoCourseDetail' : '/combatCourse/detail'
      this.$openUrl({ path, query: { uid: item.uid, showBre: true } })
    }
  }
}
</script>

<style lang="scss" scoped>
.course-item {
  width: 192px;
  height: 230px;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 24px;
  position: relative;
}

.course-item:hover {
  // box-shadow:0px 0px 12px #e8e8e8;
}

.course-type {
  position: absolute;
  left: 0;
  top: 116px;
  background: rgba(26, 27, 43, 0.3);
  border-radius: 2px 2px 2px 2px;
  font-size: 12px;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 18px;
  padding: 3px 8px;
}

.art-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 24px;
}


.art-desc {
  margin: 5px 0;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  line-height: 18px;
  padding-left: 0 !important;
}

.art-line {
  border-right: 1px solid #E6EAED;
}

.pl-8 {
  padding-left: 8px;
}

.pr-8 {
  padding-right: 8px;
}
</style>