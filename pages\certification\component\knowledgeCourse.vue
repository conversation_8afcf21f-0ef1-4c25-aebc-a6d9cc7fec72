<template>
  <div class="knowledgeCourse">
    <div class="top" v-if="isWangEditor && !!content">
      <div class="over">
        <wangEditor :editorConfig="{ readOnly: true, placeholder: '暂无内容' }"
          :wrapperStyle="{ border: 'none', 'font-size': '14px' }" :onlyEditor="true" height="auto" :content="content">
        </wangEditor>
      </div>
    </div>
    <div class="top-empty" v-else>
      <el-empty style="margin-top: 20px;" description="暂无数据"></el-empty>
    </div>
    <div class="center mt-20">
      <div class="center-top flex-space-between">
        <div class="center-top-right flex-1" v-if="filterList.length > 0">
          <hySubsectionNum width="315px" :fixedItemWidthEnable="true" itemWidth="119px" height="36px"
            :typeSwitchArr="filterList" :typeFlag.sync="active" @change="levelChange" :disabled="false">
          </hySubsectionNum>
          <!-- <hySubsection width="315px" :fixedItemWidthEnable="true" itemWidth="119px" height="36px"
            :typeSwitchArr="filterList" :typeFlag.sync="active" @change="levelChange" :disabled="false">
          </hySubsection> -->
        </div>
        <div class="center-top-letf">
          <el-input style="width:342px" v-model="listPage.name" @clear="initQuery()" placeholder="搜索课程名称" clearable
            @change="initQuery()">
            <i @click="initQuery()" class="el-icon-search el-input__icon icons-ys cursor" slot="suffix"></i>
          </el-input>
        </div>
      </div>
    </div>
    <div class="body mt-20 flex flex-wrap jc-start" v-if="selectList.length > 0">
      <div class="course-item flex-col cursor" v-for="item in selectList" :key="item.uid" @click="toCourseDetail(item)">
        <div class="item-top">
          <img :src="item.info ? item.info.url : ''">
        </div>
        <div class="item-bottom flex-col">
          <p class="font16">{{ item.name }}</p>
          <div class="flex-space-between mt-20">
            <p v-if="[0].includes(judgeSell(item.info, 0))" class="price">{{ "免费" }}
            </p>
            <p v-if="[1].includes(judgeSell(item.info, 0))" class="price">
              {{ judgeSell(item.info, 3) == 0 ? `￥${judgeSell(item.info, 2)}` : '已购买' }}
              <!-- {{ judgeSell(item.info, 2) ? "￥" +
                            judgeSell(item.info, 2) :
                            "免费" }} -->
            </p>
            <p class="info">{{ judgeSell(item.info, 1) }}人购买</p>
          </div>
        </div>
      </div>
    </div>
    <div class="body" v-else>
      <el-empty style="margin-top: 20px;" description="暂无数据" :image-size="100"></el-empty>
    </div>
    <!-- 分页 -->
    <div class="pagination">
      <span class="pagination-desc">共 {{ total }} 条</span>
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="listPage.currentPage" :page-size="listPage.pageSize" layout="prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  components: {

  },
  data() {
    return {
      isWangEditor: false,
      total: 0,
      active: 1,
      content: '',
      // filterList: [{ label: '靶机实战', id: 1 }, { label: '系列课', id: 2 }, { label: '单品课', id: 4 }, { label: '直播', id: 3 }],
      filterList: [{ label: '靶机实战', id: 1, name: '靶机实战', num: '' }],
      selectList: [],
      listPage: {
        contentType: 1,
        curriculumName: '',
        currentPage: 1,
        pageSize: 8,
        name: ''
      },
    }
  },
  computed: {

  },
  props: {
    uid: {
      type: String,
      default: ''
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    //跳转到视频详情
    toCourseDetail(item) {
      this.$emit('detail', { ...item?.info, active: this.active })
      // 学习页面跳转
      if (this.$route.path == '/certification/careerDetail') {
        this.$store.commit('setCourseBreadcrumb', [{ label: '学习' }, { label: '职业规划' }]);
        // let param = JSON.parse(JSON.stringify(this.$route.query))
        // param.type = 'careerKnowledge'
        this.$router.push({
          path: '/combatCourse/detail',
          // query: { uid: item?.info.uid, flag: true, originMsg: JSON.stringify(param) }
          query: { uid: item?.info.uid }
        })
      }
    },
    init() {
      this.$nextTick(() => {
        this.$debounce(() => {
          this.getTrainKnowledgeDetail()
        }, 200)();
      })
    },
    //判断是否售卖 0免费 1付费 2指定学员 3密码
    judgeSell(info, num) {
      if (num === 0) return info?.sellWay || 0
      if (num === 1) return info?.buyNum || 0
      if (num === 2) return info?.sellingPrice || 0
      if (num === 3) return info?.payStatus || 0
    },
    handleSizeChange(val) {
      this.listPage.pageSize = val;
      this.initDataList();
    },
    handleCurrentChange(val) {
      this.listPage.currentPage = val;
      this.getTrainKnowledgeDetail();
    },
    //获取知识点分类详情
    async getTrainKnowledgeDetail() {
      const { uid, listPage } = this
      try {
        this.content = ''
        const { data } = await this.$axios.post(this.$api.gerTrainKnowledgeInfo, { uid, ...listPage })
        if (data.code === 200) {
          const { records = [], total, content } = data.data || {}
          this.content = content == '<br/>' ? '' : content
          this.selectList = records
          this.total = total
          this.isWangEditor = true
          this.filterList.forEach(item => {
            if (item.id == this.active) {
              if (listPage.name == '') {
                item.num = `(${total})`
              } else {
                item.num = total === 0 ? '' : `(${total})`
              }
            }
          })
          // console.log('课程描述1', this.content)
          // console.log('课程分类', this.filterList)
        }
      } catch (err) {
        // console.log('获取知识分类详情失败', err)
      }
    },
    //切换seticon
    levelChange(v) {
      this.active = v.id
      this.selectList = []
      this.total = 0
      this.listPage.contentType = v.id
      this.getTrainKnowledgeDetail()
    },
    // 初始化查询
    initQuery() {
      this.listPage.currentPage = 1
      this.selectList = []
      this.getTrainKnowledgeDetail()
    },
  },
}
</script>

<style lang="scss" scoped>
.knowledgeCourse {
  .top-empty {}

  .top {
    padding: 20px 8px 20px 20px;
    background-color: #F4F6F7;
    border-radius: 8px;
    color: #666666;

    .over {
      overflow-x: auto;
      height: 160px;

      /deep/ .w-e-text-container {
        background-color: #F4F6F7;
      }

      // 滚动条整体样式
      &::-webkit-scrollbar {
        width: 4px !important;
        /* 纵向滚动条 宽度 */
        border-radius: 5px;
        /* 整体 圆角 */
      }

      //轨道部分
      &::-webkit-scrollbar-track {
        background: #F4F6F7 !important;
      }

      // 滑块部分
      &::-webkit-scrollbar-thumb {
        background: #D4DBE0;
        min-height: 64px;
        width: 2px !important;
        border-radius: 5px;
      }

      scrollbar-width: thin !important;
      /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      -ms-overflow-style: none !important;

      /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
    }

  }

  .center {
    .center-top {
      .center-top-right {
        /deep/ .typeSwitchBox {
          span {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }

  }

  .body {

    .course-item {
      width: 22.8%;
      height: 273px;
      margin-right: 20px;
      margin-bottom: 20px;

      .item-top {
        height: 172px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .item-bottom {
        border: 1px solid #E6EAED;
        border-radius: 0px 0px 4px 4px;
        height: 101px;
        padding: 12px 17px 16px 17px;

        .price {
          color: #FF564D;
          font-size: 18px;
          font-weight: 700;
        }

        .info {
          font-size: 12px;
          font-weight: 400;
          color: #999999;
        }
      }

    }
  }

  .pagination {
    margin-top: 41px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .pagination-desc {
      font-size: 14px;
      color: #999999;
    }
  }
}
</style>