<template>
    <div class="exam-container" v-loading="loading">
        <template v-if="step == 1 || step == 3">
            <!-- 头部 -->
            <div class="head" :style="mode == 1 ? {
                height: 'auto',
                background: 'transparent',
                boxShadow: 'none'
            } : {
                background: '#FFFFFF',
                boxShadow: '0px 5px 10px 0px rgb(26 27 43 / 5%)'
            }">
                <div class="head-info" :style="mode == 1 ? {
                    minHeight:'0'
                } : {
                    minHeight:'0px'
                }">
                    <div class="breadcrumb">
                        <examBreadcrumb :breadcrumbArr="[
                            {
                                name: '认证培训',
                                path: '/certification/home'
                            },
                            {
                                name: $route.query.className,
                                path: `/certification/${$route.query.type == 1?'myTraining':'certificateDetail'}?uid=` + ($route.query.classUid || $route.query.cu)
                            },
                            {
                                name: (mode == 1 ? $store.state.examInfo.exam && $store.state.examInfo.exam.examName : currentPracticeTopicInfo.pname) || '--',
                                path: ''
                            }
                        ]"></examBreadcrumb>
                    </div>
                </div>
            </div>
            <!-- 考试答题区域 -->
            <paperExamCertificateWrong v-if="step != 3" :mode="mode"></paperExamCertificateWrong>
            <!-- 练习交卷 -->
            <template v-if="step ==3">
                <handInExaminationPaper></handInExaminationPaper>
            </template>
        </template>
        <template v-if="step == 2">
            <!-- 模拟考试交卷 -->
            <visualExamHandInPaper :showPagination="1" :examInfo="examRelatedInfo"></visualExamHandInPaper>
        </template>
    </div>
</template>
<script>
export default {
    provide() {
        return {
            examThis:this
        }
    },
    data() {
        return {
            scoreModel: {
                total: 0,
                page: 1,
                pageSize: 3
            },
            loading:false,
            step:1,//1答题页 2交卷页
            mode:2,//考试模式
            rateStar: 3.7,
            examRelatedInfo: {}
        }
    },
    watch:{
        '$route': {
            handler: function (newVal, oldVal) {
                this.$nextTick(() => {
                    this.step = this.$route.query.step || 1;
                });
            },
            immediate: true,
            deep:true
        }
    },
    async mounted() {
        // 考试模式
    },
    computed: {
        currentPracticeTopicInfo() {
            return this.$store.state.currentPracticeTopicInfo;
        }
    },
    methods: {

        async getExamRelatedInfo(flag=1) {
            this.loading = true;
            let result = await this.$axios.post(this.$api.getExamRelatedInfo, {
                uid: this.$route.query.uid,
                classUid: this.$route.query.classUid,
                currentPage: this.scoreModel.page,
                pageSize: this.scoreModel.pageSize
            });
            if (result.data.code == this.$code.SUCCESS) {
                this.examRelatedInfo = result.data.data;
                this.scoreModel.total = result.data.data.total;
                if (flag == 1) {
                    if (this.examRelatedInfo.examStatus == 0) {
                        this.$message({
                            message: this.examRelatedInfo.reason,
                            type: 'warning'
                        });
                        this.$router.push({
                            path: '/certification/trainExam',
                            query: { uid: this.$route.query.classUid }
                        })
                    }
                }
            }
            this.loading = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.exam-container {
    .head {
        border-top: 1px solid #F4F6F7;
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0px 5px 10px 0px rgba(26, 27, 43, 0.05);
        border-radius: 0px 0px 0px 0px;

        .head-info {
            min-height: 280px;
            padding-bottom: 20px;
            width: 1180px;
            margin: 0 auto;

            .class-info {
                display: flex;

                .cover {
                    margin-right: 28px;
                    width: 200px;
                    height: 150px;
                    border-radius: 8px 8px 8px 8px;
                    overflow: hidden;
                    img{
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .right {
                    .title {
                        width: 974px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        word-break: break-all;
                        font-size: 28px;
                        font-weight: normal;
                        color: #333333;
                    }

                    .desc {
                        word-break: break-all;
                        margin: 8px 0 17px 0;
                        font-size: 14px;
                        font-weight: normal;
                        width: 680px;
                        line-height: 22px;
                        color: #666666;
                    }

                    .finish-info {
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                        font-weight: normal;
                        color: #666666;
                        .difficulty{
                            padding: 5px 16px;
                            display: flex;
                            align-items: center;
                            min-width: 168px;
                            height: 32px;
                            background: #F5F8FC;
                            border-radius: 4px 4px 4px 4px;
                            .txt{
                                margin-right: 12px;
                            }
                        }
                        .complete-count{
                            padding: 5px 16px;
                            min-width: 141px;
                            height: 32px;
                            background: #F5F8FC;
                            border-radius: 4px 4px 4px 4px;
                            .complete-desc{
                                margin-right: 12px;
                            }
                            .count{
                                color: #006EFF;
                                font-weight: bold;
                            }
                            margin-left: 20px;
                        }
                    }
                }
            }

            .breadcrumb {
                width: 1180px;
                margin: 40px auto 40px auto;

                .el-breadcrumb {
                    /deep/ .el-breadcrumb__item {
                        .el-breadcrumb__inner {
                            font-size: 14px;
                            font-weight: normal !important;
                            color: #999999 !important;
                        }
                    }
                }
            }
        }
    }
}
</style>