<template>
    <div class="lectureRoomDetail">
        <div class="top w-100">
            <div class="w-100 flex ai-center" style="padding: 0 0 32px 0;">
                <live-carousel :list="liveList" :index="currentIndex" :switchShow="switchShow"
                    @changeCard="changeCard"></live-carousel>
            </div>
        </div>
        <div class="content flex ai-center jc-center">
            <div v-if="livaData" class="content-body flex">
                <div class="content-left flex-1">
                    <hyTabs2 :list="typeList" :keyLabel="'name'" @changeIndex="changeLive" contentStyle="margin: 0" />
                    <div class="font-14 f-w-400 mt-30" v-html="livaData.content || ''" v-if="typeFlag == 1">
                    </div>
                    <div class="left-poster mt-20" v-if="typeFlag == 2">
                        <img style="width: 100%;min-height: 500px;" :src="livaData.livePosterPath">
                    </div>
                </div>
                <div class="content-right flex-col pt-10">
                    <p class="font18 f-w-500" style="color: #333333;">关注Ta</p>
                    <div class="peroson-photo mt-20">
                        <img class="w-100 h-100"
                            :src="livaData.classroomSpeaker ? livaData.classroomSpeaker.picturePath : ''">
                    </div>
                    <div class="flex-center-col">
                        <p class="font18 f-w-500 mt-20" style="color: #333333;">{{ livaData.classroomSpeaker ?
                            livaData.classroomSpeaker.name : '' }}</p>
                        <div class="flex ai-center font14 f-w-400 mt-10" style="color: #666666;">
                            <p>{{ livaData.classroomSpeaker ? livaData.classroomSpeaker.categoryName : '' }}</p>
                            <p class="ml-10">{{ livaData.classroomSpeaker ? livaData.classroomSpeaker.intro : '' }}</p>
                        </div>
                        <div class="mt-20">
                            <div class="content-html" style="height: 200px; width: 280px;color: #666666;overflow: auto;"
                                v-html="livaData.classroomSpeaker ?
                                    livaData.classroomSpeaker.content : ''">
                            </div>
                        </div>
                        <div v-if="livaData.liveType == 1">
                            <div class="attention flex-center mt-20 cursor"
                                v-if="livaData.classroomSpeaker.isSubscribed == 0"
                                @click="followingPerson(livaData.speakerUid)">
                                <i class="el-icon-plus" style="color: #006EFF;"></i>
                                <p class="font14 f-w-400" style="margin-left: 8px;">关注</p>
                            </div>
                            <div class="un-attention flex-center mt-20 cursor"
                                v-if="livaData.classroomSpeaker.isSubscribed == 1"
                                @click="unFollowingPerson(livaData.speakerUid)">
                                <p class="font14 f-w-400" style="margin-left: 8px;">已关注</p>
                            </div>
                        </div>
                        <div v-if="livaData.liveType == 2">
                            <div class="attention flex-center mt-20 cursor"
                                v-if="livaData.classroomEnterprise.isSubscribed == 0"
                                @click="followingEnterprise(livaData.classroomEnterprise.uid)">
                                <i class="el-icon-plus" style="color: #006EFF;"></i>
                                <p class="font14 f-w-400" style="margin-left: 8px;">关注</p>
                            </div>
                            <div class="un-attention flex-center mt-20 cursor"
                                v-if="livaData.classroomEnterprise.isSubscribed == 1"
                                @click="unFollowingEnterprise(livaData.classroomEnterprise.uid)">
                                <p class="font14 f-w-400" style="margin-left: 8px;">已关注</p>
                            </div>
                        </div>
                        <div class="advertising mt-40">
                            <img class="w-100 h-100" :src="livaData.qrCodePath">
                        </div>
                    </div>
                </div>
            </div>
            <el-empty v-else :image="require('@/assets/images/certification/empty.png')" description="暂无数据"></el-empty>
        </div>
        <el-dialog :visible.sync="dialogPlayback" :before-close="handleClose">
            <video v-if="playbackPath" width="100%" ref="vueMiniPlayer" height="100%" controls>
                <source :src="playbackPath" type="video/mp4" />
            </video>
        </el-dialog>
        <div class="qrcode-body w-100 h-100" @click.self="qrcodeShow = false" v-show="qrcodeShow">
            {{ qrCodePath }}
            <div class="qrcode-box" v-if="qrcodeShow">
                <img class="w-100 h-100" :src="qrCodePath">
            </div>
        </div>
    </div>
</template>

<script>
import isEmpty from 'lodash/isEmpty'
import HySubsection from '../../components/common/hySubsection.vue'
import LiveCarousel from './component/liveCarousel.vue'
export default {
    name: '',
    components: {
        HySubsection,
        LiveCarousel
    },
    data() {
        return {
            qrCodePath: '',
            qrcodeShow: false,//二维码弹窗
            playbackPath: '',
            dialogPlayback: false,
            switchShow: false,
            typeFlag: 1,
            typeList: [
                { uid: 1, name: '直播详情' },
                { uid: 2, name: '直播海报' }
            ],
            liveList: [],
            livaData: {
                isSubscribed: 0,
                classroomSpeaker: {
                    isSubscribed: 0,
                }
            },
            currentIndex: 0,
        }
    },
    computed: {

    },
    created() {
        this.getDetailInfo()
    },
    methods: {
        //卡片按钮操作
        changeCard(item) {
            console.log('卡片按钮操作', item)
            const { liveStatus } = item
            //1待开播 判断是否预约过 存放直播间uid 
            const liveOne = () => {
                //如果没有登录才进行缓存Id
                if (this.$store.getters.isLogin) {
                    item.isSubscribed == 0 ? this.followingAppointmentLive(item.uid) : this.$message.success('已预约过直播间')
                } else {
                    let cachedData = JSON.parse(localStorage.getItem('liveuis')) || [];
                    if (cachedData.includes(item.uid)) {
                        this.$message.success('已预约过直播间')
                        return
                    } else {
                        this.followingAppointmentLive(item.uid)
                        cachedData.push(item.uid)
                        window.localStorage.setItem('liveuis', JSON.stringify(cachedData))
                    }
                }
            }
            //2即将开播 如果有直播群二维码就弹出来
            const liveTwo = () => {
                if (item.grCodePath) {
                    this.qrCodePath = item.qrCodePath
                    this.qrcodeShow = true
                }
            }
            //3直播中 有直播链接就跳，没有的话就弹出二维码
            const liveThree = () => {
                if (item.liveAddress) {
                    window.open(item.liveAddress)
                } else {
                    if (item.qrCodePath) {
                        this.qrCodePath = item.qrCodePath
                        this.qrcodeShow = true
                    }
                }
            }
            //5 已结束 有回放就弹出回放视频
            const liveFive = () => {
                if (item.playbackPath) {
                    this.playbackPath = item.playbackPath;
                    this.dialogPlayback = true;
                } else {
                    this.$message.warning('暂无回放视频')
                }
            }
            switch (liveStatus) {
                case 1:
                    liveOne()
                    break;
                case 2:
                    liveTwo()
                    break;
                case 3:
                    liveThree()
                    break;
                case 5:
                    liveFive()
                    break;
            }
        },
        //获取详情
        async getDetailInfo() {
            let result = await this.$request.lectureHall.getLiveInfo({ liveUid: this.$route.query?.liveUid });
            if (result.data.code == this.$code.SUCCESS) {
                const { data } = result.data
                if (!this.$store.getters.isLogin) {
                    //判断是否预约过
                    let cachedData = JSON.parse(localStorage.getItem('liveuis')) || [];
                    //判断是否关注过讲师
                    let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                    cachedData.includes(data.uid) ? data.isSubscribed = 1 : ''
                    //普通讲者
                    if (data.liveType == 1) {
                        teachData.includes(data.speakerUid) ? data.classroomSpeaker.isSubscribed = 1 : 0
                    }
                    //企业
                    if (data.liveType == 2) {
                        teachData.includes(data.classroomEnterprise.uid) ? data.classroomEnterprise.isSubscribed = 1 : 0
                    }
                }
                this.liveList = [data]
                this.livaData = data
                this.currentIndex = 0
            }
        },
        handleClose(done) {
            this.playbackPath = ''
            done();
        },
        //关注企业
        followingEnterprise(enterpriseUid) {
            this.$request.lectureHall.followingEnterprise({ enterpriseUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('关注成功')
                    this.livaData.classroomEnterprise.isSubscribed = 1
                    //如果没登录就缓存
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.push(this.livaData.classroomEnterprise.uid)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                }
            })
        },
        //取消关注企业
        unFollowingEnterprise(enterpriseUid) {
            this.$request.lectureHall.unFollowingEnterprise({ enterpriseUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('已取消关注~')
                    this.livaData.classroomEnterprise.isSubscribed = 0
                    //取消缓存数据  
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.splice(teachData.indexOf(this.livaData.classroomEnterprise.uid), 1)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                }
            })
        },
        //取消关注讲师
        unFollowingPerson(speakerUid) {
            this.$request.lectureHall.getLiveUnFollowing({ speakerUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('已取消关注~')
                    this.livaData.classroomSpeaker.isSubscribed = 0
                    //取消缓存数据  
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.splice(teachData.indexOf(this.livaData.speakerUid), 1)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                }
            })
        },
        //关注讲师
        followingPerson(speakerUid) {
            this.$request.lectureHall.getLiveFollowing({ speakerUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('关注成功')
                    this.livaData.classroomSpeaker.isSubscribed = 1
                    //如果没登录就缓存
                    if (!this.$store.getters.isLogin) {
                        let teachData = JSON.parse(localStorage.getItem('livetaech')) || [];
                        teachData.push(this.livaData.speakerUid)
                        window.localStorage.setItem('livetaech', JSON.stringify(teachData))
                    }
                }
            })
        },

        //判断是否关注直播间
        judgeLiveRomm(item) {
            if (item.isSubscribed == 0) this.followingAppointmentLive(item.uid)
            if (item.isSubscribed == 1) this.upFollowingAppointmentLive(item.uid)
        },
        //预约直播
        followingAppointmentLive(liveUid) {
            this.$request.lectureHall.speakerFollow({ liveUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('预约成功')
                    this.getDetailInfo()
                }
            })
        },
        //取消预约直播
        upFollowingAppointmentLive(liveUid) {
            this.$request.lectureHall.cancelSpeakerFollow({ liveUid }).then(res => {
                if (res.data.code == this.$code.SUCCESS) {
                    this.$message.success('取消成功')
                    this.getDetailInfo()
                }
            })
        },
        changeLive(val) {
            console.log('切换tab', val)
            this.typeFlag = val.uid
        }
    },
}
</script>

<style lang="scss" scoped>
.qrcode-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.qrcode-body {
    position: fixed;
    width: 100vw;
    height: 100vh;
}

.lectureRoomDetail {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .top {
        background-color: #F5F8FC;
        height: 312px;
    }

    .content {
        width: 100%;
        background: #FFFFFF;
        margin-top: 30px;
        padding: 30px 0 30px 0;

        .content-body {
            padding-bottom: 80px;
            width: 1180px;

            .content-left {
                width: 820px;
                padding-right: 40px;

                .content-item {
                    width: 100%;

                    .item-title {
                        background: #F5F8FC;
                        height: 30px;
                        line-height: 30px;
                        color: #333333;
                    }

                    .item-info {
                        padding: 20px;
                        color: #666666;
                    }
                }

                /deep/ .tab-item {
                    background: #F4F6F7;
                    border-radius: 20px;
                    color: #666666;
                    margin: 15px 15px 20px 0;

                    .box {
                        padding: 5px 16px 5px 16px;
                        font-size: 14px;
                        font-weight: 400;
                    }
                }

                /deep/ .tab-actitem {
                    background: #006EFF !important;
                    color: #fff !important;
                }
            }

            .content-right {
                width: 320px;
                border-left: 1px solid #E6EAED;
                padding-left: 40px;
                scrollbar-width: none;

                .content-html {

                    // 滚动条整体样式
                    &::-webkit-scrollbar {
                        display: none !important;
                        width: 4px !important;
                        /* 纵向滚动条 宽度 */
                        border-radius: 5px;
                        /* 整体 圆角 */
                    }

                    //轨道部分
                    &::-webkit-scrollbar-track {
                        background: #fff !important;
                    }

                    // 滑块部分
                    &::-webkit-scrollbar-thumb {
                        background: #D4DBE0;
                        min-height: 167px;
                        width: 2px !important;
                        border-radius: 5px;
                    }

                    scrollbar-width: thin !important;
                    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
                    -ms-overflow-style: none !important;
                    /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */

                }

                .peroson-photo {
                    width: 280px;
                    height: 373px;
                }

                .attention {
                    width: 200px;
                    height: 36px;
                    border-radius: 4px;
                    border: 1px solid #006EFF;
                    color: #006EFF;
                }

                .un-attention {
                    width: 200px;
                    height: 36px;
                    border-radius: 4px;
                    border: 1px solid #006EFF;
                    color: #006EFF;
                }

                .advertising {
                    width: 280px;
                    height: 158px;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }


    }
}
</style>