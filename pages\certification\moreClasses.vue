<template>
  <div class="open-class-plan-container">
    <div class="head-form mb-20">
      <div class="head-body w-1440">
        <div class="flex">
          <div
            class="mr-24 color-333 f-w-4 font-14 fa-55 flex jc-end mt-4"
            style="min-width: 56px"
          >
            培训证书
          </div>
          <div
            class="flex ai-center"
            :class="expand ? 'certificate2' : 'certificate'"
            style="flex-wrap: wrap"
          >
            <div
              @click="certificateType({ uid: '' })"
              :class="
                classSearchForm.certificateUid == ''
                  ? 'mr-10 tab-btn mb-10 selected-btn cursor'
                  : 'tab-btn mr-10 mb-10 cursor'
              "
            >
              全部
            </div>
            <div
              @click="certificateType(item)"
              :class="
                classSearchForm.certificateUid == item.uid
                  ? 'mr-10 tab-btn mb-10 selected-btn cursor'
                  : 'tab-btn mr-10 mb-10 cursor'
              "
              v-for="(item, index) in Tags"
              :key="index"
            >
              {{ item.shortName }}
            </div>
            <div class="more" v-if="!expand" @click="expand = true">
              展开<i class="el-icon-caret-bottom"></i>
            </div>
            <div class="more" v-if="expand" @click="expand = false">
              收起<i class="el-icon-caret-top"></i>
            </div>
          </div>
        </div>
        <div class="flex ai-center mb-10">
          <div
            class="mr-24 color-333 f-w-4 font-14 fa-55 flex ai-center jc-end"
            style="min-width: 56px"
          >
            学习方式
          </div>
          <div class="flex ai-center">
            <div
              @click="classTabClick(item)"
              :class="
                classSearchForm.goToClassway == item.value
                  ? 'mr-10 tab-btn selected-btn cursor'
                  : 'tab-btn mr-10 cursor'
              "
              v-for="(item, index) in goToClasswayList"
              :key="index"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="flex ai-center mb-10">
          <div
            class="mr-24 color-333 f-w-4 font-14 fa-55 flex ai-center jc-end"
            style="min-width: 56px"
          >
            强化提升
          </div>
          <div class="flex ai-center">
            <div class="mr-10 tab-btn selected-btn cursor">培训班</div>
            <div class="tab-btn mr-10 prohibit">自由训练</div>
          </div>
        </div>
        <div class="flex ai-center mb-10">
          <div
            class="mr-24 color-333 f-w-4 font-14 fa-55 flex ai-center jc-end"
            style="min-width: 56px"
          >
            城市
          </div>
          <el-select
            filterable
            size="mini"
            @change="search"
            style="width:200px;"
            v-model="classSearchForm.city"
            placeholder="城市"
          >
            <el-option label="全部" :value="''"></el-option>
            <el-option
              v-for="(item, index) in citys"
              :key="index"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </div>
        <div class="flex ai-center mb-10">
          <div
            class="mr-24 color-333 f-w-4 font-14 fa-55 flex ai-center jc-end"
            style="min-width: 56px"
          >
            报名时间
          </div>
          <el-date-picker
            v-model="value"
            type="datetimerange"
            :picker-options="pickerOptions"
            size="mini"
            @change='initDataList'
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          >
          </el-date-picker>
        </div>
        <div class="flex ai-center">
          <div
            class="mr-24 color-333 f-w-4 font-14 fa-55 flex ai-center jc-end"
            style="min-width: 56px"
          >
            培训时间
          </div>
          <el-date-picker
            v-model="value2"
            type="datetimerange"
            size="mini"
            :picker-options="pickerOptions"
            range-separator="至"
            @change='initDataList'
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          >
          </el-date-picker>
        </div>
      </div>
    </div>
    <div class="body w-1440">
      <div class="flex ai-center jc-between mb-30">
        <div class="flex ai-center">
          <span class="font-24 color-black f-w-600 mr-10">全部班级</span>
          <img src="@/assets/newVersion/title-2.png" alt="" />
          <span class="ml-20 font-14 f-w-400 fa-55 color-333"
            >共找到
            <span style="color: #006eff" class="f-w-700">{{ total }}</span>
            个结果</span
          >
        </div>
        <el-input
          clearable
          placeholder="请输入班级名称"
          v-model="classSearchForm.courseName"
          @blur="domInputShow = false"
          @focus="domInputShow = true"
          @keyup.enter.native="search"
          @clear="search"
          @input="inputChange"
        >
          <div slot="append" class="immmm" :class="{ active: domInputShow }">
            <i
              slot="suffix"
              class="el-input__icon el-icon-d label-width60 cursor"
              @click="search"
            >
              搜索
            </i>
          </div>
        </el-input>
      </div>

      <div
        style="display: flex; flex-wrap: wrap"
        ref="purchaseItem"
        :style="boxStyle"
      >
        <div
          class="item flex-col cursor"
          v-for="(item, index) in classSearchTable"
          :key="index"
          @click="gotoSignUp(item)"
          :class="{ 'mt-20': index > 3, 'mr-20': (index + 1) % 4 != 0 }"
        >
          <div class="logo_div">
            <img
              v-if="item.fileUrl"
              :src="item.fileUrl"
              alt=""
              class="w-100 h-100"
            />
            <img
              v-else
              src="@/assets/images/huYuanSchool/hot-cover-img1.png"
              alt=""
              class="w-100 h-100"
            />
          </div>
          <div class="pl-8 pr-8 pb-10">
            <div class="mt-8 mb-8 color-black font-16" style="display: flex">
              <div class="ellipsis">{{ item.name }}</div>
            </div>
            <div class="font-14 color-999 f-w-400">
              <div>
                报名时间：<span v-if="item.registrationStartTime">{{
                  item.registrationStartTime.split(" ")[0].replace(/-/g, '.')
                }}</span
                > - <span v-if="item.registrationEndTime">{{
                  item.registrationEndTime.split(" ")[0].replace(/-/g, '.')
                }}</span>
              </div>
            </div>
            <div class="font-14 my-5 color-999 f-w-400">
              <div>
                培训时间：<span v-if="item.trainingStartTime">{{
                  item.trainingStartTime.split(" ")[0].replace(/-/g, '.')
                }}</span
                > - <span v-if="item.trainingEndTime">{{
                  item.trainingEndTime.split(" ")[0].replace(/-/g, '.')
                }}</span>
              </div>
            </div>
            <div class="flex mt-4 ai-center jc-between">
              <div class="flex ai-center">
                <div class="flex ai-center type-tag">
                  <span v-if="item.teachMethod == 1">面授</span>
                  <span v-if="item.teachMethod == 2">直播</span>
                  <span v-if="item.teachMethod == 3">直播+面授</span>
                </div>
                <div
                  class="type-tag ml-8 flex ai-center"
                  style="color: #1a7dff; background: rgb(0 38 247 / 10%)"
                  v-if="item.trainingPlass"
                >
                  {{ item.trainingPlass }}
                </div>
              </div>

              <div class="rob-div cursor">
                {{ item.sellWay == 0 ? "免费" : `￥${item.trainingPrice}元` }}
              </div>
            </div>
          </div>
        </div>
        <div class="flex ai-center jc-center py-50" style="width:100%;">
        <el-empty
          v-if="classSearchTable.length == 0"
          :image="require('@/assets/images/certification/empty.png')"
          description="暂无数据"
        ></el-empty>
        </div>
      </div>
      <div class="pagination" v-if="classSearchTable.length != 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          :current-page="page"
          :page-sizes="[5, 10, 15, 20, 30]"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const start = new Date();
              const end = new Date();
              end.setTime(end.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const start = new Date();
              const end = new Date();
              end.setTime(end.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const start = new Date();
              const end = new Date();
              end.setTime(end.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      loading: false,
      citys: [],
      value2: "",
      value: "",
      classSearchTable: [],
      page: 1,
      pageSize: 8,
      total: 0,
      classSearchForm: {
        openClassTime: "",
        city: "",
        goToClassway: "",
        courseName: "",
        certificateUid: "",
      },
      domInputShow: false,
      goToClasswayList: [
        {
          name: "全部",
          value: "",
        },
        {
          name: "面授",
          value: "1",
        },
        {
          name: "直播",
          value: "2",
        },
        {
          name: "直播+面授",
          value: "3",
        },
      ],
      boxStyle: "",
      Tags: "",
      expand: false,
    };
  },
  mounted() {
    this.initDataList();
    this.initTrainCertificatePageData();
    window.addEventListener("resize", this.liveBroadcastBgResize);
    this.$nextTick(() => {
      this.liveBroadcastBgResize();
    });
  },
  filters: {
    courseStatusFilter: function (val) {
      let str = "";
      if (val == 0 || val == 2) str = "待报名";
      if (val == 1) str = "报名中";
      if (val == 3) str = "培训中";
      if (val == 4) str = "已结束";
      return str;
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.liveBroadcastBgResize);
  },
  methods: {
    // 跳转培训班详情页面
    gotoSignUp(item) {
      // this.$message.warning(this.$store.state.warnMap['wn-2']);
      // return;
      // 未登录
      if (!this.$store.getters.isLogin) {
        this.$confirm("您还未登录，请先登录?", "提示", {
          confirmButtonText: "前往登录",
          cancelButtonText: "再逛逛",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
          })
          .catch(() => {
            // 拒绝前往登录界面
            return;
          });
      } else {
        this.$store.commit("setCourseBreadcrumb", [{ label: "首页" }]);
        this.$router.push({
          path: "/certification/myTraining",
          query: { uid: item.uid },
        });
      }
    },
    liveBroadcastBgResize() {
      let offsetWidth = this.$refs.purchaseItem.offsetWidth;
      this.boxStyle = "--review-item-width:" + (offsetWidth - 61) / 4 + "px;";
    },
    async initTrainCertificatePageData() {
      this.loading = true;
      let params = {
        currentPage: 1,
        pageSize: 999999,
        areaUid: null,
      };
      let result = await this.$axios({
        url: this.$api.getTrainCertificatePage,
        method: "post",
        data: params,
      });
      if (result.data.code === this.$code.SUCCESS) {
        this.Tags = result.data.data.records;
      }
      this.loading = false;
    },
    classTabClick(item) {
      this.classSearchForm.goToClassway = item.value;
      this.page = 1;
      this.initDataList();
    },
    certificateType(item) {
      this.classSearchForm.certificateUid = item.uid;
      this.page = 1;
      this.initDataList();
    },
    handleCurrentChange(val) {
      this.page = val;
      this.initDataList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.initDataList();
    },
    async initDataList() {
      this.loading = true;
      let params = {
        currentPage: this.page,
        pageSize: this.pageSize,
        teachMethod: this.classSearchForm.goToClassway,
        classStartTime: this.classSearchForm.openClassTime,
        trainingPlass: this.classSearchForm.city,
        name: this.classSearchForm.courseName,
        recent:0,
        certificateUid: this.classSearchForm.certificateUid,
        registrationStartTime: this.value ? new Date(this.value[0]).getTime() : '',
        registrationEndTime: this.value ? new Date(this.value[1]).getTime() : '',
        trainingStartTime: this.value2 ? new Date(this.value2[0]).getTime() : '',
        trainingEndTime: this.value2 ? new Date(this.value2[1]).getTime() : '',
      };
      this.$request.career.getPageListByCer(params).then((result) => {
        this.citys = result.data.data.city;
        this.total = result.data.data.total;
        this.classSearchTable = result.data.data.records;
      });
      this.loading = false;
    },
    inputChange(val) {
      this.classSearchForm.courseName = val.replace(/\_/g, "");
    },
    search() {
      this.page = 1;
      this.initDataList();
    },
    // 跳转培训班详情页面
    gotoSignUp(item) {
      // this.$message.warning(this.$store.state.warnMap['wn-2']);
      // return;
      // 未登录
      if (!this.$store.getters.isLogin) {
        this.$confirm("您还未登录，请先登录后再报名?", "提示", {
          confirmButtonText: "前往登录",
          cancelButtonText: "再逛逛",
          type: "warning",
        })
          .then(() => {
            this.$router.push("/login");
          })
          .catch(() => {
            // 拒绝前往登录界面
            return;
          });
      } else {
        this.$store.commit("setCourseBreadcrumb", [{ label: "首页" }]);
        this.$router.push({
          path: "/certification/myTraining",
          query: { uid: item.uid },
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.open-class-plan-container {
  width: 100%;
  margin: 0 auto;
  background: #fff;
  // width: 1200px;
  .head-form {
    background: url("@/assets/certification/bk.png") no-repeat;
    background-size: 100% 100%;
    padding: 27px 0;
    width: 100%;
    min-width: 1440px;
    /deep/ .el-form {
      .el-form-item {
        .el-form-item__label {
          color: $color666;
          font-size: 14px;
        }

        .el-form-item__content {
          width: 160px;
        }
      }
    }
  }
  .head-body {
    margin: 0 auto;
  }
  .el-input:hover {
    border: 1px solid #006eff;
  }

  .el-input {
    width: 300px !important;
    height: 30px !important;
    border-radius: 3px;
    border: 1px solid #c0c4cc;

    /deep/.el-input__inner {
      // border: 1px solid #C0C4CC;
      border: 0px !important;
      background-color: #fff !important;
      font-size: 14px !important;
      height: 30px;
    }

    // /deep/.el-input__inner:hover {
    //     // border: 1px solid #448ace
    // }

    /deep/.el-input-group__append {
      border: 0px !important;
      // background-color: #fff;
      // color: #fff;
      padding: 0 !important;
      // border-bottom-right-radius: 3px;
      //  border-top-right-radius: 3px;
    }
  }
  .immmm {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
    color: #fff;
    background-color: #0062ff;
    height: 30px;
    .el-input__icon {
      line-height: 30px !important;
      font-size: 12px;
    }
  }

  /deep/.immmm.active {
    border: 0px !important;
    background-color: #0062ff;
    color: #fff;
  }
  .body {
    margin: 0 auto;
    padding: 20px;
    min-height: 300px;
    border-radius: 8px;
    background: white;

    .pagination {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }

    .table {
      .class-status {
        padding: 2px 8px;
        display: inline-block;
        border-radius: 4px;
        color: $colorMain;
        background: rgba(0, 110, 255, 0.1);
      }

      .price {
        font-size: 14px;
        color: #ff564d;
        font-weight: 700;
      }
    }
  }
  .item {
    background: linear-gradient(135deg, #f2f7fc 0%, #f9fbfc 100%);
    box-shadow: 0px 4px 10px 0px rgba(55, 99, 170, 0.1);
    border-radius: 4px;
    opacity: 1;
    border: 2px solid #ffffff;
    overflow: hidden;
    width: var(--review-item-width);
    .logo_div {
      height: calc(var(--review-item-width) * (156 / 278));
    }
    .type-tag {
      color: #f77900;
      font-size: 12px;
      font-weight: 400;
      font-family: Alibaba PuHuiTi 2-55 Regular, Alibaba PuHuiTi 20;
      padding: 3px 8px;
      background: rgba(247, 121, 0, 0.1);
      border-radius: 8px 8px 8px 8px;
      max-width: 90px;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      -webkit-box-orient: vertical;
    }
    .rob-div {
      font-size: 14px;
      font-family: Alibaba PuHuiTi 2-85 Bold, Alibaba PuHuiTi 20;
      font-weight: 700;
      color: #ff564d;
    }
  }
}
.selected-btn {
  background: linear-gradient(90deg, #33e4ff 0%, #006eff 100%);
  box-shadow: 0px 2px 5px 0px rgba(55, 99, 170, 0.1),
    inset 0px 2px 10px 0px rgba(0, 17, 124, 0.302);
  border-radius: 20px 20px 20px 20px;
  opacity: 1;
  border: 1px solid #ffffff;
  padding: 4px 16px;
  font-size: 14px;
  font-family: Alibaba PuHuiTi 2-65 Medium, Alibaba PuHuiTi 20;
  font-weight: 500;
  color: #ffffff !important;
}
.tab-btn {
  padding: 4px 16px;
  font-family: Alibaba PuHuiTi 2-55 Regular, Alibaba PuHuiTi 20;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}
.prohibit {
  cursor: not-allowed;
}
.mt-4 {
  margin-top: 4px;
}

.certificate {
  height: 36px;
  position: relative;
  overflow: hidden;
}
.certificate2 {
  position: relative;
  overflow: hidden;
}
.more {
  position: absolute;
  right: 0;
  top: 5px;
  font-size: 12px;
  color: #0062ff;
  cursor: pointer;
}
::v-deep {
  .el-input__suffix{
    display: flex;
    align-items: center;
  }
}
</style>