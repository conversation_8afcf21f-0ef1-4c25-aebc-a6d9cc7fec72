<template>
  <div v-if="transitionPage" class="page">
    <!-- 背景图 -->
    <el-image
      :key="loginBg"
      v-if="loginBg"
      class="login-bg"
      :src="loginBg"
      fit="cover"
    ></el-image>
    <!-- logo -->
    <img class="login-logo cursor" v-if="webConfig" :src="webConfig.logoPhoto" @click="$router.push('/')">
    <!-- 登录注册浮窗 -->
    <loginSection v-if="activeSection == 'loginSection'"></loginSection>
    <!-- 找回密码区域 -->
    <forgetPwd v-if="activeSection == 'forgetPwd'"></forgetPwd>
    <!-- 设置密码区域 -->
    <setPwd v-if="activeSection == 'setPwd'" :phone="phone"></setPwd>
    <!-- 异地登录验证弹窗 -->
    <verifyDialog></verifyDialog>
    <!-- 微信登录未绑定手机弹窗 -->
    <bindDialog v-if="bindWxShow" :visible="bindWxShow"></bindDialog>
    <!-- 微信注册用户未设置密码，却用密码登录时提示弹窗 -->
    <wxNopwdTipDialog></wxNopwdTipDialog>
    <!-- 老用户重置密码弹窗 -->
    <resetDialog></resetDialog>
    <!-- 邮箱用户未绑定手机号弹窗 -->
    <bindMobile></bindMobile>
    <!-- 邮箱用户绑定手机号成功提示弹窗 -->
    <tipDialog></tipDialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeSection: "loginSection",
      bindWxShow: false,
      preRouter: "", // 上个界面的路由
      preRouterQuery: "", // 上个界面的路由query参数
      preRouterParams: "", // 上个界面的路由params参数
      phone: "", // 传至设置密码区域
      loginBg: "",
      webConfig:null,
      transitionPage: false
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例,preRouter
      vm.preRouter = from.path;
      vm.preRouterQuery = from.query
      vm.preRouterParams = from.params
      //console.log("上个页面路由地址：---", vm.preRouter,"query参数：---", vm.preRouterQuery, "params参数：---", vm.preRouterParams);
    });
  },
  created() {
    this.getLoginBg();
    this.getWebConfigInfo()
    // 切换忘记密码
    this.$bus(this).$on("forgetPwd", () => {
      //console.log("forgetPwd");
      this.activeSection = "forgetPwd";
    });
    // 切换设置密码
    this.$bus(this).$on("setPwd", (phone) => {
      //console.log("setPwd", phone);
      this.phone = phone;
      this.activeSection = "setPwd";
    });
    // 找回密码-返回登录
    this.$bus(this).$on("backLogin", () => {
      //console.log("backLogin");
      this.activeSection = "loginSection";
    });
    // 设置密码-返回忘记密码
    this.$bus(this).$on("backForget", () => {
      //console.log("backForget");
      this.activeSection = "forgetPwd";
    });
    // 登录成功
    this.$bus(this).$on("loginSuccess", () => {
      //console.log("loginSuccess");
      this.loginPageBack()
    });
  },
  async mounted() {
    await this.$nextTick();
    // 已登陆
    if (this.$store.state.isLogin) {
      //console.log("已经登录");
      // 返回指令者学堂
      this.$router.replace('/lectureHall');
    }
    // 微信扫码重定向至本页面监听
    let query = this.$route.query;
    if (query.code && query.state) {
      this.$axios({
        method: "get",
        url: this.$api.wxScanCodeCallback,
        params: { code: query.code, state: query.state },
      }).then((res) => {
        let status = res.data.code;
        if (status == this.$code.SUCCESS) {
          // 微信扫码成功，并且已绑定手机，直接跳转正常页面
          this.$store.commit("changeUserInfo", res.data.data.userInfo);
          this.$store.commit("changeIsLogin", true);
          //保存用户鉴权信息
          this.$store.commit('setAuthenticationInfo',{
            userInfo:res.data.data.userInfo,
            token:res.data.data.Authorization
          });
          localStorage.setItem('token', res.data.data.Authorization)
          // localStorage.setItem('userInfo', JSON.stringify(res.data.data.userInfo))
          this.$store.commit("changeUserInfo", res.data.data.userInfo);
          this.loginPageBack()
          this.getUserInfo()
        } else if (status == this.$code.ERROR_DIALOG) {
          // 微信扫码成功，未绑定手机，显示绑定弹窗
          this.bindWxShow = true;
          this.$store.commit("changeWxopenid", res.data.data.wechatInfo.openId);
        } else {
          // 异常情况
          this.$message.warning(res.data.message);
        }
      });
    }
    setTimeout(() => {
      this.transitionPage = true
    }, 1500)
  },
  beforeDestroy() {
        if(this.$store.state.loginAfterRoutePath) {
            this.$store.commit('setLoginAfterRoutePath','')
        }
    },
  methods: {
    // 登录成功页面跳转
    loginPageBack() {
      if(this.$store.state.loginAfterRoutePath) { // 判断是否有登录成功后跳转至指定的页面。目前在在线企业实验室用到
        this.$router.push({
          path: this.$store.state.loginAfterRoutePath
        })
        return
      }
      if (this.preRouter && this.$store.state.loginRouter.indexOf(this.preRouter) == -1) {
        if(Object.keys(this.preRouterQuery).length) {
          this.$router.push({path: this.preRouter, query: this.preRouterQuery});
        } else if(Object.keys(this.preRouterParams).length) {
          this.$router.push({path: this.preRouter, params: this.preRouterParams});
        } else {
          this.$router.push(this.preRouter)
        }
      } else {
        this.$router.push("/");
      }
    },
    getUserInfo () {
      this.$axios({
          method: "get",
          url: this.$api.userQueryMessage,
          params: { token: localStorage.getItem('token') }
      }).then(res => {
          if (res.data.code == 200) {
              const userInfo = { ...this.$store.state.userInfo, ...res.data.data, userName: this.$store.state.userInfo.userName };
              this.$store.commit("changeUserInfo", JSON.parse(JSON.stringify(userInfo)));
          }
      }).catch(err => {
          // Message.warning( JSON.stringify(err));
      });
    },
    // 获取背景图
    getLoginBg() {
      this.$axios({
        method: "get",
        url: this.$api.getPictureUrl,
        params: {pictureSortUid: '4ff13134b0a6ff97c1576f4941bdcf8a'}
      }).then((res) => {
        this.loginBg = res.data.data.codeUrl
      });
    },
       // 获取网站配置
       getWebConfigInfo() {
      this.$axios({
        method: "get",
        url: this.$api.getWebConfig,
      }).then((res) => {
        this.webConfig = res.data.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #f8f8f8;
  .login-bg {
    position: absolute;
    width: 100vw;
    height: 100vh;
  }
  .login-logo {
    position: absolute;
    top: 40px;
    left: 40px;
    width: auto;
    height: 46px;
    z-index: 10;
  }
}
</style>